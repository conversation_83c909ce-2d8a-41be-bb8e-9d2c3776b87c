"""
Condition Tag Resolvers for Adventure Chess Rule Engine
Processors for conditional and modifier tags

Note: Individual resolvers are imported directly by the base registry
to avoid circular import issues.
"""

# No bulk imports to avoid circular dependencies
# Individual resolvers are imported directly by base.py

__all__ = [
    # Cost modifiers
    "NoTurnCostTagResolver",
    "CostPerDistanceTagResolver",
    "CostPerTargetTagResolver",
    "VariableCostTagResolver",

    # Prerequisites
    "RequiresStartingPositionTagResolver",
    "OncePerTurnTagResolver",
    "RequiresAllyTagResolver",
    "RequiresEnemyTagResolver",

    # Status effects
    "ProtectTagResolver",
    "CannotDieTagResolver",
    "InvisibleTagResolver",
    "TruesightTagResolver",
    "ImmobilizeImmuneTagResolver"
]
