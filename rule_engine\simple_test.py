#!/usr/bin/env python3
"""
Simple test for Adventure Chess Movement System
Basic functionality verification
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """Test that all imports work correctly"""
    print("🧪 Testing imports...")
    
    try:
        from schemas.base import Coordinate, MovementType
        print("  ✅ Schema imports successful")
        
        from core.tester_engine import PieceColor
        print("  ✅ Core imports successful")
        
        from rule_engine.movement.patterns import MovementPatternProcessor
        print("  ✅ Movement pattern processor import successful")
        
        from rule_engine.movement.validator import MovementValidator
        print("  ✅ Movement validator import successful")
        
        from rule_engine.movement.pathfinding import PathfindingEngine
        print("  ✅ Pathfinding engine import successful")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Import failed: {str(e)}")
        return False


def test_coordinate_creation():
    """Test Coordinate object creation"""
    print("\n🧪 Testing Coordinate creation...")
    
    try:
        from schemas.base import Coordinate
        
        # Test valid coordinates
        coord1 = Coordinate(row=3, col=4)
        print(f"  ✅ Created coordinate: ({coord1.row}, {coord1.col})")
        
        coord2 = Coordinate(row=0, col=7)
        print(f"  ✅ Created coordinate: ({coord2.row}, {coord2.col})")
        
        # Test coordinate methods
        coord_list = coord1.to_list()
        print(f"  ✅ Coordinate to list: {coord_list}")
        
        coord3 = Coordinate.from_list([2, 5])
        print(f"  ✅ Coordinate from list: ({coord3.row}, {coord3.col})")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Coordinate test failed: {str(e)}")
        return False


def test_basic_movement_processor():
    """Test basic movement pattern processor"""
    print("\n🧪 Testing basic movement processor...")
    
    try:
        from schemas.base import Coordinate
        from core.tester_engine import PieceColor
        from rule_engine.movement.patterns import MovementPatternProcessor
        
        processor = MovementPatternProcessor()
        print("  ✅ Created MovementPatternProcessor")
        
        # Create simple piece data
        piece_data = {
            'movement': {
                'type': 'orthogonal',
                'distance': 2
            },
            'can_capture': True,
            'owner': PieceColor.WHITE
        }
        
        current_pos = Coordinate(row=3, col=3)
        print(f"  ✅ Created piece data and position: ({current_pos.row}, {current_pos.col})")
        
        # Mock game state
        class MockGameState:
            def get_piece_at(self, pos):
                return []  # No pieces for basic test
        
        game_state = MockGameState()
        
        # Calculate valid moves
        result = processor.calculate_valid_moves(piece_data, current_pos, game_state)
        print(f"  ✅ Calculated valid moves: {len(result.valid_moves)} positions")
        
        if result.valid_moves:
            sample_moves = result.valid_moves[:3]  # Show first 3 moves
            for move in sample_moves:
                print(f"     Sample move: ({move.row}, {move.col})")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Movement processor test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_rule_engine_creation():
    """Test rule engine creation"""
    print("\n🧪 Testing rule engine creation...")
    
    try:
        from rule_engine.engine import RuleEngine
        
        engine = RuleEngine()
        print("  ✅ Created RuleEngine successfully")
        
        # Test basic properties
        print(f"  ✅ Engine has validation_engine: {hasattr(engine, 'validation_engine')}")
        print(f"  ✅ Engine has movement_validator: {hasattr(engine, 'movement_validator')}")
        print(f"  ✅ Engine has event_system: {hasattr(engine, 'event_system')}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Rule engine test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all simple tests"""
    print("🚀 Adventure Chess Movement System - Simple Tests")
    print("=" * 60)
    
    tests = [
        test_imports,
        test_coordinate_creation,
        test_basic_movement_processor,
        test_rule_engine_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"  ❌ Test {test.__name__} crashed: {str(e)}")
    
    print("\n" + "=" * 60)
    print(f"✅ Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All simple tests passed! Movement system is working.")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")


if __name__ == "__main__":
    main()
