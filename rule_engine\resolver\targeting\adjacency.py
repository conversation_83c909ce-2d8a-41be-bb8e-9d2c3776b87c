#!/usr/bin/env python3
"""
Adjacency Tag Resolver for Adventure Chess Rule Engine
Handles adjacency-based targeting and movement
"""

from typing import List, Dict, Any
import logging

from schemas import Ability
from schemas.base import Coordinate
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class AdjacencyTagResolver(BaseTagResolver):
    """
    Resolver for the 'adjacency' tag
    Handles targeting and movement limited to adjacent squares
    """
    
    def _get_tag_name(self) -> str:
        return "adjacency"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.TARGETING
    
    def _get_conflicts(self) -> List[str]:
        return ["teleport", "ranged"]  # Adjacency conflicts with long-range abilities
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        """
        Process adjacency tag and return targeting effects
        
        Args:
            ability: The ability containing the adjacency tag
            context: ActionContext with game state and action details
            
        Returns:
            List of TagEffect objects for adjacency targeting
        """
        effects = []
        
        # Extract adjacency-specific data
        adjacency_data = extract_tag_data(ability, "adjacency")
        
        # Get adjacency parameters
        include_diagonals = adjacency_data.get("include_diagonals", True)
        max_targets = adjacency_data.get("max_targets", 8)
        target_filter = adjacency_data.get("target_filter", "any")
        
        # Calculate valid adjacent positions
        valid_positions = self._get_adjacent_positions(
            context.source_position, 
            include_diagonals,
            context.game_state
        )
        
        # Filter positions based on target requirements
        filtered_positions = self._filter_positions(
            valid_positions, 
            target_filter, 
            context
        )
        
        # Create targeting effect
        targeting_effect = TagEffect(
            effect_type="targeting_constraint",
            priority=200,  # Very high priority for targeting constraints
            immediate=True,
            parameters={
                "constraint_type": "adjacency",
                "valid_positions": filtered_positions[:max_targets],
                "include_diagonals": include_diagonals,
                "max_targets": max_targets,
                "target_filter": target_filter
            }
        )
        
        effects.append(targeting_effect)
        
        return effects
    
    def _get_adjacent_positions(self, center: Coordinate, include_diagonals: bool, 
                              game_state: Any) -> List[Coordinate]:
        """
        Get all valid adjacent positions
        
        Args:
            center: Center position
            include_diagonals: Whether to include diagonal adjacency
            game_state: Current game state
            
        Returns:
            List of valid adjacent coordinates
        """
        adjacent_positions = []
        
        if include_diagonals:
            # All 8 adjacent squares
            directions = [
                (-1, -1), (-1, 0), (-1, 1),
                (0, -1),           (0, 1),
                (1, -1),  (1, 0),  (1, 1)
            ]
        else:
            # Only orthogonal adjacency (4 squares)
            directions = [(-1, 0), (0, -1), (0, 1), (1, 0)]
        
        for dr, dc in directions:
            new_row = center.row + dr
            new_col = center.col + dc
            
            # Check bounds
            if 0 <= new_row < 8 and 0 <= new_col < 8:
                position = Coordinate(row=new_row, col=new_col)
                adjacent_positions.append(position)
        
        return adjacent_positions
    
    def _filter_positions(self, positions: List[Coordinate], target_filter: str, 
                         context: Any) -> List[Coordinate]:
        """
        Filter positions based on target requirements
        
        Args:
            positions: List of positions to filter
            target_filter: Type of targets to allow
            context: ActionContext
            
        Returns:
            Filtered list of positions
        """
        filtered = []
        
        for position in positions:
            pieces_at_position = context.game_state.get_piece_at(position)
            
            if target_filter == "empty":
                # Only empty positions
                if not pieces_at_position:
                    filtered.append(position)
            
            elif target_filter == "occupied":
                # Only occupied positions
                if pieces_at_position:
                    filtered.append(position)
            
            elif target_filter == "enemy":
                # Only positions with enemy pieces
                for piece in pieces_at_position:
                    if piece.owner != context.current_player:
                        filtered.append(position)
                        break
            
            elif target_filter == "friendly":
                # Only positions with friendly pieces
                for piece in pieces_at_position:
                    if piece.owner == context.current_player:
                        filtered.append(position)
                        break
            
            else:  # "any"
                # All valid positions
                filtered.append(position)
        
        return filtered
    
    def get_range_modifier(self, base_range: int, ability: Ability, context: Any) -> int:
        """
        Get range modifications for adjacency
        
        Args:
            base_range: Base range
            ability: The ability being used
            context: ActionContext
            
        Returns:
            Modified range (always 1 for adjacency)
        """
        # Adjacency always limits range to 1
        return 1
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        """
        Get targeting requirements for adjacency abilities
        
        Args:
            ability: The ability being analyzed
            
        Returns:
            Dictionary with targeting requirements
        """
        adjacency_data = extract_tag_data(ability, "adjacency")
        
        return {
            "requires_target": True,
            "target_type": "position",
            "min_targets": 1,
            "max_targets": adjacency_data.get("max_targets", 8),
            "range_limit": 1,
            "adjacency_only": True,
            "include_diagonals": adjacency_data.get("include_diagonals", True),
            "line_of_sight": False  # Adjacency doesn't require LoS
        }
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> tuple[bool, str]:
        """
        Validate prerequisites for adjacency tag
        
        Args:
            ability: The ability being validated
            context: ActionContext
            
        Returns:
            (is_valid, error_message)
        """
        # Call parent validation first
        is_valid, message = super().validate_prerequisites(ability, context)
        if not is_valid:
            return is_valid, message
        
        # Check adjacency-specific prerequisites
        adjacency_data = extract_tag_data(ability, "adjacency")
        
        # Check if there are any valid adjacent positions
        valid_positions = self._get_adjacent_positions(
            context.source_position,
            adjacency_data.get("include_diagonals", True),
            context.game_state
        )
        
        filtered_positions = self._filter_positions(
            valid_positions,
            adjacency_data.get("target_filter", "any"),
            context
        )
        
        if not filtered_positions:
            return False, "No valid adjacent targets available"
        
        # Validate target is actually adjacent
        if context.targeting.primary_target:
            target = context.targeting.primary_target
            source = context.source_position
            
            distance = max(abs(target.row - source.row), abs(target.col - source.col))
            
            if distance > 1:
                return False, "Target is not adjacent"
            
            # Check diagonal restriction
            if not adjacency_data.get("include_diagonals", True):
                # Only orthogonal adjacency allowed
                if abs(target.row - source.row) > 0 and abs(target.col - source.col) > 0:
                    return False, "Diagonal adjacency not allowed"
        
        return True, "Prerequisites satisfied"
    
    def calculate_cost_modifier(self, base_cost: int, ability: Ability, context: Any) -> int:
        """
        Calculate cost modifications for adjacency
        
        Args:
            base_cost: Base ability cost
            ability: The ability being used
            context: ActionContext
            
        Returns:
            Modified cost (usually reduced for adjacency)
        """
        adjacency_data = extract_tag_data(ability, "adjacency")
        
        # Adjacency abilities are often cheaper due to limited range
        cost_reduction = adjacency_data.get("cost_reduction", 0)
        
        return max(0, base_cost - cost_reduction)
