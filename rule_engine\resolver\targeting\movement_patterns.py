#!/usr/bin/env python3
"""
Movement Pattern Tag Resolvers for Adventure Chess Rule Engine
Handles orthogonal, diagonal, any direction, L-shape, and teleport targeting
"""

from typing import List, Dict, Any
from schemas import Ability
from schemas.base import Coordinate
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class OrthogonalTagResolver(BaseTagResolver):
    """Resolver for orthogonal movement/targeting"""
    
    def _get_tag_name(self) -> str:
        return "orthogonal"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.TARGETING
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(
            effect_type="targeting_constraint",
            priority=190,
            immediate=True,
            parameters={
                "constraint_type": "orthogonal",
                "directions": [(0, 1), (0, -1), (1, 0), (-1, 0)]
            }
        )]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        return {
            "requires_target": True,
            "movement_type": "orthogonal",
            "range_required": True
        }


class DiagonalTagResolver(BaseTagResolver):
    """Resolver for diagonal movement/targeting"""
    
    def _get_tag_name(self) -> str:
        return "diagonal"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.TARGETING
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(
            effect_type="targeting_constraint",
            priority=190,
            immediate=True,
            parameters={
                "constraint_type": "diagonal",
                "directions": [(1, 1), (1, -1), (-1, 1), (-1, -1)]
            }
        )]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        return {
            "requires_target": True,
            "movement_type": "diagonal",
            "range_required": True
        }


class AnyDirectionTagResolver(BaseTagResolver):
    """Resolver for any direction movement/targeting"""
    
    def _get_tag_name(self) -> str:
        return "any"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.TARGETING
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(
            effect_type="targeting_constraint",
            priority=190,
            immediate=True,
            parameters={
                "constraint_type": "any",
                "directions": [
                    (0, 1), (0, -1), (1, 0), (-1, 0),  # orthogonal
                    (1, 1), (1, -1), (-1, 1), (-1, -1)  # diagonal
                ]
            }
        )]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        return {
            "requires_target": True,
            "movement_type": "any",
            "range_required": True
        }


class LShapeTagResolver(BaseTagResolver):
    """Resolver for L-shape (knight) movement/targeting"""
    
    def _get_tag_name(self) -> str:
        return "lShape"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.TARGETING
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(
            effect_type="targeting_constraint",
            priority=190,
            immediate=True,
            parameters={
                "constraint_type": "lShape",
                "offsets": [
                    (2, 1), (2, -1), (-2, 1), (-2, -1),
                    (1, 2), (1, -2), (-1, 2), (-1, -2)
                ]
            }
        )]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        return {
            "requires_target": True,
            "movement_type": "lShape",
            "range_required": False  # L-shape has fixed positions
        }


class TeleportTagResolver(BaseTagResolver):
    """Resolver for teleport targeting"""
    
    def _get_tag_name(self) -> str:
        return "teleport"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.TARGETING
    
    def _get_conflicts(self) -> List[str]:
        return ["adjacency", "orthogonal", "diagonal", "lShape"]
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        teleport_data = extract_tag_data(ability, "teleport")
        return [TagEffect(
            effect_type="targeting_constraint",
            priority=195,
            immediate=True,
            parameters={
                "constraint_type": "teleport",
                "ignore_obstacles": teleport_data.get("ignore_obstacles", True),
                "ignore_pieces": teleport_data.get("ignore_pieces", True),
                "requires_los": teleport_data.get("requires_los", False)
            }
        )]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        teleport_data = extract_tag_data(ability, "teleport")
        return {
            "requires_target": True,
            "movement_type": "teleport",
            "range_required": True,
            "line_of_sight": teleport_data.get("requires_los", False),
            "ignore_obstacles": teleport_data.get("ignore_obstacles", True)
        }
