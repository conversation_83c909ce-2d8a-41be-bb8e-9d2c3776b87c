"""
Data migration utility for Adventure Chess
Migrates existing JSON files to be compatible with new Pydantic models
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import PIECES_DIR, ABILITIES_DIR
from schemas import (
    pydantic_data_manager,
    DataMigrationManager,
    CompatibilityLayer
)

logger = logging.getLogger(__name__)


class DataMigrator:
    """
    Handles migration of existing data files to Pydantic-compatible format
    """
    
    def __init__(self):
        self.migration_log: List[str] = []
        self.errors: List[str] = []
        self.warnings: List[str] = []
    
    def migrate_all_data(self, backup_original: bool = True) -> Tuple[bool, Dict[str, Any]]:
        """
        Migrate all pieces and abilities to Pydantic format
        Returns: (success, migration_report)
        """
        report = {
            'pieces_migrated': 0,
            'abilities_migrated': 0,
            'pieces_failed': 0,
            'abilities_failed': 0,
            'warnings': [],
            'errors': []
        }
        
        logger.info("Starting data migration to Pydantic format...")
        
        # Migrate pieces
        pieces_success, pieces_report = self.migrate_pieces(backup_original)
        report.update({
            'pieces_migrated': pieces_report['migrated'],
            'pieces_failed': pieces_report['failed']
        })
        
        # Migrate abilities
        abilities_success, abilities_report = self.migrate_abilities(backup_original)
        report.update({
            'abilities_migrated': abilities_report['migrated'],
            'abilities_failed': abilities_report['failed']
        })
        
        # Combine warnings and errors
        report['warnings'].extend(self.warnings)
        report['errors'].extend(self.errors)
        
        overall_success = pieces_success and abilities_success
        
        if overall_success:
            logger.info(f"Migration completed successfully: {report['pieces_migrated']} pieces, {report['abilities_migrated']} abilities")
        else:
            logger.error(f"Migration completed with errors: {len(report['errors'])} errors, {len(report['warnings'])} warnings")
        
        return overall_success, report
    
    def migrate_pieces(self, backup_original: bool = True) -> Tuple[bool, Dict[str, int]]:
        """
        Migrate all piece files to Pydantic format
        Returns: (success, report)
        """
        report = {'migrated': 0, 'failed': 0}
        
        pieces_dir = Path(PIECES_DIR)
        if not pieces_dir.exists():
            self.errors.append("Pieces directory not found")
            return False, report
        
        # Create backup directory if needed
        if backup_original:
            backup_dir = pieces_dir / "backup_pre_pydantic"
            backup_dir.mkdir(exist_ok=True)
        
        # Process each piece file
        for piece_file in pieces_dir.glob("*.json"):
            if piece_file.parent.name == "backup_pre_pydantic":
                continue  # Skip backup files
            
            try:
                # Load original data
                with open(piece_file, 'r', encoding='utf-8') as f:
                    original_data = json.load(f)
                
                # Backup original if requested
                if backup_original:
                    backup_file = backup_dir / piece_file.name
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        json.dump(original_data, f, indent=2, ensure_ascii=False)
                
                # Migrate to Pydantic model
                piece, warnings = DataMigrationManager.migrate_piece_dict_to_model(original_data)
                
                if piece is None:
                    self.errors.append(f"Failed to migrate piece: {piece_file.name}")
                    report['failed'] += 1
                    continue
                
                # Add any warnings
                for warning in warnings:
                    self.warnings.append(f"{piece_file.name}: {warning}")
                
                # Save migrated data
                migrated_data = piece.to_legacy_dict()
                with open(piece_file, 'w', encoding='utf-8') as f:
                    json.dump(migrated_data, f, indent=2, ensure_ascii=False)
                
                report['migrated'] += 1
                self.migration_log.append(f"Migrated piece: {piece.name}")
                
            except Exception as e:
                self.errors.append(f"Error migrating {piece_file.name}: {e}")
                report['failed'] += 1
        
        return report['failed'] == 0, report
    
    def migrate_abilities(self, backup_original: bool = True) -> Tuple[bool, Dict[str, int]]:
        """
        Migrate all ability files to Pydantic format
        Returns: (success, report)
        """
        report = {'migrated': 0, 'failed': 0}
        
        abilities_dir = Path(ABILITIES_DIR)
        if not abilities_dir.exists():
            self.errors.append("Abilities directory not found")
            return False, report
        
        # Create backup directory if needed
        if backup_original:
            backup_dir = abilities_dir / "backup_pre_pydantic"
            backup_dir.mkdir(exist_ok=True)
        
        # Process each ability file
        for ability_file in abilities_dir.glob("*.json"):
            if ability_file.parent.name == "backup_pre_pydantic":
                continue  # Skip backup files
            
            try:
                # Load original data
                with open(ability_file, 'r', encoding='utf-8') as f:
                    original_data = json.load(f)
                
                # Backup original if requested
                if backup_original:
                    backup_file = backup_dir / ability_file.name
                    with open(backup_file, 'w', encoding='utf-8') as f:
                        json.dump(original_data, f, indent=2, ensure_ascii=False)
                
                # Migrate to Pydantic model
                ability, warnings = DataMigrationManager.migrate_ability_dict_to_model(original_data)
                
                if ability is None:
                    self.errors.append(f"Failed to migrate ability: {ability_file.name}")
                    report['failed'] += 1
                    continue
                
                # Add any warnings
                for warning in warnings:
                    self.warnings.append(f"{ability_file.name}: {warning}")
                
                # Validate all tag data
                validation_errors = ability.validate_all_tags()
                if validation_errors:
                    for tag, error in validation_errors.items():
                        self.warnings.append(f"{ability_file.name} tag '{tag}': {error}")
                
                # Save migrated data
                migrated_data = ability.to_legacy_dict()
                with open(ability_file, 'w', encoding='utf-8') as f:
                    json.dump(migrated_data, f, indent=2, ensure_ascii=False)
                
                report['migrated'] += 1
                self.migration_log.append(f"Migrated ability: {ability.name}")
                
            except Exception as e:
                self.errors.append(f"Error migrating {ability_file.name}: {e}")
                report['failed'] += 1
        
        return report['failed'] == 0, report
    
    def validate_migrated_data(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate all migrated data can be loaded correctly
        Returns: (success, validation_report)
        """
        report = {
            'pieces_valid': 0,
            'abilities_valid': 0,
            'pieces_invalid': 0,
            'abilities_invalid': 0,
            'validation_errors': []
        }
        
        logger.info("Validating migrated data...")
        
        # Validate pieces
        pieces_dir = Path(PIECES_DIR)
        if pieces_dir.exists():
            for piece_file in pieces_dir.glob("*.json"):
                if piece_file.parent.name == "backup_pre_pydantic":
                    continue
                
                try:
                    piece, error = pydantic_data_manager.load_piece(piece_file.name)
                    if piece is None:
                        report['pieces_invalid'] += 1
                        report['validation_errors'].append(f"Piece {piece_file.name}: {error}")
                    else:
                        report['pieces_valid'] += 1
                except Exception as e:
                    report['pieces_invalid'] += 1
                    report['validation_errors'].append(f"Piece {piece_file.name}: {e}")
        
        # Validate abilities
        abilities_dir = Path(ABILITIES_DIR)
        if abilities_dir.exists():
            for ability_file in abilities_dir.glob("*.json"):
                if ability_file.parent.name == "backup_pre_pydantic":
                    continue
                
                try:
                    ability, error = pydantic_data_manager.load_ability(ability_file.name)
                    if ability is None:
                        report['abilities_invalid'] += 1
                        report['validation_errors'].append(f"Ability {ability_file.name}: {error}")
                    else:
                        report['abilities_valid'] += 1
                except Exception as e:
                    report['abilities_invalid'] += 1
                    report['validation_errors'].append(f"Ability {ability_file.name}: {e}")
        
        success = report['pieces_invalid'] == 0 and report['abilities_invalid'] == 0
        
        if success:
            logger.info(f"Validation successful: {report['pieces_valid']} pieces, {report['abilities_valid']} abilities")
        else:
            logger.error(f"Validation failed: {len(report['validation_errors'])} errors")
        
        return success, report
    
    def get_migration_summary(self) -> str:
        """Get a human-readable summary of the migration"""
        summary = []
        summary.append("=== Data Migration Summary ===")
        summary.append(f"Migration log entries: {len(self.migration_log)}")
        summary.append(f"Warnings: {len(self.warnings)}")
        summary.append(f"Errors: {len(self.errors)}")
        summary.append("")
        
        if self.migration_log:
            summary.append("Successfully migrated:")
            for entry in self.migration_log:
                summary.append(f"  ✓ {entry}")
            summary.append("")
        
        if self.warnings:
            summary.append("Warnings:")
            for warning in self.warnings:
                summary.append(f"  ⚠️ {warning}")
            summary.append("")
        
        if self.errors:
            summary.append("Errors:")
            for error in self.errors:
                summary.append(f"  ❌ {error}")
        
        return "\n".join(summary)


def run_migration(backup_original: bool = True, validate_after: bool = True) -> bool:
    """
    Run the complete data migration process
    Returns: True if successful, False otherwise
    """
    migrator = DataMigrator()
    
    try:
        # Run migration
        success, migration_report = migrator.migrate_all_data(backup_original)
        
        if not success:
            print("❌ Migration failed!")
            print(migrator.get_migration_summary())
            return False
        
        # Validate migrated data if requested
        if validate_after:
            validation_success, validation_report = migrator.validate_migrated_data()
            if not validation_success:
                print("❌ Migration validation failed!")
                print(migrator.get_migration_summary())
                return False
        
        print("✅ Migration completed successfully!")
        print(migrator.get_migration_summary())
        return True
        
    except Exception as e:
        print(f"❌ Migration failed with exception: {e}")
        return False


if __name__ == "__main__":
    # Run migration when script is executed directly
    import sys
    
    backup = "--no-backup" not in sys.argv
    validate = "--no-validate" not in sys.argv
    
    print("Starting Adventure Chess data migration to Pydantic format...")
    print(f"Backup original files: {backup}")
    print(f"Validate after migration: {validate}")
    print()
    
    success = run_migration(backup_original=backup, validate_after=validate)
    
    if success:
        print("\n🎉 Migration completed successfully!")
        print("Your data has been migrated to use Pydantic models for better validation and consistency.")
    else:
        print("\n💥 Migration failed!")
        print("Please check the error messages above and fix any issues before retrying.")
    
    sys.exit(0 if success else 1)
