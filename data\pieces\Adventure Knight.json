{"version": "1.0.0", "name": "Adventure Knight", "description": "Can carry a pawn and share space with friendly pieces", "role": "Commander", "canCastle": false, "movement": {"type": "lShape", "pattern": [[0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 3, 0, 3, 0, 0, 0], [0, 3, 0, 0, 0, 3, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 3, 0, 0, 0, 3, 0, 0], [0, 0, 3, 0, 3, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0, 0, 0]], "piecePosition": [3, 3]}, "canCapture": true, "colorDirectional": false, "abilities": [], "trackStartingPosition": false, "blackIcon": "black-knight.png", "whiteIcon": "white-knight.png", "enableRecharge": false, "maxPoints": 0, "startingPoints": 0, "rechargeType": "turn<PERSON><PERSON><PERSON><PERSON>", "turnPoints": 1, "committedRechargeTurns": 3, "adjacencyDistance": 1, "promotions": [], "secondaryPromotions": []}