#!/usr/bin/env python3
"""
Test Ability Tester Data Flow
Verify that all ability editor fields are properly saved and loaded in ability testing
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ability_tester_data_flow():
    """Test that ability testing properly loads all ability editor fields"""
    
    print("=== ABILITY TESTER DATA FLOW TEST ===")
    
    # Test 1: Create a test ability with various fields set
    print("\n1. Creating Test Ability with Multiple Fields...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # Create minimal QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from editors.ability_editor import AbilityEditorWindow
        from utils.simple_bridge import simple_bridge
        
        # Create ability editor instance
        editor = AbilityEditorWindow()
        
        # Set comprehensive test data
        editor.name_edit.setText("TestDataFlowAbility")
        editor.description_edit.setPlainText("Test ability for data flow verification")
        editor.cost_spin.setValue(3)
        editor.activation_combo.setCurrentText("manual")
        
        # Select some tags if available
        if hasattr(editor, 'tag_groups') and editor.tag_groups:
            # Select first few available tags
            tag_names = list(editor.tag_groups.keys())[:3]
            for tag_name in tag_names:
                if tag_name in editor.tag_groups:
                    editor.tag_groups[tag_name].setChecked(True)
        
        # Set range pattern if available
        if hasattr(editor, 'range_pattern'):
            # Create a simple test pattern
            test_pattern = [[0 for _ in range(8)] for _ in range(8)]
            test_pattern[3][3] = 1  # Center piece
            test_pattern[3][4] = 3  # Right of piece
            test_pattern[4][3] = 3  # Below piece
            editor.range_pattern = test_pattern
        
        # Collect the data
        ability_data = simple_bridge.get_ability_data_from_ui(editor)
        
        # Verify basic fields are collected
        expected_fields = {
            'name': 'TestDataFlowAbility',
            'cost': 3,
            'activationMode': 'manual'
        }
        
        all_fields_correct = True
        for field, expected_value in expected_fields.items():
            actual_value = ability_data.get(field)
            if actual_value != expected_value:
                print(f"❌ Field {field}: expected {expected_value}, got {actual_value}")
                all_fields_correct = False
        
        # Check that tags are collected
        if 'tags' in ability_data and len(ability_data['tags']) > 0:
            print(f"✓ Tags collected: {ability_data['tags']}")
        else:
            print("⚠️ No tags collected (may be normal if no tags selected)")
        
        if all_fields_correct:
            print("✓ All ability editor fields properly collected")
        else:
            return False
            
    except Exception as e:
        print(f"❌ Test ability creation failed: {e}")
        return False
    
    # Test 2: Save the ability and verify it can be loaded
    print("\n2. Testing Save and Load Cycle...")
    
    try:
        # Save the ability
        success, error = simple_bridge.save_ability_from_ui(editor, "TestDataFlowAbility")
        
        if not success:
            print(f"❌ Save failed: {error}")
            return False
        
        print("✓ Test ability saved successfully")
        
        # Load the ability back
        loaded_data, load_error = simple_bridge.load_ability_for_ui("TestDataFlowAbility")
        
        if load_error:
            print(f"❌ Load failed: {load_error}")
            return False
        
        # Verify critical fields are preserved
        critical_fields = ['name', 'cost', 'activationMode', 'tags']
        for field in critical_fields:
            if loaded_data.get(field) != ability_data.get(field):
                print(f"❌ Field {field} not preserved: {ability_data.get(field)} → {loaded_data.get(field)}")
                return False
        
        print("✓ All critical fields preserved in save/load cycle")
        
    except Exception as e:
        print(f"❌ Save/load test failed: {e}")
        return False
    
    # Test 3: Test ability usage in piece tester/tester engine
    print("\n3. Testing Ability in Tester Engine...")
    
    try:
        from core.tester_engine import tester_engine
        
        # Reset the tester engine
        tester_engine.reset_board()
        
        # Check if our test ability is available
        available_abilities = simple_bridge.list_abilities()
        
        if "TestDataFlowAbility" not in available_abilities:
            print("❌ Test ability not found in available abilities")
            return False
        
        # Try to load the ability through the tester engine path
        test_ability_data, error = simple_bridge.load_ability_for_ui("TestDataFlowAbility")
        
        if error:
            print(f"❌ Tester engine couldn't load ability: {error}")
            return False
        
        # Verify the ability has the expected structure
        required_fields = ['name', 'cost', 'activationMode']
        for field in required_fields:
            if field not in test_ability_data:
                print(f"❌ Missing required field in tester engine: {field}")
                return False
        
        print("✓ Ability properly accessible to tester engine")
        
    except Exception as e:
        print(f"❌ Tester engine integration test failed: {e}")
        return False
    
    # Test 4: Test UI population from loaded data
    print("\n4. Testing UI Population from Loaded Data...")
    
    try:
        # Create a new editor instance
        editor2 = AbilityEditorWindow()
        
        # Load the saved data into the new editor
        editor2.set_widget_values_from_data(loaded_data)
        
        # Verify UI state matches original
        ui_checks = [
            (editor2.name_edit.text(), "TestDataFlowAbility", "name"),
            (editor2.cost_spin.value(), 3, "cost"),
            (editor2.activation_combo.currentText(), "manual", "activation_mode")
        ]
        
        all_ui_correct = True
        for actual, expected, field_name in ui_checks:
            if actual != expected:
                print(f"❌ UI field {field_name}: expected {expected}, got {actual}")
                all_ui_correct = False
        
        # Check tag population
        if hasattr(editor2, 'tag_groups') and 'tags' in loaded_data:
            loaded_tags = loaded_data['tags']
            for tag_name in loaded_tags:
                if tag_name in editor2.tag_groups:
                    if not editor2.tag_groups[tag_name].isChecked():
                        print(f"❌ Tag {tag_name} not properly loaded")
                        all_ui_correct = False
        
        if all_ui_correct:
            print("✓ UI properly populated from loaded data")
        else:
            return False
            
    except Exception as e:
        print(f"❌ UI population test failed: {e}")
        return False
    
    # Test 5: Test ability in piece ability manager
    print("\n5. Testing Ability in Piece Ability Manager...")
    
    try:
        from PyQt6.QtCore import Qt
        from dialogs.piece_ability_manager import PieceAbilityManagerDialog

        # Create piece ability manager instance
        manager = PieceAbilityManagerDialog()
        
        # Refresh ability lists to include our test ability
        manager.refresh_ability_lists()
        
        # Check if our ability appears in the available list
        found_ability = False
        for i in range(manager.available_list.count()):
            item = manager.available_list.item(i)
            ability_name = item.data(Qt.ItemDataRole.UserRole)
            if ability_name == "TestDataFlowAbility":
                found_ability = True
                break
        
        if found_ability:
            print("✓ Ability properly appears in piece ability manager")
        else:
            print("❌ Ability not found in piece ability manager")
            return False
        
    except Exception as e:
        print(f"❌ Piece ability manager test failed: {e}")
        return False
    
    # Test 6: Clean up test file
    print("\n6. Cleaning Up...")
    
    try:
        from utils.direct_data_manager import DirectDataManager
        success, error = DirectDataManager.delete_ability("TestDataFlowAbility")
        if success:
            print("✓ Test ability cleaned up")
        else:
            print(f"⚠️ Cleanup warning: {error}")
            
    except Exception as e:
        print(f"⚠️ Cleanup failed: {e}")
    
    print("\n✅ ALL ABILITY TESTER DATA FLOW TESTS PASSED!")
    print("\nData Flow Summary:")
    print("  ✓ Ability Editor → SimpleBridge → JSON file")
    print("  ✓ JSON file → SimpleBridge → Tester Engine")
    print("  ✓ JSON file → SimpleBridge → UI Population")
    print("  ✓ JSON file → SimpleBridge → Piece Ability Manager")
    print("  ✓ All fields including tags and patterns properly handled")
    print("  ✓ Complete data integrity maintained across all systems")
    
    return True

if __name__ == "__main__":
    success = test_ability_tester_data_flow()
    sys.exit(0 if success else 1)
