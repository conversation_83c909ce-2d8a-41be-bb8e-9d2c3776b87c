#!/usr/bin/env python3
"""
Test Complete Data Flow with SimpleBridge
Tests the unified data path: UI → SimpleBridge → JSON → Piece Tester
"""

import sys
import os
import tempfile
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_simple_bridge_data_flow():
    """Test complete data flow using SimpleBridge"""
    
    print("=== SIMPLE BRIDGE DATA FLOW TEST ===")
    
    # Test 1: Test SimpleBridge piece operations
    print("\n1. Testing SimpleBridge Piece Operations...")
    
    try:
        from utils.simple_bridge import simple_bridge
        
        # Test piece listing
        pieces = simple_bridge.list_pieces()
        print(f"✓ Found {len(pieces)} pieces")
        
        if pieces:
            # Test loading a piece
            test_piece = pieces[0]
            piece_data, error = simple_bridge.load_piece_for_ui(test_piece)
            if piece_data and not error:
                print(f"✓ Loaded piece: {piece_data.get('name', test_piece)}")
                print(f"  - Role: {piece_data.get('role', 'Unknown')}")
                print(f"  - Movement: {piece_data.get('movement', {}).get('type', 'Unknown')}")
                print(f"  - Abilities: {len(piece_data.get('abilities', []))}")
            else:
                print(f"❌ Failed to load piece {test_piece}: {error}")
                return False
        else:
            print("⚠️ No pieces found to test")
            
    except Exception as e:
        print(f"❌ SimpleBridge piece test failed: {e}")
        return False
    
    # Test 2: Test SimpleBridge ability operations
    print("\n2. Testing SimpleBridge Ability Operations...")
    
    try:
        # Test ability listing
        abilities = simple_bridge.list_abilities()
        print(f"✓ Found {len(abilities)} abilities")
        
        if abilities:
            # Test loading an ability
            test_ability = abilities[0]
            ability_data, error = simple_bridge.load_ability_for_ui(test_ability)
            if ability_data and not error:
                print(f"✓ Loaded ability: {ability_data.get('name', test_ability)}")
                print(f"  - Cost: {ability_data.get('cost', 0)}")
                print(f"  - Tags: {len(ability_data.get('tags', []))}")
                print(f"  - Activation: {ability_data.get('activationMode', 'auto')}")
            else:
                print(f"❌ Failed to load ability {test_ability}: {error}")
                return False
        else:
            print("⚠️ No abilities found to test")
            
    except Exception as e:
        print(f"❌ SimpleBridge ability test failed: {e}")
        return False
    
    # Test 3: Test piece tester integration (without GUI)
    print("\n3. Testing Piece Tester Integration...")

    try:
        # Test the load_available_pieces method directly without creating GUI
        from PyQt6.QtWidgets import QApplication
        import sys

        # Create minimal QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        from editors.piece_tester import PieceTester

        # Create piece tester instance
        tester = PieceTester()

        # Test loading available pieces
        available_pieces = tester.load_available_pieces()
        print(f"✓ Piece tester loaded {len(available_pieces)} pieces")

        if available_pieces:
            # Test that piece data is properly formatted
            first_piece = list(available_pieces.values())[0]
            required_fields = ['name', 'role', 'movement', 'canCapture']
            missing_fields = [field for field in required_fields if field not in first_piece]

            if not missing_fields:
                print("✓ Piece data contains all required fields")
            else:
                print(f"❌ Missing required fields: {missing_fields}")
                return False

    except Exception as e:
        print(f"❌ Piece tester integration test failed: {e}")
        return False
    
    # Test 4: Test tester engine integration
    print("\n4. Testing Tester Engine Integration...")
    
    try:
        from core.tester_engine import tester_engine, PieceColor, BoardPosition
        
        # Reset engine
        tester_engine.reset_board()
        
        if pieces:
            # Test placing a piece
            test_piece_name = pieces[0]
            position = BoardPosition(3, 3)  # Center of board
            
            success, message = tester_engine.place_piece(test_piece_name, PieceColor.WHITE, position)
            if success:
                print(f"✓ Successfully placed piece: {message}")
                
                # Test getting piece at position
                placed_piece = tester_engine.get_piece_at(position)
                if placed_piece:
                    print(f"✓ Retrieved placed piece: {placed_piece.piece_name}")
                else:
                    print("❌ Could not retrieve placed piece")
                    return False
            else:
                print(f"❌ Failed to place piece: {message}")
                return False
        
    except Exception as e:
        print(f"❌ Tester engine integration test failed: {e}")
        return False
    
    # Test 5: Test data consistency
    print("\n5. Testing Data Consistency...")
    
    try:
        # Compare data loaded by different components
        if pieces:
            test_piece = pieces[0]
            
            # Load via SimpleBridge
            bridge_data, _ = simple_bridge.load_piece_for_ui(test_piece)
            
            # Load via piece tester (reuse existing app)
            tester = PieceTester()
            tester_pieces = tester.load_available_pieces()
            tester_data = None
            for name, data in tester_pieces.items():
                if data.get('name') == bridge_data.get('name'):
                    tester_data = data
                    break
            
            if tester_data:
                # Compare key fields
                key_fields = ['name', 'role', 'canCapture']
                consistent = True
                for field in key_fields:
                    if bridge_data.get(field) != tester_data.get(field):
                        print(f"❌ Inconsistent {field}: {bridge_data.get(field)} vs {tester_data.get(field)}")
                        consistent = False
                
                if consistent:
                    print("✓ Data consistency verified across components")
                else:
                    return False
            else:
                print("⚠️ Could not find matching piece data for consistency check")
        
    except Exception as e:
        print(f"❌ Data consistency test failed: {e}")
        return False
    
    print("\n✅ ALL TESTS PASSED - SimpleBridge data flow is working correctly!")
    print("\nData Flow Summary:")
    print("  UI Components → SimpleBridge → DirectDataManager → JSON Files")
    print("  JSON Files → SimpleBridge → Piece Tester → Tester Engine")
    print("  ✓ Unified data path established")
    print("  ✓ Legacy PydanticBridge usage eliminated")
    print("  ✓ All components using SimpleBridge")
    
    return True

if __name__ == "__main__":
    success = test_simple_bridge_data_flow()
    sys.exit(0 if success else 1)
