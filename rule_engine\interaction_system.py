#!/usr/bin/env python3
"""
Complex Interaction System for Adventure Chess Rule Engine
Handles multi-piece interactions, chain reactions, and complex ability combinations
"""

from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from enum import Enum
import logging
import uuid
from datetime import datetime

from schemas.base import Coordinate
from core.tester_engine import PieceColor


class InteractionType(Enum):
    """Types of piece interactions"""
    ABILITY_CHAIN = "ability_chain"
    REACTION_TRIGGER = "reaction_trigger"
    STATUS_PROPAGATION = "status_propagation"
    AREA_EFFECT = "area_effect"
    CONDITIONAL_ACTIVATION = "conditional_activation"
    SYNERGY_BONUS = "synergy_bonus"


class InteractionPriority(Enum):
    """Priority levels for interaction processing"""
    IMMEDIATE = 1000
    HIGH = 800
    NORMAL = 500
    LOW = 200
    CLEANUP = 100


@dataclass
class InteractionContext:
    """Context information for an interaction"""
    interaction_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    interaction_type: InteractionType = InteractionType.ABILITY_CHAIN
    priority: InteractionPriority = InteractionPriority.NORMAL
    
    # Source information
    source_piece_id: str = ""
    source_ability: str = ""
    source_position: Optional[Coordinate] = None
    
    # Target information
    target_piece_ids: List[str] = field(default_factory=list)
    target_positions: List[Coordinate] = field(default_factory=list)
    
    # Interaction parameters
    parameters: Dict[str, Any] = field(default_factory=dict)
    conditions: List[str] = field(default_factory=list)
    
    # State
    processed: bool = False
    timestamp: datetime = field(default_factory=datetime.now)
    
    def add_target_piece(self, piece_id: str):
        """Add a target piece to the interaction"""
        if piece_id not in self.target_piece_ids:
            self.target_piece_ids.append(piece_id)
    
    def add_target_position(self, position: Coordinate):
        """Add a target position to the interaction"""
        if position not in self.target_positions:
            self.target_positions.append(position)


@dataclass
class InteractionResult:
    """Result of processing an interaction"""
    success: bool = False
    effects_applied: List[Dict[str, Any]] = field(default_factory=list)
    triggered_interactions: List[InteractionContext] = field(default_factory=list)
    error_message: Optional[str] = None
    
    def add_effect(self, effect: Dict[str, Any]):
        """Add an effect to the result"""
        self.effects_applied.append(effect)
    
    def add_triggered_interaction(self, interaction: InteractionContext):
        """Add a triggered interaction to the result"""
        self.triggered_interactions.append(interaction)


class InteractionProcessor:
    """
    Processes complex interactions between pieces and abilities
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Interaction queue
        self.pending_interactions: List[InteractionContext] = []
        self.processing_interactions: Set[str] = set()
        
        # Interaction handlers
        self.interaction_handlers: Dict[InteractionType, callable] = {}
        
        # Chain tracking
        self.interaction_chains: Dict[str, List[str]] = {}
        self.max_chain_depth = 10
        
        # Initialize handlers
        self._initialize_handlers()
        
        self.logger.info("InteractionProcessor initialized")
    
    def queue_interaction(self, interaction: InteractionContext) -> str:
        """
        Queue an interaction for processing
        
        Args:
            interaction: InteractionContext to queue
            
        Returns:
            Interaction ID
        """
        # Check for duplicate interactions
        if self._is_duplicate_interaction(interaction):
            self.logger.debug(f"Skipping duplicate interaction: {interaction.interaction_id}")
            return interaction.interaction_id
        
        # Add to queue
        self.pending_interactions.append(interaction)
        
        # Sort by priority
        self.pending_interactions.sort(key=lambda x: x.priority.value, reverse=True)
        
        self.logger.debug(f"Queued interaction: {interaction.interaction_type.value}")
        return interaction.interaction_id
    
    def process_interactions(self, game_state: Any) -> List[InteractionResult]:
        """
        Process all pending interactions
        
        Args:
            game_state: Current game state
            
        Returns:
            List of interaction results
        """
        results = []
        processed_count = 0
        
        while self.pending_interactions and processed_count < 100:  # Safety limit
            interaction = self.pending_interactions.pop(0)
            
            # Skip if already processing (circular reference protection)
            if interaction.interaction_id in self.processing_interactions:
                continue
            
            # Process the interaction
            result = self._process_single_interaction(interaction, game_state)
            results.append(result)
            processed_count += 1
            
            # Queue any triggered interactions
            for triggered in result.triggered_interactions:
                self.queue_interaction(triggered)
        
        self.logger.debug(f"Processed {processed_count} interactions")
        return results
    
    def create_ability_chain(self, source_piece_id: str, source_ability: str,
                           chain_abilities: List[str], target_positions: List[Coordinate]) -> InteractionContext:
        """Create an ability chain interaction"""
        return InteractionContext(
            interaction_type=InteractionType.ABILITY_CHAIN,
            priority=InteractionPriority.HIGH,
            source_piece_id=source_piece_id,
            source_ability=source_ability,
            target_positions=target_positions,
            parameters={
                "chain_abilities": chain_abilities,
                "delay_between": 0
            }
        )
    
    def create_reaction_trigger(self, triggering_event: str, reactor_piece_id: str,
                              reaction_ability: str, conditions: List[str] = None) -> InteractionContext:
        """Create a reaction trigger interaction"""
        return InteractionContext(
            interaction_type=InteractionType.REACTION_TRIGGER,
            priority=InteractionPriority.IMMEDIATE,
            source_piece_id=reactor_piece_id,
            source_ability=reaction_ability,
            conditions=conditions or [],
            parameters={
                "triggering_event": triggering_event,
                "reaction_type": "immediate"
            }
        )
    
    def create_area_effect(self, center_position: Coordinate, radius: int,
                          effect_type: str, parameters: Dict[str, Any]) -> InteractionContext:
        """Create an area effect interaction"""
        # Calculate affected positions
        affected_positions = self._get_positions_in_radius(center_position, radius)
        
        return InteractionContext(
            interaction_type=InteractionType.AREA_EFFECT,
            priority=InteractionPriority.NORMAL,
            target_positions=affected_positions,
            parameters={
                "effect_type": effect_type,
                "radius": radius,
                "center": center_position,
                **parameters
            }
        )
    
    def create_synergy_bonus(self, piece_ids: List[str], synergy_type: str,
                           bonus_parameters: Dict[str, Any]) -> InteractionContext:
        """Create a synergy bonus interaction"""
        return InteractionContext(
            interaction_type=InteractionType.SYNERGY_BONUS,
            priority=InteractionPriority.LOW,
            target_piece_ids=piece_ids,
            parameters={
                "synergy_type": synergy_type,
                "bonus_parameters": bonus_parameters
            }
        )
    
    def _process_single_interaction(self, interaction: InteractionContext, 
                                  game_state: Any) -> InteractionResult:
        """Process a single interaction"""
        # Mark as processing
        self.processing_interactions.add(interaction.interaction_id)
        
        try:
            # Get handler for interaction type
            handler = self.interaction_handlers.get(interaction.interaction_type)
            if not handler:
                return InteractionResult(
                    success=False,
                    error_message=f"No handler for interaction type: {interaction.interaction_type.value}"
                )
            
            # Process the interaction
            result = handler(interaction, game_state)
            interaction.processed = True
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error processing interaction {interaction.interaction_id}: {str(e)}")
            return InteractionResult(
                success=False,
                error_message=str(e)
            )
        
        finally:
            # Remove from processing set
            self.processing_interactions.discard(interaction.interaction_id)
    
    def _initialize_handlers(self):
        """Initialize interaction handlers"""
        self.interaction_handlers = {
            InteractionType.ABILITY_CHAIN: self._handle_ability_chain,
            InteractionType.REACTION_TRIGGER: self._handle_reaction_trigger,
            InteractionType.STATUS_PROPAGATION: self._handle_status_propagation,
            InteractionType.AREA_EFFECT: self._handle_area_effect,
            InteractionType.CONDITIONAL_ACTIVATION: self._handle_conditional_activation,
            InteractionType.SYNERGY_BONUS: self._handle_synergy_bonus
        }
    
    def _handle_ability_chain(self, interaction: InteractionContext, game_state: Any) -> InteractionResult:
        """Handle ability chain interactions"""
        result = InteractionResult(success=True)
        
        chain_abilities = interaction.parameters.get("chain_abilities", [])
        
        for ability_name in chain_abilities:
            # Create effect for each ability in chain
            effect = {
                "type": "ability_activation",
                "piece_id": interaction.source_piece_id,
                "ability": ability_name,
                "targets": interaction.target_positions
            }
            result.add_effect(effect)
        
        return result
    
    def _handle_reaction_trigger(self, interaction: InteractionContext, game_state: Any) -> InteractionResult:
        """Handle reaction trigger interactions"""
        result = InteractionResult(success=True)
        
        # Check conditions
        if not self._check_conditions(interaction.conditions, game_state):
            result.success = False
            result.error_message = "Reaction conditions not met"
            return result
        
        # Create reaction effect
        effect = {
            "type": "reaction",
            "piece_id": interaction.source_piece_id,
            "ability": interaction.source_ability,
            "triggering_event": interaction.parameters.get("triggering_event")
        }
        result.add_effect(effect)
        
        return result
    
    def _handle_status_propagation(self, interaction: InteractionContext, game_state: Any) -> InteractionResult:
        """Handle status effect propagation"""
        result = InteractionResult(success=True)
        
        status_effect = interaction.parameters.get("status_effect")
        propagation_range = interaction.parameters.get("range", 1)
        
        for piece_id in interaction.target_piece_ids:
            effect = {
                "type": "status_effect",
                "target_piece_id": piece_id,
                "status_effect": status_effect
            }
            result.add_effect(effect)
        
        return result
    
    def _handle_area_effect(self, interaction: InteractionContext, game_state: Any) -> InteractionResult:
        """Handle area effect interactions"""
        result = InteractionResult(success=True)
        
        effect_type = interaction.parameters.get("effect_type")
        
        for position in interaction.target_positions:
            # Find pieces at position
            pieces_at_position = game_state.get_piece_at(position) if hasattr(game_state, 'get_piece_at') else []
            
            for piece in pieces_at_position:
                effect = {
                    "type": "area_effect",
                    "effect_type": effect_type,
                    "target_piece_id": piece.id if hasattr(piece, 'id') else str(piece),
                    "position": position
                }
                result.add_effect(effect)
        
        return result
    
    def _handle_conditional_activation(self, interaction: InteractionContext, game_state: Any) -> InteractionResult:
        """Handle conditional activation interactions"""
        result = InteractionResult(success=True)
        
        # Check activation conditions
        if self._check_conditions(interaction.conditions, game_state):
            effect = {
                "type": "conditional_activation",
                "piece_id": interaction.source_piece_id,
                "ability": interaction.source_ability
            }
            result.add_effect(effect)
        else:
            result.success = False
            result.error_message = "Activation conditions not met"
        
        return result
    
    def _handle_synergy_bonus(self, interaction: InteractionContext, game_state: Any) -> InteractionResult:
        """Handle synergy bonus interactions"""
        result = InteractionResult(success=True)
        
        synergy_type = interaction.parameters.get("synergy_type")
        bonus_params = interaction.parameters.get("bonus_parameters", {})
        
        for piece_id in interaction.target_piece_ids:
            effect = {
                "type": "synergy_bonus",
                "target_piece_id": piece_id,
                "synergy_type": synergy_type,
                "bonus_parameters": bonus_params
            }
            result.add_effect(effect)
        
        return result
    
    def _is_duplicate_interaction(self, interaction: InteractionContext) -> bool:
        """Check if an interaction is a duplicate"""
        for pending in self.pending_interactions:
            if (pending.interaction_type == interaction.interaction_type and
                pending.source_piece_id == interaction.source_piece_id and
                pending.source_ability == interaction.source_ability):
                return True
        return False
    
    def _check_conditions(self, conditions: List[str], game_state: Any) -> bool:
        """Check if conditions are met"""
        # Simplified condition checking
        # In a full implementation, this would parse and evaluate complex conditions
        return True
    
    def _get_positions_in_radius(self, center: Coordinate, radius: int) -> List[Coordinate]:
        """Get all positions within a radius of the center"""
        positions = []
        
        for row in range(max(0, center.row - radius), min(8, center.row + radius + 1)):
            for col in range(max(0, center.col - radius), min(8, center.col + radius + 1)):
                distance = max(abs(row - center.row), abs(col - center.col))
                if distance <= radius:
                    positions.append(Coordinate(row=row, col=col))
        
        return positions
