#!/usr/bin/env python3
"""
Movement Modifier Engine for Adventure Chess Rule Engine
Handles ability effects that modify movement behavior
"""

from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import logging

# Import from schemas for data structures
from schemas.base import Coordinate
from core.tester_engine import PieceColor


class ModifierType(Enum):
    """Types of movement modifiers"""
    RANGE_BONUS = "range_bonus"           # Increase movement range
    RANGE_PENALTY = "range_penalty"       # Decrease movement range
    IGNORE_OBSTACLES = "ignore_obstacles" # Pass through obstacles
    IGNORE_PIECES = "ignore_pieces"       # Pass through pieces
    TELEPORT = "teleport"                 # Ignore all blocking
    FORCED_DIRECTION = "forced_direction" # Restrict to specific directions
    SHARE_SPACE = "share_space"           # Allow multiple pieces per cell
    CARRY_PIECES = "carry_pieces"         # Move other pieces along
    DISPLACEMENT = "displacement"         # Push other pieces
    IMMOBILIZE = "immobilize"            # Prevent movement
    COMMITMENT = "commitment"             # Restrict movement while charging


@dataclass
class MovementModifier:
    """Represents a modifier that affects movement"""
    modifier_id: str
    modifier_type: ModifierType
    source_ability: str
    source_piece_id: str
    target_piece_id: Optional[str] = None
    
    # Modifier parameters
    value: int = 0                        # Numeric value (range bonus/penalty, etc.)
    directions: List[Tuple[int, int]] = field(default_factory=list)  # Allowed directions
    conditions: Dict[str, Any] = field(default_factory=dict)         # Activation conditions
    duration: int = -1                    # -1 for permanent, >0 for temporary
    
    # State tracking
    active: bool = True
    turns_remaining: int = -1
    
    def is_expired(self) -> bool:
        """Check if modifier has expired"""
        return self.duration > 0 and self.turns_remaining <= 0
    
    def tick(self):
        """Reduce duration by one turn"""
        if self.turns_remaining > 0:
            self.turns_remaining -= 1
            if self.turns_remaining <= 0:
                self.active = False


class MovementModifierEngine:
    """
    Manages movement modifiers and their effects
    Integrates with the movement validation system
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Active modifiers by piece
        self.piece_modifiers: Dict[str, List[MovementModifier]] = {}
        
        # Global modifiers (affect all pieces)
        self.global_modifiers: List[MovementModifier] = []
        
        # Modifier processing order
        self.processing_order = [
            ModifierType.IMMOBILIZE,      # Process blocking modifiers first
            ModifierType.COMMITMENT,
            ModifierType.FORCED_DIRECTION,
            ModifierType.RANGE_PENALTY,
            ModifierType.RANGE_BONUS,
            ModifierType.IGNORE_OBSTACLES,
            ModifierType.IGNORE_PIECES,
            ModifierType.TELEPORT,
            ModifierType.SHARE_SPACE,
            ModifierType.CARRY_PIECES,
            ModifierType.DISPLACEMENT
        ]
    
    def add_modifier(self, modifier: MovementModifier):
        """Add a movement modifier"""
        if modifier.target_piece_id:
            # Piece-specific modifier
            if modifier.target_piece_id not in self.piece_modifiers:
                self.piece_modifiers[modifier.target_piece_id] = []
            self.piece_modifiers[modifier.target_piece_id].append(modifier)
        else:
            # Global modifier
            self.global_modifiers.append(modifier)
        
        self.logger.debug(f"Added movement modifier: {modifier.modifier_type.value} for piece {modifier.target_piece_id}")
    
    def remove_modifier(self, modifier_id: str):
        """Remove a movement modifier by ID"""
        # Remove from piece modifiers
        for piece_id, modifiers in self.piece_modifiers.items():
            self.piece_modifiers[piece_id] = [m for m in modifiers if m.modifier_id != modifier_id]
        
        # Remove from global modifiers
        self.global_modifiers = [m for m in self.global_modifiers if m.modifier_id != modifier_id]
        
        self.logger.debug(f"Removed movement modifier: {modifier_id}")
    
    def get_active_modifiers(self, piece_id: str) -> List[MovementModifier]:
        """Get all active modifiers affecting a piece"""
        modifiers = []
        
        # Add piece-specific modifiers
        if piece_id in self.piece_modifiers:
            modifiers.extend([m for m in self.piece_modifiers[piece_id] if m.active and not m.is_expired()])
        
        # Add global modifiers
        modifiers.extend([m for m in self.global_modifiers if m.active and not m.is_expired()])
        
        return modifiers
    
    def apply_modifiers_to_movement(self, piece_id: str, base_movement_data: Dict[str, Any],
                                  current_position: Coordinate, game_state: Any) -> Dict[str, Any]:
        """
        Apply all active modifiers to movement data
        
        Args:
            piece_id: ID of piece being moved
            base_movement_data: Base movement configuration
            current_position: Current piece position
            game_state: Current game state
            
        Returns:
            Modified movement data
        """
        modified_data = base_movement_data.copy()
        active_modifiers = self.get_active_modifiers(piece_id)
        
        # Sort modifiers by processing order
        sorted_modifiers = self._sort_modifiers_by_priority(active_modifiers)
        
        # Apply each modifier
        for modifier in sorted_modifiers:
            modified_data = self._apply_single_modifier(modifier, modified_data, current_position, game_state)
        
        return modified_data
    
    def _sort_modifiers_by_priority(self, modifiers: List[MovementModifier]) -> List[MovementModifier]:
        """Sort modifiers by processing priority"""
        def get_priority(modifier: MovementModifier) -> int:
            try:
                return self.processing_order.index(modifier.modifier_type)
            except ValueError:
                return len(self.processing_order)  # Unknown types go last
        
        return sorted(modifiers, key=get_priority)
    
    def _apply_single_modifier(self, modifier: MovementModifier, movement_data: Dict[str, Any],
                             current_position: Coordinate, game_state: Any) -> Dict[str, Any]:
        """Apply a single modifier to movement data"""
        if modifier.modifier_type == ModifierType.RANGE_BONUS:
            return self._apply_range_bonus(modifier, movement_data)
        
        elif modifier.modifier_type == ModifierType.RANGE_PENALTY:
            return self._apply_range_penalty(modifier, movement_data)
        
        elif modifier.modifier_type == ModifierType.IGNORE_OBSTACLES:
            return self._apply_ignore_obstacles(modifier, movement_data)
        
        elif modifier.modifier_type == ModifierType.IGNORE_PIECES:
            return self._apply_ignore_pieces(modifier, movement_data)
        
        elif modifier.modifier_type == ModifierType.TELEPORT:
            return self._apply_teleport(modifier, movement_data)
        
        elif modifier.modifier_type == ModifierType.FORCED_DIRECTION:
            return self._apply_forced_direction(modifier, movement_data, current_position)
        
        elif modifier.modifier_type == ModifierType.SHARE_SPACE:
            return self._apply_share_space(modifier, movement_data)
        
        elif modifier.modifier_type == ModifierType.IMMOBILIZE:
            return self._apply_immobilize(modifier, movement_data)
        
        elif modifier.modifier_type == ModifierType.COMMITMENT:
            return self._apply_commitment(modifier, movement_data)
        
        # Add more modifier types as needed
        
        return movement_data
    
    def _apply_range_bonus(self, modifier: MovementModifier, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply range bonus modifier"""
        current_distance = movement_data.get('distance', 1)
        movement_data['distance'] = current_distance + modifier.value
        movement_data['_range_modified'] = True
        return movement_data
    
    def _apply_range_penalty(self, modifier: MovementModifier, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply range penalty modifier"""
        current_distance = movement_data.get('distance', 1)
        movement_data['distance'] = max(0, current_distance - modifier.value)
        movement_data['_range_modified'] = True
        return movement_data
    
    def _apply_ignore_obstacles(self, modifier: MovementModifier, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply ignore obstacles modifier"""
        movement_data['ignore_obstacles'] = True
        return movement_data
    
    def _apply_ignore_pieces(self, modifier: MovementModifier, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply ignore pieces modifier"""
        movement_data['ignore_pieces'] = True
        return movement_data
    
    def _apply_teleport(self, modifier: MovementModifier, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply teleport modifier"""
        movement_data['teleport'] = True
        movement_data['ignore_obstacles'] = True
        movement_data['ignore_pieces'] = True
        return movement_data
    
    def _apply_forced_direction(self, modifier: MovementModifier, movement_data: Dict[str, Any],
                              current_position: Coordinate) -> Dict[str, Any]:
        """Apply forced direction modifier"""
        if modifier.directions:
            movement_data['allowed_directions'] = modifier.directions
        return movement_data
    
    def _apply_share_space(self, modifier: MovementModifier, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply share space modifier"""
        movement_data['allow_stacking'] = True
        movement_data['max_stack_size'] = modifier.value if modifier.value > 0 else 999
        return movement_data
    
    def _apply_immobilize(self, modifier: MovementModifier, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply immobilize modifier"""
        movement_data['immobilized'] = True
        movement_data['distance'] = 0
        return movement_data
    
    def _apply_commitment(self, modifier: MovementModifier, movement_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply commitment modifier"""
        movement_data['committed'] = True
        movement_data['distance'] = 0
        return movement_data
    
    def check_movement_allowed(self, piece_id: str) -> Tuple[bool, str]:
        """
        Check if a piece is allowed to move
        
        Returns:
            (can_move, reason)
        """
        active_modifiers = self.get_active_modifiers(piece_id)
        
        # Check for blocking modifiers
        for modifier in active_modifiers:
            if modifier.modifier_type == ModifierType.IMMOBILIZE:
                return False, f"Piece is immobilized by {modifier.source_ability}"
            
            if modifier.modifier_type == ModifierType.COMMITMENT:
                return False, f"Piece is committed and cannot move"
        
        return True, "Movement allowed"
    
    def get_movement_restrictions(self, piece_id: str) -> Dict[str, Any]:
        """Get all movement restrictions for a piece"""
        restrictions = {
            'immobilized': False,
            'committed': False,
            'forced_directions': None,
            'range_modifier': 0,
            'special_abilities': []
        }
        
        active_modifiers = self.get_active_modifiers(piece_id)
        
        for modifier in active_modifiers:
            if modifier.modifier_type == ModifierType.IMMOBILIZE:
                restrictions['immobilized'] = True
            
            elif modifier.modifier_type == ModifierType.COMMITMENT:
                restrictions['committed'] = True
            
            elif modifier.modifier_type == ModifierType.FORCED_DIRECTION:
                restrictions['forced_directions'] = modifier.directions
            
            elif modifier.modifier_type == ModifierType.RANGE_BONUS:
                restrictions['range_modifier'] += modifier.value
            
            elif modifier.modifier_type == ModifierType.RANGE_PENALTY:
                restrictions['range_modifier'] -= modifier.value
            
            elif modifier.modifier_type in [ModifierType.TELEPORT, ModifierType.IGNORE_OBSTACLES, 
                                          ModifierType.IGNORE_PIECES, ModifierType.SHARE_SPACE]:
                restrictions['special_abilities'].append(modifier.modifier_type.value)
        
        return restrictions
    
    def tick_all_modifiers(self):
        """Reduce duration of all temporary modifiers"""
        # Tick piece modifiers
        for piece_id, modifiers in self.piece_modifiers.items():
            for modifier in modifiers:
                modifier.tick()
            
            # Remove expired modifiers
            self.piece_modifiers[piece_id] = [m for m in modifiers if not m.is_expired()]
        
        # Tick global modifiers
        for modifier in self.global_modifiers:
            modifier.tick()
        
        # Remove expired global modifiers
        self.global_modifiers = [m for m in self.global_modifiers if not m.is_expired()]
    
    def clear_all_modifiers(self):
        """Clear all movement modifiers"""
        self.piece_modifiers.clear()
        self.global_modifiers.clear()
        self.logger.debug("All movement modifiers cleared")
    
    def get_modifier_summary(self, piece_id: str) -> Dict[str, Any]:
        """Get a summary of all modifiers affecting a piece"""
        active_modifiers = self.get_active_modifiers(piece_id)
        
        summary = {
            'total_modifiers': len(active_modifiers),
            'by_type': {},
            'restrictions': self.get_movement_restrictions(piece_id),
            'can_move': self.check_movement_allowed(piece_id)[0]
        }
        
        for modifier in active_modifiers:
            mod_type = modifier.modifier_type.value
            if mod_type not in summary['by_type']:
                summary['by_type'][mod_type] = 0
            summary['by_type'][mod_type] += 1
        
        return summary
