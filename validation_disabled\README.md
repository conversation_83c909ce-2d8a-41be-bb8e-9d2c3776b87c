# Validation Files - DISABLED

This folder contains validation-related files that have been temporarily disabled until further notice.

## Files Moved Here

### Test Files
- `test_complete_data_flow.py` - Complete data pipeline validation test
- `test_widget_mapping_validation.py` - UI widget mapping coverage test  
- `test_refactor_validation.py` - Refactor validation and legacy code detection

### Validation Utilities
- `validate_data.py` - Data validation utilities (from utils/)
- `validators.py` - Validation helper functions (from utils/)
- `test_validators.py` - Validator tests (from tests/)
- `validator.py` - Rule engine validator (from rule_engine/)

## Status

**🚫 DISABLED UNTIL FURTHER NOTICE**

These validation files are currently disabled and should not be used in the main application flow. They were moved here to:

1. Remove validation overhead from editor functionality
2. Simplify the save process 
3. Focus on core editor features without validation constraints

## Re-enabling

To re-enable validation:

1. Move files back to their original locations:
   - `validate_data.py` → `utils/`
   - `validators.py` → `utils/`
   - `test_validators.py` → `tests/`
   - `validator.py` → `rule_engine/`

2. Update any import statements that reference these files

3. Re-integrate validation calls in the editor save processes

## Original Functionality

These files provided:
- Comprehensive data validation using Pydantic schemas
- Widget mapping validation and coverage testing
- Data flow integrity testing from UI to JSON to piece tester
- Legacy code pattern detection
- Refactor validation and completeness checking

## Notes

The core data flow (UI → EditorDataInterface → PydanticBridge → JSON) remains intact and functional. Only the additional validation layers have been disabled.
