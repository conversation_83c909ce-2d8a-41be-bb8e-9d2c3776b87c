"""
Validation Module for Adventure Chess - Pydantic Integration
This module now serves as a compatibility layer that redirects to Pydantic validation.

The legacy validation classes have been removed in favor of the new Pydantic-based system.
"""

import logging
from typing import Dict, List, Tuple

logger = logging.getLogger(__name__)

def validate_piece_data(piece_data: Dict) -> Tuple[bool, List[str], List[str]]:
    """
    Validate piece data using Pydantic validation
    Returns: (is_valid, errors, warnings)
    """
    try:
        from utils.pydantic_bridge import pydantic_bridge
        return pydantic_bridge.validate_piece_data(piece_data)
    except Exception as e:
        logger.error(f"Pydantic validation failed: {e}")
        return False, [f"Validation error: {e}"], []

def validate_ability_data(ability_data: Dict) -> Tuple[bool, List[str], List[str]]:
    """
    Validate ability data using Pydantic validation
    Returns: (is_valid, errors, warnings)
    """
    try:
        from utils.pydantic_bridge import pydantic_bridge
        return pydantic_bridge.validate_ability_data(ability_data)
    except Exception as e:
        logger.error(f"Pydantic validation failed: {e}")
        return False, [f"Validation error: {e}"], []

# Note: Legacy validation classes (PieceValidator, AbilityValidator, etc.) have been removed.
# All validation now goes through the Pydantic system via pydantic_bridge.
