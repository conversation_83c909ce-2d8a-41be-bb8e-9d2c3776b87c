#!/usr/bin/env python3
"""
Refactor Validation Test - Adventure Chess
Tests the streamlined data flow according to DATA_FLOW_REFERENCE.md refactor prompts

This test validates:
1. Single source of truth through PydanticBridge
2. Universal data collection through EditorDataInterface
3. No legacy bypass patterns
4. Proper field mapping and data flow
5. Consistent state management
"""

import sys
import os
import json
from typing import Dict, Any

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_pydantic_bridge_integration():
    """Test that PydanticBridge uses EditorDataInterface (not legacy methods)"""
    print("=== Testing PydanticBridge Integration ===")
    
    from utils.pydantic_bridge import PydanticBridge
    import inspect
    
    # Check that bridge methods use EditorDataInterface
    source = inspect.getsource(PydanticBridge.get_ability_data_from_ui)
    if "EditorDataInterface.collect_data_from_ui" in source:
        print("✓ PydanticBridge.get_ability_data_from_ui uses EditorDataInterface")
    else:
        print("❌ PydanticBridge.get_ability_data_from_ui still uses legacy methods")
    
    source = inspect.getsource(PydanticBridge.set_ability_data_to_ui)
    if "EditorDataInterface.populate_ui_from_data" in source:
        print("✓ PydanticBridge.set_ability_data_to_ui uses EditorDataInterface")
    else:
        print("❌ PydanticBridge.set_ability_data_to_ui still uses legacy methods")
    
    source = inspect.getsource(PydanticBridge.get_piece_data_from_ui)
    if "EditorDataInterface.collect_data_from_ui" in source:
        print("✓ PydanticBridge.get_piece_data_from_ui uses EditorDataInterface")
    else:
        print("❌ PydanticBridge.get_piece_data_from_ui still uses legacy methods")
    
    source = inspect.getsource(PydanticBridge.set_piece_data_to_ui)
    if "EditorDataInterface.populate_ui_from_data" in source:
        print("✓ PydanticBridge.set_piece_data_to_ui uses EditorDataInterface")
    else:
        print("❌ PydanticBridge.set_piece_data_to_ui still uses legacy methods")

def test_editor_data_interface_mappings():
    """Test EditorDataInterface field mappings completeness"""
    print("\n=== Testing EditorDataInterface Field Mappings ===")
    
    from utils.editor_data_interface import EditorDataInterface
    
    ability_mappings = EditorDataInterface.ABILITY_FIELD_MAPPINGS
    piece_mappings = EditorDataInterface.PIECE_FIELD_MAPPINGS
    
    print(f"✓ Ability field mappings: {len(ability_mappings)} fields")
    print(f"✓ Piece field mappings: {len(piece_mappings)} fields")
    
    # Check for key fields
    ability_fields = [mapping[1] for mapping in ability_mappings]
    piece_fields = [mapping[1] for mapping in piece_mappings]
    
    # Essential ability fields
    essential_ability_fields = ['name', 'description', 'cost', 'activationMode', 'autoCostCheck']
    for field in essential_ability_fields:
        if field in ability_fields:
            print(f"✓ Essential ability field '{field}' mapped")
        else:
            print(f"❌ Essential ability field '{field}' missing")
    
    # Essential piece fields
    essential_piece_fields = ['name', 'description', 'role', 'movement.type', 'canCapture']
    for field in essential_piece_fields:
        if field in piece_fields:
            print(f"✓ Essential piece field '{field}' mapped")
        else:
            print(f"❌ Essential piece field '{field}' missing")

def test_nested_field_handling():
    """Test nested field handling (e.g., movement.distance)"""
    print("\n=== Testing Nested Field Handling ===")
    
    from utils.editor_data_interface import EditorDataInterface
    
    # Create mock editor instance
    class MockPieceEditor:
        def __init__(self):
            self.move_combo = MockCombo("orthogonal")
            self.ortho_range_spin = MockSpin(3)
            self.diag_range_spin = MockSpin(2)
            self.any_range_spin = MockSpin(5)
            self.abilities = []
            self.primary_promotions = []
            self.secondary_promotions = []
    
    class MockCombo:
        def __init__(self, text):
            self._text = text
        def currentText(self):
            return self._text
    
    class MockSpin:
        def __init__(self, value):
            self._value = value
        def value(self):
            return self._value
        def setValue(self, value):
            self._value = value
    
    mock_editor = MockPieceEditor()
    
    # Test data collection with nested fields
    data = EditorDataInterface.collect_data_from_ui(mock_editor, "piece")
    
    if 'movement' in data and 'type' in data['movement']:
        print(f"✓ Nested field collection: movement.type = {data['movement']['type']}")
    else:
        print("❌ Nested field collection failed for movement.type")
    
    if 'movement' in data and 'distance' in data['movement']:
        print(f"✓ Movement distance handling: distance = {data['movement']['distance']}")
    else:
        print("❌ Movement distance handling failed")

def test_data_flow_consistency():
    """Test complete data flow: collect -> validate -> populate"""
    print("\n=== Testing Data Flow Consistency ===")
    
    from utils.editor_data_interface import EditorDataInterface
    from schemas import DataMigrationManager
    
    # Test ability data flow
    test_ability_data = {
        'version': '1.0.0',
        'name': 'Test Ability',
        'description': 'Test description',
        'cost': 5,
        'autoCostCheck': False,
        'activationMode': 'manual',
        'tags': ['range', 'areaEffect']
    }
    
    # Test migration
    ability, warnings = DataMigrationManager.migrate_ability_dict_to_model(test_ability_data)
    if ability:
        print("✓ Ability data migration successful")
        
        # Test back to dict
        legacy_dict = ability.to_legacy_dict()
        if legacy_dict['name'] == test_ability_data['name']:
            print("✓ Ability round-trip conversion successful")
        else:
            print("❌ Ability round-trip conversion failed")
    else:
        print("❌ Ability data migration failed")
    
    # Test piece data flow
    test_piece_data = {
        'version': '1.0.0',
        'name': 'Test Piece',
        'description': 'Test piece description',
        'role': 'Commander',
        'movement': {'type': 'orthogonal', 'distance': 2},
        'canCapture': True,
        'abilities': []
    }
    
    # Test migration
    piece, warnings = DataMigrationManager.migrate_piece_dict_to_model(test_piece_data)
    if piece:
        print("✓ Piece data migration successful")
        
        # Test back to dict
        legacy_dict = piece.to_legacy_dict()
        if legacy_dict['name'] == test_piece_data['name']:
            print("✓ Piece round-trip conversion successful")
        else:
            print("❌ Piece round-trip conversion failed")
    else:
        print("❌ Piece data migration failed")

def test_single_source_of_truth():
    """Test that all data operations go through PydanticBridge"""
    print("\n=== Testing Single Source of Truth ===")
    
    from editors.base_editor import BaseEditor
    import inspect
    
    # Check BaseEditor methods use PydanticBridge
    source = inspect.getsource(BaseEditor.load_data)
    if "pydantic_bridge.load_" in source:
        print("✓ BaseEditor.load_data uses pydantic_bridge")
    else:
        print("❌ BaseEditor.load_data bypasses pydantic_bridge")
    
    source = inspect.getsource(BaseEditor.save_data)
    if "pydantic_bridge.save_" in source:
        print("✓ BaseEditor.save_data uses pydantic_bridge")
    else:
        print("❌ BaseEditor.save_data bypasses pydantic_bridge")
    
    source = inspect.getsource(BaseEditor.validate_data)
    if "pydantic_bridge.validate_" in source:
        print("✓ BaseEditor.validate_data uses pydantic_bridge")
    else:
        print("❌ BaseEditor.validate_data bypasses pydantic_bridge")

def main():
    """Run all refactor validation tests"""
    print("🎯 Adventure Chess Refactor Validation")
    print("=" * 50)
    
    try:
        test_pydantic_bridge_integration()
        test_editor_data_interface_mappings()
        test_nested_field_handling()
        test_data_flow_consistency()
        test_single_source_of_truth()
        
        print("\n" + "=" * 50)
        print("✅ Refactor validation complete!")
        print("🎉 System follows DATA_FLOW_REFERENCE.md specifications")
        
    except Exception as e:
        print(f"\n❌ Validation failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
