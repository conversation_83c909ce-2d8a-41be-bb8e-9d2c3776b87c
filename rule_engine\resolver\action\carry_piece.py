#!/usr/bin/env python3
"""Carry Piece Tag Resolver for Adventure Chess Rule Engine"""

from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data

class CarryPieceTagResolver(BaseTagResolver):
    def _get_tag_name(self) -> str:
        return "carryPiece"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.ACTION
    
    def _get_conflicts(self) -> List[str]:
        return ["immobilize"]
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        carry_data = extract_tag_data(ability, "carryPiece")
        return [TagEffect(
            effect_type="carry_piece",
            priority=75,
            immediate=True,
            parameters={
                "action": "carry_piece",
                "range": carry_data.get("range", 1),
                "drop_on_death": carry_data.get("drop_on_death", True),
                "share_abilities": carry_data.get("share_abilities", False)
            }
        )]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        return {"requires_target": True, "target_type": "piece", "min_targets": 1, "max_targets": 1}
