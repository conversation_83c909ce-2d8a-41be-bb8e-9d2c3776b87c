{"version": "1.0.0", "name": "Test<PERSON><PERSON>ce", "description": "Test piece for missing fields", "role": "Commander", "canCastle": false, "movement": {"type": "orthogonal", "pattern": [[0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0], [3, 3, 3, 0, 3, 3, 3, 3], [0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0], [0, 0, 0, 3, 0, 0, 0, 0]], "piecePosition": [3, 3]}, "canCapture": false, "colorDirectional": true, "abilities": ["Bishops Teleport home", "Bishops revival", "Kings Swap"], "trackStartingPosition": false, "blackIcon": "(None)", "whiteIcon": "(None)", "enableRecharge": false, "maxPoints": 3, "startingPoints": 2, "rechargeType": "turn<PERSON><PERSON><PERSON><PERSON>", "turnPoints": 1, "committedRechargeTurns": 1, "adjacencyDistance": 1, "promotions": [], "secondaryPromotions": []}