#!/usr/bin/env python3
"""
Debug Tag Collection Issue
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_tag_collection():
    """Debug the tag collection issue"""
    
    print("=== DEBUG TAG COLLECTION ===")
    
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # Create minimal QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from editors.ability_editor import AbilityEditorWindow
        from utils.editor_data_interface import EditorDataInterface
        
        # Create ability editor instance
        editor = AbilityEditorWindow()
        
        print(f"Editor created: {type(editor)}")
        print(f"Has tag_groups: {hasattr(editor, 'tag_groups')}")
        
        if hasattr(editor, 'tag_groups'):
            print(f"Tag groups count: {len(editor.tag_groups)}")
            print(f"Tag groups keys: {list(editor.tag_groups.keys())[:5]}...")  # First 5
            
            # Test selecting a tag
            test_tag = 'move'
            if test_tag in editor.tag_groups:
                checkbox = editor.tag_groups[test_tag]
                print(f"Found checkbox for {test_tag}: {type(checkbox)}")
                
                # Check the checkbox
                print(f"Before setChecked: {checkbox.isChecked()}")
                checkbox.setChecked(True)
                print(f"After setChecked: {checkbox.isChecked()}")

                # Force process events
                app.processEvents()
                print(f"After processEvents: {checkbox.isChecked()}")

                # Check if there are any signal connections
                print(f"Checkbox signals: {checkbox.receivers(checkbox.stateChanged)}")

                # Try blocking signals
                checkbox.blockSignals(True)
                checkbox.setChecked(True)
                print(f"After blocking signals and setChecked: {checkbox.isChecked()}")
                checkbox.blockSignals(False)

                # Try to collect data directly
                print("\nTesting direct collection...")
                tags = []
                for tag, cb in editor.tag_groups.items():
                    if cb.isChecked():
                        tags.append(tag)
                        print(f"Found checked tag: {tag}")

                print(f"Direct collection result: {tags}")

                # Test EditorDataInterface collection
                print("\nTesting EditorDataInterface collection...")
                data = EditorDataInterface.collect_data_from_ui(editor, "ability")
                print(f"EditorDataInterface result: {data.get('tags', 'NO TAGS FIELD')}")

                # Check if the checkbox is still checked
                print(f"Checkbox still checked after collection: {checkbox.isChecked()}")
                
            else:
                print(f"Tag {test_tag} not found in tag_groups")
        
        return True
        
    except Exception as e:
        print(f"Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_tag_collection()
