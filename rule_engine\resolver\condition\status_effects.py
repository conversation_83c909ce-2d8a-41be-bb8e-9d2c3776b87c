#!/usr/bin/env python3
"""
Status Effect Tag Resolvers for Adventure Chess Rule Engine
Handles status effect and immunity tags
"""

from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class ProtectTagResolver(BaseTagResolver):
    """Resolver for protect tag"""
    
    def _get_tag_name(self) -> str:
        return "protect"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def _get_conflicts(self) -> List[str]:
        return ["capture", "damage"]
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        protect_data = extract_tag_data(ability, "protect")
        
        return [TagEffect(
            effect_type="status_effect",
            priority=220,
            immediate=True,
            parameters={
                "effect_type": "protection",
                "duration": protect_data.get("duration", -1),
                "protection_type": protect_data.get("type", "all"),
                "source_piece_id": context.source_piece_id
            }
        )]


class CannotDieTagResolver(BaseTagResolver):
    """Resolver for cannotDie tag"""
    
    def _get_tag_name(self) -> str:
        return "cannotDie"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def _get_conflicts(self) -> List[str]:
        return ["capture", "damage", "banish"]
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        cannot_die_data = extract_tag_data(ability, "cannotDie")
        
        return [TagEffect(
            effect_type="status_effect",
            priority=225,
            immediate=True,
            parameters={
                "effect_type": "immortality",
                "duration": cannot_die_data.get("duration", -1),
                "condition": cannot_die_data.get("condition"),
                "min_health": cannot_die_data.get("min_health", 1)
            }
        )]


class InvisibleTagResolver(BaseTagResolver):
    """Resolver for invisible tag"""
    
    def _get_tag_name(self) -> str:
        return "invisible"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def _get_conflicts(self) -> List[str]:
        return ["truesight"]
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        invisible_data = extract_tag_data(ability, "invisible")
        
        return [TagEffect(
            effect_type="status_effect",
            priority=215,
            immediate=True,
            parameters={
                "effect_type": "invisibility",
                "duration": invisible_data.get("duration", -1),
                "break_on_action": invisible_data.get("break_on_action", True),
                "break_on_damage": invisible_data.get("break_on_damage", True)
            }
        )]


class TruesightTagResolver(BaseTagResolver):
    """Resolver for truesight tag"""
    
    def _get_tag_name(self) -> str:
        return "truesight"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        truesight_data = extract_tag_data(ability, "truesight")
        
        return [TagEffect(
            effect_type="status_effect",
            priority=210,
            immediate=True,
            parameters={
                "effect_type": "truesight",
                "duration": truesight_data.get("duration", -1),
                "see_invisible": True,
                "see_through_illusions": truesight_data.get("see_illusions", True),
                "range": truesight_data.get("range", 8)
            }
        )]


class ImmobilizeImmuneTagResolver(BaseTagResolver):
    """Resolver for immobilizeImmune tag"""
    
    def _get_tag_name(self) -> str:
        return "immobilizeImmune"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def _get_conflicts(self) -> List[str]:
        return ["immobilize"]
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        immune_data = extract_tag_data(ability, "immobilizeImmune")
        
        return [TagEffect(
            effect_type="immunity",
            priority=205,
            immediate=True,
            parameters={
                "immunity_type": "immobilize",
                "duration": immune_data.get("duration", -1),
                "source": immune_data.get("source", "all")
            }
        )]
