"""
Unit tests for tester_engine module
"""
import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime

from tester_engine import (
    TesterEngine, PieceInstance, BoardPosition, PieceColor, 
    InteractionMode, ActionLog
)

class TestBoardPosition(unittest.TestCase):
    """Test BoardPosition class"""
    
    def test_valid_position(self):
        """Test creating valid board positions"""
        pos = BoardPosition(3, 4)
        self.assertEqual(pos.row, 3)
        self.assertEqual(pos.col, 4)
        self.assertEqual(pos.to_tuple(), (3, 4))
        self.assertEqual(str(pos), "(3, 4)")
    
    def test_invalid_position(self):
        """Test that invalid positions raise ValueError"""
        with self.assertRaises(ValueError):
            BoardPosition(-1, 0)
        
        with self.assertRaises(ValueError):
            BoardPosition(0, 8)
        
        with self.assertRaises(ValueError):
            BoardPosition(8, 0)

class TestPieceInstance(unittest.TestCase):
    """Test PieceInstance class"""
    
    def test_piece_creation(self):
        """Test creating a piece instance"""
        position = BoardPosition(2, 3)
        piece = PieceInstance(
            piece_name="Test Piece",
            color=PieceColor.WHITE,
            position=position,
            current_points=5,
            max_points=10
        )
        
        self.assertEqual(piece.piece_name, "Test Piece")
        self.assertEqual(piece.color, PieceColor.WHITE)
        self.assertEqual(piece.position, position)
        self.assertEqual(piece.current_points, 5)
        self.assertEqual(piece.max_points, 10)
        self.assertIsNotNone(piece.instance_id)
        self.assertTrue(piece.instance_id.startswith("Test Piece_23_"))

class TestTesterEngine(unittest.TestCase):
    """Test TesterEngine functionality"""
    
    def setUp(self):
        """Set up test engine"""
        self.engine = TesterEngine()
        
        # Mock piece data
        self.mock_piece_data = {
            "name": "Test Piece",
            "movement": {"type": "orthogonal", "distance": 1},
            "maxPoints": 5,
            "startingPoints": 3,
            "rechargeType": "turnRecharge",
            "turnPoints": 1,
            "abilities": []
        }
    
    def test_initial_state(self):
        """Test initial engine state"""
        self.assertEqual(self.engine.turn_number, 1)
        self.assertEqual(len(self.engine.board), 0)
        self.assertEqual(len(self.engine.piece_instances), 0)
        self.assertEqual(len(self.engine.action_log), 0)
    
    def test_reset_board(self):
        """Test board reset functionality"""
        # Add some data first
        self.engine.turn_number = 5
        self.engine.action_log.append(ActionLog(
            turn=1,
            timestamp=datetime.now(),
            action_type="test",
            description="test action"
        ))
        
        # Reset
        self.engine.reset_board()
        
        # Verify reset
        self.assertEqual(self.engine.turn_number, 1)
        self.assertEqual(len(self.engine.board), 0)
        self.assertEqual(len(self.engine.piece_instances), 0)
        self.assertEqual(len(self.engine.action_log), 1)  # Reset action is logged
    
    @patch('tester_engine.piece_manager.load_piece_by_name')
    def test_place_piece_success(self, mock_load_piece):
        """Test successful piece placement"""
        mock_load_piece.return_value = (self.mock_piece_data, None)
        
        position = BoardPosition(3, 4)
        success, message = self.engine.place_piece("Test Piece", PieceColor.WHITE, position)
        
        self.assertTrue(success)
        self.assertIn("Placed", message)
        self.assertEqual(len(self.engine.board), 1)
        self.assertEqual(len(self.engine.piece_instances), 1)
        
        # Verify piece is at correct position
        piece = self.engine.get_piece_at(position)
        self.assertIsNotNone(piece)
        self.assertEqual(piece.piece_name, "Test Piece")
        self.assertEqual(piece.color, PieceColor.WHITE)
    
    @patch('tester_engine.piece_manager.load_piece_by_name')
    def test_place_piece_occupied_position(self, mock_load_piece):
        """Test placing piece on occupied position fails"""
        mock_load_piece.return_value = (self.mock_piece_data, None)
        
        position = BoardPosition(3, 4)
        
        # Place first piece
        success1, _ = self.engine.place_piece("Test Piece", PieceColor.WHITE, position)
        self.assertTrue(success1)
        
        # Try to place second piece at same position
        success2, message2 = self.engine.place_piece("Test Piece", PieceColor.BLACK, position)
        self.assertFalse(success2)
        self.assertIn("occupied", message2)
    
    @patch('tester_engine.piece_manager.load_piece_by_name')
    def test_place_piece_load_error(self, mock_load_piece):
        """Test placing piece with load error"""
        mock_load_piece.return_value = (None, "Piece not found")
        
        position = BoardPosition(3, 4)
        success, message = self.engine.place_piece("Nonexistent Piece", PieceColor.WHITE, position)
        
        self.assertFalse(success)
        self.assertIn("Cannot load piece", message)
    
    @patch('tester_engine.piece_manager.load_piece_by_name')
    def test_move_piece_success(self, mock_load_piece):
        """Test successful piece movement"""
        mock_load_piece.return_value = (self.mock_piece_data, None)
        
        from_pos = BoardPosition(3, 4)
        to_pos = BoardPosition(3, 5)
        
        # Place piece first
        self.engine.place_piece("Test Piece", PieceColor.WHITE, from_pos)
        
        # Move piece
        success, message = self.engine.move_piece(from_pos, to_pos)
        
        self.assertTrue(success)
        self.assertIn("Moved", message)
        
        # Verify piece moved
        self.assertIsNone(self.engine.get_piece_at(from_pos))
        piece = self.engine.get_piece_at(to_pos)
        self.assertIsNotNone(piece)
        self.assertEqual(piece.position, to_pos)
    
    @patch('tester_engine.piece_manager.load_piece_by_name')
    def test_move_piece_no_source(self, mock_load_piece):
        """Test moving from empty position fails"""
        from_pos = BoardPosition(3, 4)
        to_pos = BoardPosition(3, 5)
        
        success, message = self.engine.move_piece(from_pos, to_pos)
        
        self.assertFalse(success)
        self.assertIn("No piece at", message)
    
    @patch('tester_engine.piece_manager.load_piece_by_name')
    def test_remove_piece(self, mock_load_piece):
        """Test piece removal"""
        mock_load_piece.return_value = (self.mock_piece_data, None)
        
        position = BoardPosition(3, 4)
        
        # Place piece first
        self.engine.place_piece("Test Piece", PieceColor.WHITE, position)
        self.assertEqual(len(self.engine.board), 1)
        
        # Remove piece
        success, message = self.engine.remove_piece(position)
        
        self.assertTrue(success)
        self.assertIn("Removed", message)
        self.assertEqual(len(self.engine.board), 0)
        self.assertEqual(len(self.engine.piece_instances), 0)
    
    @patch('tester_engine.piece_manager.load_piece_by_name')
    def test_next_turn(self, mock_load_piece):
        """Test turn advancement"""
        mock_load_piece.return_value = (self.mock_piece_data, None)
        
        # Place a piece
        position = BoardPosition(3, 4)
        self.engine.place_piece("Test Piece", PieceColor.WHITE, position)
        
        # Get the piece and modify its state
        piece = self.engine.get_piece_at(position)
        piece.current_points = 2  # Less than max
        piece.cooldowns["test_ability"] = 2
        piece.abilities_used_this_turn.add("some_ability")
        
        initial_turn = self.engine.turn_number
        
        # Advance turn
        summary = self.engine.next_turn()
        
        # Verify turn advanced
        self.assertEqual(self.engine.turn_number, initial_turn + 1)
        self.assertIn(f"Turn {initial_turn + 1}", summary)
        
        # Verify piece state updated
        self.assertEqual(len(piece.abilities_used_this_turn), 0)  # Cleared
        self.assertEqual(piece.cooldowns["test_ability"], 1)  # Decremented
        self.assertGreater(piece.current_points, 2)  # Recharged
    
    @patch('tester_engine.piece_manager.load_piece_by_name')
    @patch('tester_engine.ability_manager.load_piece_abilities')
    def test_use_ability_success(self, mock_load_abilities, mock_load_piece):
        """Test successful ability usage"""
        mock_load_piece.return_value = (self.mock_piece_data, None)
        mock_ability = {
            "name": "Test Ability",
            "cost": 2,
            "delay": 0,
            "activationMode": "click",
            "tags": []
        }
        mock_load_abilities.return_value = ([mock_ability], [])
        
        # Place piece
        position = BoardPosition(3, 4)
        self.engine.place_piece("Test Piece", PieceColor.WHITE, position)
        piece = self.engine.get_piece_at(position)
        piece.current_points = 5  # Enough for ability
        
        # Use ability
        success, message = self.engine.use_ability(piece.instance_id, "Test Ability")
        
        self.assertTrue(success)
        self.assertEqual(piece.current_points, 3)  # Cost deducted
        self.assertIn("Test Ability", piece.abilities_used_this_turn)
    
    @patch('tester_engine.piece_manager.load_piece_by_name')
    def test_get_board_state(self, mock_load_piece):
        """Test getting board state for saving"""
        mock_load_piece.return_value = (self.mock_piece_data, None)
        
        # Place a piece
        position = BoardPosition(3, 4)
        self.engine.place_piece("Test Piece", PieceColor.WHITE, position)
        self.engine.turn_number = 5
        
        # Get state
        state = self.engine.get_board_state()
        
        self.assertEqual(state['version'], '1.0.0')
        self.assertEqual(state['turn_number'], 5)
        self.assertEqual(len(state['pieces']), 1)
        self.assertGreater(len(state['action_log']), 0)
        
        # Verify piece data
        piece_state = state['pieces'][0]
        self.assertEqual(piece_state['piece_name'], 'Test Piece')
        self.assertEqual(piece_state['color'], 'white')
        self.assertEqual(piece_state['position'], (3, 4))
    
    @patch('tester_engine.piece_manager.load_piece_by_name')
    def test_load_board_state(self, mock_load_piece):
        """Test loading board state"""
        mock_load_piece.return_value = (self.mock_piece_data, None)
        
        # Create test state
        test_state = {
            'version': '1.0.0',
            'turn_number': 3,
            'pieces': [{
                'instance_id': 'test_piece_34_123456',
                'piece_name': 'Test Piece',
                'color': 'white',
                'position': [3, 4],
                'current_points': 4,
                'max_points': 5,
                'cooldowns': {'ability1': 2},
                'abilities_used_this_turn': ['ability2']
            }],
            'action_log': []
        }
        
        # Load state
        success, message = self.engine.load_board_state(test_state)
        
        self.assertTrue(success)
        self.assertEqual(self.engine.turn_number, 3)
        self.assertEqual(len(self.engine.board), 1)
        
        # Verify piece loaded correctly
        piece = self.engine.get_piece_at(BoardPosition(3, 4))
        self.assertIsNotNone(piece)
        self.assertEqual(piece.piece_name, 'Test Piece')
        self.assertEqual(piece.current_points, 4)
        self.assertEqual(piece.cooldowns['ability1'], 2)
        self.assertIn('ability2', piece.abilities_used_this_turn)
    
    def test_get_statistics(self):
        """Test getting board statistics"""
        stats = self.engine.get_statistics()
        
        self.assertIn('turn_number', stats)
        self.assertIn('total_pieces', stats)
        self.assertIn('white_pieces', stats)
        self.assertIn('black_pieces', stats)
        self.assertEqual(stats['turn_number'], 1)
        self.assertEqual(stats['total_pieces'], 0)
    
    def test_movement_validation(self):
        """Test movement validation logic"""
        position = BoardPosition(3, 4)
        piece = PieceInstance(
            piece_name="Test Piece",
            color=PieceColor.WHITE,
            position=position,
            piece_data={"movement": {"type": "orthogonal", "distance": 1}}
        )
        
        # Valid orthogonal moves
        self.assertTrue(self.engine._is_valid_move(piece, position, BoardPosition(3, 5)))
        self.assertTrue(self.engine._is_valid_move(piece, position, BoardPosition(4, 4)))
        
        # Invalid diagonal move for orthogonal piece
        self.assertFalse(self.engine._is_valid_move(piece, position, BoardPosition(4, 5)))
        
        # Test diagonal piece
        piece.piece_data = {"movement": {"type": "diagonal", "distance": 1}}
        self.assertTrue(self.engine._is_valid_move(piece, position, BoardPosition(4, 5)))
        self.assertFalse(self.engine._is_valid_move(piece, position, BoardPosition(3, 5)))

if __name__ == '__main__':
    unittest.main()