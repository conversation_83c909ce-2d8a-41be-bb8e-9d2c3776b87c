#!/usr/bin/env python3
"""
Comprehensive test runner for Adventure Chess
Automatically discovers and runs all tests in the tests directory
"""

import unittest
import sys
import os

# Add the project root to the path
project_root = os.path.dirname(os.path.dirname(__file__))
sys.path.insert(0, project_root)

def discover_and_run_tests():
    """Discover and run all tests in the tests directory"""
    
    # Discover tests
    loader = unittest.TestLoader()
    start_dir = os.path.dirname(__file__)  # tests directory
    suite = loader.discover(start_dir, pattern='test_*.py')
    
    # Run tests
    runner = unittest.TextTestRunner(
        verbosity=2,
        buffer=True,
        failfast=False
    )
    
    print("🧪 Adventure Chess - Test Suite")
    print("=" * 50)
    print(f"Discovering tests in: {start_dir}")
    print("=" * 50)
    
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped) if hasattr(result, 'skipped') else 0}")
    
    if result.failures:
        print(f"\n❌ FAILURES ({len(result.failures)}):")
        for test, traceback in result.failures:
            print(f"  • {test}")
    
    if result.errors:
        print(f"\n💥 ERRORS ({len(result.errors)}):")
        for test, traceback in result.errors:
            print(f"  • {test}")
    
    if result.testsRun > 0:
        success_rate = (result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100
        print(f"\n📈 Success rate: {success_rate:.1f}%")
    
    if result.wasSuccessful():
        print("✅ All tests passed!")
        return True
    else:
        print("❌ Some tests failed!")
        return False

def list_available_tests():
    """List all available test files"""
    tests_dir = os.path.dirname(__file__)
    test_files = [f for f in os.listdir(tests_dir) if f.startswith('test_') and f.endswith('.py')]
    
    print("📋 Available Test Files:")
    print("=" * 30)
    for test_file in sorted(test_files):
        print(f"  • {test_file}")
    print(f"\nTotal: {len(test_files)} test files")

if __name__ == '__main__':
    # Setup logging to suppress debug messages during testing
    import logging
    logging.getLogger().setLevel(logging.WARNING)
    
    if len(sys.argv) > 1 and sys.argv[1] == '--list':
        list_available_tests()
    else:
        success = discover_and_run_tests()
        sys.exit(0 if success else 1)