#!/usr/bin/env python3
"""
Validation Engine for Adventure Chess Rule Engine
Handles comprehensive validation with flexible tag support
"""

from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
from enum import Enum
import logging

from schemas import Ability
from .game_state import EnhancedGameState, ActionContext


class ValidationLevel(Enum):
    """Validation strictness levels"""
    PERMISSIVE = "permissive"  # Allow maximum flexibility during editing
    STANDARD = "standard"     # Normal gameplay validation
    STRICT = "strict"         # Tournament-level validation


@dataclass
class ValidationResult:
    """Result of validation with detailed information"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    suggestions: List[str] = field(default_factory=list)
    conflict_details: Dict[str, Any] = field(default_factory=dict)
    
    def add_error(self, message: str):
        """Add an error message"""
        self.errors.append(message)
        self.is_valid = False
    
    def add_warning(self, message: str):
        """Add a warning message"""
        self.warnings.append(message)
    
    def add_suggestion(self, message: str):
        """Add a suggestion message"""
        self.suggestions.append(message)


class ConflictResolver:
    """
    Resolves conflicts between ability tags based on field_ref_completed.md
    Implements the conflict matrix from the user's requirements
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._initialize_conflict_rules()
    
    def _initialize_conflict_rules(self):
        """Initialize conflict resolution rules from field reference"""
        self.conflict_rules = {
            # Movement vs Immobilization conflicts
            "movement_immobilization": {
                "conflicting_tags": ["immobilize", "move", "teleport", "carryPiece", "swapPlaces"],
                "resolution": "block_movement_when_immobilized",
                "exceptions": ["forced_movement_from_third_party"]
            },
            
            # Passive vs Manual Activation conflicts
            "activation_mode": {
                "conflicting_conditions": [
                    ("activation_mode", "auto"),
                    ("cost", ">0"),
                    ("activation_mode", "manual")
                ],
                "resolution": "auto_with_cost_becomes_manual"
            },
            
            # Visibility Logic conflicts
            "visibility": {
                "conflicting_tags": ["invisible", "alwaysVisible", "revealOnEnemyLoS", "revealOnMove", "revealOnAction"],
                "resolution": "single_visibility_state",
                "priority_order": ["alwaysVisible", "invisible", "reveal_conditions"]
            },
            
            # Mortality Logic conflicts
            "mortality": {
                "conflicting_tags": ["selfSacrifice", "cannotDie", "revival.selfRevive"],
                "resolution": "immortal_cannot_sacrifice"
            },
            
            # Shared Capture Logic conflicts
            "shared_capture": {
                "conflicting_tags": ["trapTile.capture", "shareSpace", "noTurnCost", "carryPiece"],
                "resolution": "all_units_destroyed_unless_selective_survival"
            },
            
            # Execution Timing conflicts
            "execution_timing": {
                "conflicting_tags": ["reaction", "delay", "pulseEffect", "triggeredAction"],
                "resolution": "explicit_execution_order",
                "order": ["delay", "reaction", "pulseEffect"]
            },
            
            # Repeatable vs One-Off conflicts
            "repeatable": {
                "conflicting_tags": ["pulseEffect", "cost", "cooldown"],
                "resolution": "cancel_if_insufficient_points",
                "check_frequency": "once_per_turn"
            },
            
            # Position Anchoring conflicts
            "position_anchoring": {
                "conflicting_tags": ["requiresStartingPosition", "teleport", "displacePiece"],
                "resolution": "break_condition_when_moved"
            }
        }
    
    def check_tag_conflicts(self, tags: List[str], ability_data: Dict[str, Any] = None) -> ValidationResult:
        """
        Check for conflicts between ability tags
        Supports user's requirement for flexible tag selection with save-time validation
        """
        result = ValidationResult(is_valid=True)
        
        if not tags:
            return result
        
        # Check each conflict rule
        for conflict_name, rule in self.conflict_rules.items():
            conflict_found = self._check_specific_conflict(tags, rule, ability_data)
            if conflict_found:
                result.conflict_details[conflict_name] = conflict_found
                
                # Add appropriate message based on conflict severity
                if rule.get("severity", "error") == "error":
                    result.add_error(f"Conflict detected: {conflict_found['message']}")
                else:
                    result.add_warning(f"Potential conflict: {conflict_found['message']}")
        
        return result
    
    def _check_specific_conflict(self, tags: List[str], rule: Dict[str, Any], 
                               ability_data: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Check a specific conflict rule"""
        conflicting_tags = rule.get("conflicting_tags", [])
        
        # Check if multiple conflicting tags are present
        present_conflicts = [tag for tag in tags if tag in conflicting_tags]
        
        if len(present_conflicts) > 1:
            return {
                "message": f"Conflicting tags found: {', '.join(present_conflicts)}",
                "tags": present_conflicts,
                "resolution": rule.get("resolution"),
                "suggestions": self._get_conflict_suggestions(rule, present_conflicts)
            }
        
        # Check conditional conflicts (like activation mode + cost)
        if "conflicting_conditions" in rule:
            return self._check_conditional_conflicts(rule, tags, ability_data)
        
        return None
    
    def _check_conditional_conflicts(self, rule: Dict[str, Any], tags: List[str], 
                                   ability_data: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """Check conflicts based on conditions rather than just tag presence"""
        if not ability_data:
            return None
        
        conditions = rule.get("conflicting_conditions", [])
        met_conditions = []
        
        for condition in conditions:
            field, operator, *value = condition if len(condition) > 2 else (condition[0], condition[1], None)
            
            if self._evaluate_condition(ability_data, field, operator, value):
                met_conditions.append(condition)
        
        if len(met_conditions) > 1:
            return {
                "message": f"Conflicting conditions: {met_conditions}",
                "conditions": met_conditions,
                "resolution": rule.get("resolution")
            }
        
        return None
    
    def _evaluate_condition(self, data: Dict[str, Any], field: str, operator: str, value: Any = None) -> bool:
        """Evaluate a single condition"""
        field_value = data.get(field)
        
        if operator == "auto":
            return field_value == "auto"
        elif operator == ">0":
            return isinstance(field_value, (int, float)) and field_value > 0
        elif operator == "manual":
            return field_value == "manual"
        
        return False
    
    def _get_conflict_suggestions(self, rule: Dict[str, Any], conflicting_tags: List[str]) -> List[str]:
        """Get suggestions for resolving conflicts"""
        suggestions = []
        resolution = rule.get("resolution", "")
        
        if resolution == "block_movement_when_immobilized":
            suggestions.append("Consider using 'immobilize' OR movement tags, not both")
            suggestions.append("Use 'forcedMovement' tag if third-party movement is intended")
        
        elif resolution == "single_visibility_state":
            suggestions.append("Choose only one visibility state: invisible, alwaysVisible, or reveal conditions")
        
        elif resolution == "auto_with_cost_becomes_manual":
            suggestions.append("Auto abilities with cost > 0 will become manual activation")
        
        return suggestions
    
    def resolve_effect_conflicts(self, effects: List[Dict[str, Any]], 
                               context: ActionContext) -> List[Dict[str, Any]]:
        """
        Resolve conflicts between multiple effects
        Returns prioritized and conflict-free effect list
        """
        if not effects:
            return effects
        
        # Sort effects by priority
        prioritized_effects = sorted(effects, key=lambda e: e.get('priority', 0), reverse=True)
        
        # Apply conflict resolution
        resolved_effects = []
        applied_types = set()
        
        for effect in prioritized_effects:
            effect_type = effect.get('type')
            
            # Check for mutually exclusive effects
            if self._is_mutually_exclusive(effect_type, applied_types):
                self.logger.warning(f"Skipping conflicting effect: {effect_type}")
                continue
            
            resolved_effects.append(effect)
            applied_types.add(effect_type)
        
        return resolved_effects
    
    def _is_mutually_exclusive(self, effect_type: str, applied_types: Set[str]) -> bool:
        """Check if an effect type conflicts with already applied effects"""
        exclusive_groups = [
            {"move", "teleport", "immobilize"},  # Movement effects
            {"invisible", "alwaysVisible"},      # Visibility effects
            {"capture", "protect"}               # Survival effects
        ]
        
        for group in exclusive_groups:
            if effect_type in group and any(t in applied_types for t in group):
                return True
        
        return False


class ValidationEngine:
    """
    Main validation engine supporting flexible tag validation
    Implements user's requirement for permissive editing with save-time validation
    """
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.STANDARD):
        self.logger = logging.getLogger(__name__)
        self.validation_level = validation_level
        self.conflict_resolver = ConflictResolver()
    
    def validate_action(self, game_state: EnhancedGameState, action_request: Any) -> ValidationResult:
        """
        Validate a game action
        Uses flexible validation based on current validation level
        """
        result = ValidationResult(is_valid=True)
        
        # Basic input validation
        if not self._validate_basic_input(action_request, result):
            return result
        
        # Validate piece existence and ownership
        if not self._validate_piece_access(game_state, action_request, result):
            return result
        
        # Validate action-specific requirements
        if action_request.action_type == "ability":
            self._validate_ability_action(game_state, action_request, result)
        elif action_request.action_type == "move":
            self._validate_move_action(game_state, action_request, result)
        
        return result
    
    def validate_ability_tags(self, tags: List[str], ability_data: Dict[str, Any] = None, 
                            validation_level: ValidationLevel = None) -> ValidationResult:
        """
        Validate ability tags with flexible strictness
        Supports user's requirement for maximum tag selection during editing
        """
        level = validation_level or self.validation_level
        result = ValidationResult(is_valid=True)
        
        if level == ValidationLevel.PERMISSIVE:
            # During editing - allow maximum flexibility
            # Only check for critical errors that would break the system
            critical_conflicts = self._check_critical_conflicts_only(tags, ability_data)
            if critical_conflicts:
                result.add_warning("Some tag combinations may cause issues during gameplay")
                result.conflict_details.update(critical_conflicts)
        
        elif level == ValidationLevel.STANDARD:
            # During save - perform comprehensive validation
            conflict_result = self.conflict_resolver.check_tag_conflicts(tags, ability_data)
            result.errors.extend(conflict_result.errors)
            result.warnings.extend(conflict_result.warnings)
            result.conflict_details.update(conflict_result.conflict_details)
            if conflict_result.errors:
                result.is_valid = False
        
        elif level == ValidationLevel.STRICT:
            # Tournament mode - strictest validation
            conflict_result = self.conflict_resolver.check_tag_conflicts(tags, ability_data)
            # Treat warnings as errors in strict mode
            result.errors.extend(conflict_result.errors)
            result.errors.extend(conflict_result.warnings)
            result.conflict_details.update(conflict_result.conflict_details)
            if conflict_result.errors or conflict_result.warnings:
                result.is_valid = False
        
        return result
    
    def _check_critical_conflicts_only(self, tags: List[str], ability_data: Dict[str, Any] = None) -> Dict[str, Any]:
        """Check only critical conflicts that would break the game"""
        critical_conflicts = {}
        
        # Only check for system-breaking conflicts during editing
        if "immobilize" in tags and any(tag in tags for tag in ["move", "teleport"]):
            critical_conflicts["movement_immobilization"] = {
                "message": "Movement and immobilization tags may conflict during gameplay"
            }
        
        return critical_conflicts
    
    def _validate_basic_input(self, action_request: Any, result: ValidationResult) -> bool:
        """Validate basic action request structure"""
        if not hasattr(action_request, 'piece_id') or not action_request.piece_id:
            result.add_error("Action request must specify a piece_id")
            return False
        
        if not hasattr(action_request, 'action_type') or not action_request.action_type:
            result.add_error("Action request must specify an action_type")
            return False
        
        return True
    
    def _validate_piece_access(self, game_state: EnhancedGameState, action_request: Any, 
                             result: ValidationResult) -> bool:
        """Validate piece exists and can be controlled"""
        piece = game_state.get_piece(action_request.piece_id)
        if not piece:
            result.add_error(f"Piece {action_request.piece_id} not found")
            return False
        
        if piece.owner != game_state.current_player:
            result.add_error("Cannot control opponent's pieces")
            return False
        
        return True
    
    def _validate_ability_action(self, game_state: EnhancedGameState, action_request: Any, 
                               result: ValidationResult):
        """Validate ability usage"""
        piece = game_state.get_piece(action_request.piece_id)
        context = ActionContext(game_state=game_state, action_request=action_request,
                              current_player=game_state.current_player, 
                              turn_counter=game_state.turn_counter)
        
        can_use, reason, info = piece.can_use_ability_enhanced(action_request.ability_name, context)
        if not can_use:
            result.add_error(reason)
    
    def _validate_move_action(self, game_state: EnhancedGameState, action_request: Any, 
                            result: ValidationResult):
        """Validate movement action"""
        # Basic movement validation - will be enhanced in movement system phase
        if not action_request.target_position:
            result.add_error("Move action requires target_position")
            return
        
        if not game_state.is_valid_position(action_request.target_position):
            result.add_error("Target position is outside board bounds")
