#!/usr/bin/env python3
"""
Event System for Adventure Chess Rule Engine
Handles reactions, triggers, and event-driven gameplay
"""

from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
import logging
import uuid
from datetime import datetime


class EventType(Enum):
    """Types of game events that can trigger reactions"""
    # Movement events
    PIECE_MOVED = "piece_moved"
    PIECE_TELEPORTED = "piece_teleported"
    
    # Combat events
    PIECE_CAPTURED = "piece_captured"
    PIECE_ATTACKED = "piece_attacked"
    PIECE_TARGETED = "piece_targeted"
    
    # Ability events
    ABILITY_USED = "ability_used"
    ABILITY_ACTIVATED = "ability_activated"
    EFFECT_APPLIED = "effect_applied"
    
    # State events
    PIECE_SUMMONED = "piece_summoned"
    PIECE_REVIVED = "piece_revived"
    PIECE_TRANSFORMED = "piece_transformed"
    
    # Turn events
    TURN_STARTED = "turn_started"
    TURN_ENDED = "turn_ended"
    PHASE_CHANGED = "phase_changed"
    
    # Position events
    POSITION_ENTERED = "position_entered"
    POSITION_LEFT = "position_left"
    ADJACENT_PIECE_MOVED = "adjacent_piece_moved"
    
    # Status events
    STATUS_EFFECT_APPLIED = "status_effect_applied"
    STATUS_EFFECT_REMOVED = "status_effect_removed"
    POINTS_CHANGED = "points_changed"


@dataclass
class GameEvent:
    """Represents a game event that can trigger reactions"""
    event_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: EventType = EventType.ABILITY_USED
    source_piece_id: Optional[str] = None
    target_piece_id: Optional[str] = None
    source_position: Optional[Any] = None  # Coordinate
    target_position: Optional[Any] = None  # Coordinate
    ability_name: Optional[str] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    turn_number: int = 0
    phase: str = "main"
    
    def matches_trigger(self, trigger: 'ReactionTrigger') -> bool:
        """Check if this event matches a reaction trigger"""
        # Check event type
        if trigger.event_type != self.event_type:
            return False
        
        # Check source piece filter
        if trigger.source_piece_filter and self.source_piece_id not in trigger.source_piece_filter:
            return False
        
        # Check target piece filter
        if trigger.target_piece_filter and self.target_piece_id not in trigger.target_piece_filter:
            return False
        
        # Check ability filter
        if trigger.ability_filter and self.ability_name not in trigger.ability_filter:
            return False
        
        # Check custom conditions
        if trigger.custom_condition and not trigger.custom_condition(self):
            return False
        
        return True


@dataclass
class ReactionTrigger:
    """Defines when a reaction should trigger"""
    trigger_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    event_type: EventType = EventType.ABILITY_USED
    source_piece_filter: Optional[List[str]] = None  # Piece IDs or types
    target_piece_filter: Optional[List[str]] = None  # Piece IDs or types
    ability_filter: Optional[List[str]] = None       # Ability names
    position_filter: Optional[List[Any]] = None      # Coordinates
    custom_condition: Optional[Callable[[GameEvent], bool]] = None
    
    # Reaction details
    reaction_ability: str = ""
    reaction_piece_id: str = ""
    priority: int = 0  # Higher priority reactions trigger first
    uses_remaining: int = -1  # -1 for unlimited uses
    cooldown_turns: int = 0
    last_triggered_turn: int = -1


@dataclass
class PendingReaction:
    """A reaction waiting to be processed"""
    trigger: ReactionTrigger
    triggering_event: GameEvent
    reaction_piece_id: str
    reaction_ability: str
    reaction_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    target_override: Optional[Any] = None  # Override target for reaction
    delay_turns: int = 0  # Delay before reaction triggers
    created_turn: int = 0


class EventSystem:
    """
    Manages game events and reaction processing
    Supports complex trigger conditions and reaction chains
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Event tracking
        self.event_history: List[GameEvent] = []
        self.active_triggers: Dict[str, ReactionTrigger] = {}
        self.pending_reactions: List[PendingReaction] = []
        
        # Event listeners for debugging/logging
        self.event_listeners: Dict[EventType, List[Callable]] = {}
        
        # Reaction processing
        self.max_reaction_chain_depth = 10  # Prevent infinite loops
        self.current_chain_depth = 0
        
        self.logger.info("EventSystem initialized")
    
    def register_trigger(self, trigger: ReactionTrigger):
        """Register a reaction trigger"""
        self.active_triggers[trigger.trigger_id] = trigger
        self.logger.debug(f"Registered trigger {trigger.trigger_id} for {trigger.event_type}")
    
    def unregister_trigger(self, trigger_id: str):
        """Unregister a reaction trigger"""
        if trigger_id in self.active_triggers:
            del self.active_triggers[trigger_id]
            self.logger.debug(f"Unregistered trigger {trigger_id}")
    
    def emit_event(self, event: GameEvent) -> List[PendingReaction]:
        """
        Emit a game event and return any triggered reactions
        """
        self.event_history.append(event)
        self.logger.debug(f"Event emitted: {event.event_type} from {event.source_piece_id}")
        
        # Notify listeners
        self._notify_listeners(event)
        
        # Check for triggered reactions
        triggered_reactions = []
        for trigger in self.active_triggers.values():
            if self._should_trigger_reaction(event, trigger):
                reaction = self._create_pending_reaction(event, trigger)
                if reaction:
                    triggered_reactions.append(reaction)
                    self.pending_reactions.append(reaction)
        
        return triggered_reactions
    
    def process_pending_reactions(self, game_state: Any) -> List[Dict[str, Any]]:
        """
        Process all pending reactions and return the effects
        """
        if self.current_chain_depth >= self.max_reaction_chain_depth:
            self.logger.warning("Maximum reaction chain depth reached, stopping processing")
            return []
        
        self.current_chain_depth += 1
        
        try:
            processed_effects = []
            reactions_to_process = [r for r in self.pending_reactions if r.delay_turns <= 0]
            
            # Sort by priority
            reactions_to_process.sort(key=lambda r: r.trigger.priority, reverse=True)
            
            for reaction in reactions_to_process:
                effects = self._process_single_reaction(game_state, reaction)
                processed_effects.extend(effects)
                
                # Remove processed reaction
                self.pending_reactions.remove(reaction)
                
                # Update trigger usage
                self._update_trigger_usage(reaction.trigger, game_state.turn_counter)
            
            return processed_effects
            
        finally:
            self.current_chain_depth -= 1
    
    def tick_pending_reactions(self):
        """Reduce delay on pending reactions"""
        for reaction in self.pending_reactions:
            if reaction.delay_turns > 0:
                reaction.delay_turns -= 1
    
    def trigger_reactions(self, game_state: Any, state_changes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Trigger reactions based on state changes
        This is the main entry point called by the rule engine
        """
        triggered_effects = []
        
        # Convert state changes to events
        events = self._state_changes_to_events(state_changes, game_state)
        
        # Emit events and collect reactions
        for event in events:
            self.emit_event(event)
        
        # Process immediate reactions
        immediate_effects = self.process_pending_reactions(game_state)
        triggered_effects.extend(immediate_effects)
        
        return triggered_effects
    
    def _should_trigger_reaction(self, event: GameEvent, trigger: ReactionTrigger) -> bool:
        """Check if an event should trigger a reaction"""
        # Check if event matches trigger
        if not event.matches_trigger(trigger):
            return False
        
        # Check cooldown
        if trigger.cooldown_turns > 0:
            turns_since_last = event.turn_number - trigger.last_triggered_turn
            if turns_since_last < trigger.cooldown_turns:
                return False
        
        # Check uses remaining
        if trigger.uses_remaining == 0:
            return False
        
        return True
    
    def _create_pending_reaction(self, event: GameEvent, trigger: ReactionTrigger) -> Optional[PendingReaction]:
        """Create a pending reaction from an event and trigger"""
        # Validate reaction piece exists
        # This would check game state in real implementation
        
        return PendingReaction(
            trigger=trigger,
            triggering_event=event,
            reaction_piece_id=trigger.reaction_piece_id,
            reaction_ability=trigger.reaction_ability,
            created_turn=event.turn_number
        )
    
    def _process_single_reaction(self, game_state: Any, reaction: PendingReaction) -> List[Dict[str, Any]]:
        """Process a single reaction and return its effects"""
        self.logger.debug(f"Processing reaction: {reaction.reaction_ability} from {reaction.reaction_piece_id}")
        
        # This would integrate with the rule engine to execute the reaction ability
        # For now, return a placeholder effect
        return [{
            'type': 'reaction',
            'piece_id': reaction.reaction_piece_id,
            'ability': reaction.reaction_ability,
            'triggered_by': reaction.triggering_event.event_id
        }]
    
    def _update_trigger_usage(self, trigger: ReactionTrigger, current_turn: int):
        """Update trigger usage tracking"""
        if trigger.uses_remaining > 0:
            trigger.uses_remaining -= 1
        
        trigger.last_triggered_turn = current_turn
        
        # Remove trigger if no uses remaining
        if trigger.uses_remaining == 0:
            self.unregister_trigger(trigger.trigger_id)
    
    def _state_changes_to_events(self, state_changes: List[Dict[str, Any]], game_state: Any) -> List[GameEvent]:
        """Convert state changes to game events"""
        events = []
        
        for change in state_changes:
            change_type = change.get('type')
            
            if change_type == 'move':
                event = GameEvent(
                    event_type=EventType.PIECE_MOVED,
                    source_piece_id=change.get('piece_id'),
                    source_position=change.get('from_position'),
                    target_position=change.get('to_position'),
                    turn_number=game_state.turn_counter
                )
                events.append(event)
            
            elif change_type == 'capture':
                event = GameEvent(
                    event_type=EventType.PIECE_CAPTURED,
                    source_piece_id=change.get('piece_id'),
                    target_piece_id=change.get('captured_piece'),
                    target_position=change.get('target_position'),
                    turn_number=game_state.turn_counter
                )
                events.append(event)
            
            elif change_type == 'ability_used':
                event = GameEvent(
                    event_type=EventType.ABILITY_USED,
                    source_piece_id=change.get('piece_id'),
                    ability_name=change.get('ability_name'),
                    target_position=change.get('target_position'),
                    turn_number=game_state.turn_counter
                )
                events.append(event)
        
        return events
    
    def _notify_listeners(self, event: GameEvent):
        """Notify registered event listeners"""
        listeners = self.event_listeners.get(event.event_type, [])
        for listener in listeners:
            try:
                listener(event)
            except Exception as e:
                self.logger.error(f"Error in event listener: {str(e)}")
    
    def add_event_listener(self, event_type: EventType, listener: Callable[[GameEvent], None]):
        """Add an event listener for debugging or logging"""
        if event_type not in self.event_listeners:
            self.event_listeners[event_type] = []
        self.event_listeners[event_type].append(listener)
    
    def remove_event_listener(self, event_type: EventType, listener: Callable[[GameEvent], None]):
        """Remove an event listener"""
        if event_type in self.event_listeners:
            try:
                self.event_listeners[event_type].remove(listener)
            except ValueError:
                pass
    
    def get_recent_events(self, count: int = 10, event_type: Optional[EventType] = None) -> List[GameEvent]:
        """Get recent events for debugging"""
        events = self.event_history
        
        if event_type:
            events = [e for e in events if e.event_type == event_type]
        
        return events[-count:] if events else []
    
    def clear_history(self):
        """Clear event history (for memory management)"""
        self.event_history.clear()
        self.logger.debug("Event history cleared")
