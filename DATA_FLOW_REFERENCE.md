!! The Goal of these editors are to provide customization options to the creator, all fields refresh on being deselected. information uses similar input fields or common definitions. saving a file updates it to hold only the currently filled in or activated ability tags. Use this as a back and forth tool measuring twice and cutting once to ensure proper handling of all data. Keep this File up to date, Clean, Organized, descriptive, and empowering a perfect mirror to what we are creating !!

!! Follow our UI (editors/ability_editor, piece_editor) as it is the cannon for fields we are required to be able to understand, edit, save, and load. !!

!! Do not add validation to the saving or loading of info in the piece tester. maintain the ability that saving a piece updates it to only hold currently selected fields. !!

---

# 🎯 **ADVENTURE CHESS DATA FLOW REFERENCE**
## Complete Data Lifecycle Management Documentation

> **Date**: 2025-06-23
> **Current Architecture**: SimpleBridge with DirectDataManager

---

## 🏗️ **CURRENT ARCHITECTURE OVERVIEW**

### **🔧 DATA MANAGEMENT STACK**

```
┌─────────────────────────────────────────────────────────────┐
│                    UI LAYER (Editors)                      │
│  ┌─────────────────┐    ┌─────────────────┐               │
│  │ AbilityEditor   │    │ PieceEditor     │               │
│  │ (BaseEditor)    │    │ (BaseEditor)    │               │
│  └─────────────────┘    └─────────────────┘               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                DATA INTERFACE LAYER                        │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ EditorDataInterface (Universal Data Collection)        │ │
│  │ • collect_data_from_ui() - Complete field mappings    │ │
│  │ • populate_ui_from_data() - Universal widget setting  │ │
│  │ • Unified state management                            │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                    BRIDGE LAYER                           │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ SimpleBridge (Direct JSON Operations)                 │ │
│  │ • save_*_from_ui() - Direct save operations           │ │
│  │ • load_*_for_ui() - Direct load operations            │ │
│  │ • list_*() - File listing operations                  │ │
│  │ • No validation, no migration - pure JSON operations  │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                  STORAGE LAYER                             │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │ DirectDataManager → JSON Files                        │ │
│  │ • data/abilities/*.json                               │ │
│  │ • data/pieces/*.json                                  │ │
│  │ • Direct JSON read/write operations                   │ │
│  │ • Consistent field naming (camelCase)                 │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

---

## 🔄 **DATA LIFECYCLE FLOWS**

### **📁 JSON → UI LOADING FLOW**

```
JSON File → DirectDataManager → SimpleBridge → EditorDataInterface → UI Widgets
    ↓              ↓                ↓              ↓                  ↓
File Read → Direct Parse → Dict Return → Field Mapping → Widget Population
```

**Detailed Steps**:
1. **File Discovery**: `simple_bridge.list_*()` scans directories directly
2. **File Loading**: `simple_bridge.load_*_for_ui(filename)` loads JSON directly
3. **Direct Parsing**: Direct JSON to dict conversion
4. **Widget Population**: `EditorDataInterface.populate_ui_from_data()` sets all widgets
5. **State Management**: `BaseEditor.clear_unsaved_changes()` resets state

### **✏️ UI EDITING & STATE MANAGEMENT FLOW**

```
Widget Change → Event Handler → BaseEditor.mark_unsaved_changes() → Window Title "*"
     ↓              ↓                        ↓                           ↓
User Input → Signal Emission → State Tracking → Visual Feedback
```

**Change Tracking**:
- **Automatic**: All widget changes trigger `mark_unsaved_changes()`
- **Visual**: Window title shows "*" for unsaved changes
- **Consistent**: Same pattern across all editors

### **💾 UI → JSON SAVING FLOW**

```
UI Widgets → EditorDataInterface → BaseEditor → SimpleBridge → JSON File
    ↓              ↓                   ↓            ↓             ↓
Widget Values → Field Collection → Save Trigger → Direct Save → File Write
```

**Detailed Steps**:
1. **Data Collection**: `EditorDataInterface.collect_data_from_ui()` gathers all fields
2. **Direct Save**: `simple_bridge.save_*_from_ui()` saves directly to JSON
3. **File Writing**: `DirectDataManager.save_*()` writes to JSON
4. **State Reset**: `BaseEditor.clear_unsaved_changes()` clears dirty flag

---

## 🎯 **SYSTEM COMPONENTS**

### **🔧 EditorDataInterface** (`utils/editor_data_interface.py`)

**Universal Data Collection**:
- **Complete Field Mappings**: Coverage of ALL UI widgets in both editors
- **Type-Safe Collection**: Automatic type conversion
- **Nested Field Support**: Handles complex fields like `movement.pattern` and `movement.piecePosition`
- **Quick Option Buttons**: Movement pattern buttons generate and store complete pattern data
- **Pattern-Only Movement**: All movement data stored as patterns
- **Pattern Editor Integration**: Single source of truth for all movement pattern data
- **Error Handling**: Graceful handling of missing/deleted widgets

**Field Mapping Examples**:
```python
# Ability Editor Mappings
('name_edit', 'name', 'text'),
('cost_spin', 'cost', 'value'),
('summon_max_spin', 'summonMax', 'value'),
('buff_duration_spin', 'buffDuration', 'value'),
('carry_range_spin', 'carryRange', 'value'),
('delay_turn_check', 'delayTurnEnabled', 'checked'),

# Piece Editor Mappings
('name_edit', 'name', 'text'),                           # Basic Info
('desc_edit', 'description', 'plainText'),               # Basic Info
('role_combo', 'role', 'currentText'),                   # Role Selection
('can_castle_check', 'canCastle', 'checked'),           # Role Options
('track_starting_position_check', 'trackStartingPosition', 'checked'),
('color_directional_check', 'colorDirectional', 'checked'),
('can_capture_check', 'canCapture', 'checked'),         # Capture checkbox
('black_icon_combo', 'blackIcon', 'currentText'),       # Icon Selection
('white_icon_combo', 'whiteIcon', 'currentText'),       # Icon Selection
('enable_recharge_check', 'enableRecharge', 'checked'), # Recharge System
('max_points_spin', 'maxPoints', 'value'),
('starting_points_spin', 'startingPoints', 'value'),
('recharge_combo', 'rechargeType', 'currentText'),
('turn_points_spin', 'turnPoints', 'value'),
('committed_recharge_spin', 'committedRechargeTurns', 'value'),
('adjacency_distance_spin', 'adjacencyDistance', 'value'),

# Special Data Collections (Piece Editor)
- Movement Data: current_movement_data → movement (complex pattern storage)
- Abilities List: abilities → abilities (array of ability references)
- Promotion Lists: primary_promotions → promotions, secondary_promotions → secondaryPromotions
- Adjacency Recharge: adjacency_recharge_selector → adjacencyRechargeList
```

**🎯 Quick Option Button System**:
```python
# Movement Pattern Buttons (Piece Editor)
movement_buttons = [
    ("♜", "orthogonal", "Orthogonal lines (like Rook)"),
    ("♝", "diagonal", "Diagonal lines (like Bishop)"),
    ("♛", "any", "All directions (like Queen)"),
    ("♞", "lShape", "L-shaped moves (like Knight)"),
    ("♚", "king", "King movement (1 square any direction)"),
    ("🌐", "global", "Global movement (anywhere on board)"),
    ("🎨", "custom", "Custom pattern (opens pattern editor)")
]

# Automatic Full Data Storage with Single Range Control
def select_movement_preset(movement_type, button):
    # Stores complete movement data structure
    range_value = self.movement_range_spin.value()

    # Auto-adjust range for specific movement types
    if movement_type in ['lShape', 'king']:
        range_value = 1  # Fixed range for L-shape and King
    elif movement_type == 'global':
        range_value = 8  # Max range for Global

    self.current_movement_data = {
        "type": movement_type,
        "distance": range_value,
        "pattern": custom_pattern if custom else None
    }
```

---

## **📋 ABILITY EDITOR DATA FLOW**

### **🎯 Ability Editor Widget Mapping**

```python
# Ability Editor Mappings - Core Fields
# Basic Information
('name_edit', 'name', 'text'),                           # Basic Info
('description_edit', 'description', 'plainText'),        # Basic Info
('cost_spin', 'cost', 'value'),                         # Cost System
('auto_cost_check', 'autoCostCheck', 'checked'),        # Cost System
('activation_combo', 'activationMode', 'currentText'),   # Activation Mode

# Range Configuration (4 fields)
('range_friendly_only_check', 'rangeFriendlyOnly', 'checked'),
('range_enemy_only_check', 'rangeEnemyOnly', 'checked'),
('range_include_start_check', 'rangeIncludeStart', 'checked'),
('range_include_self_check', 'rangeIncludeSelf', 'checked'),

# Area Effect Configuration (2 fields)
('area_size_spin', 'areaSize', 'value'),
('area_shape_combo', 'areaShape', 'currentText'),

# Tag-Specific Configurations
('summon_max_spin', 'summonMax', 'value'),               # Summon Tag
('capture_target_combo', 'captureTarget', 'currentText'), # Capture Tag
('adjacency_distance_spin', 'adjacencyDistance', 'value'), # Adjacency Tag
('no_turn_cost_limit_spin', 'noTurnCostLimit', 'value'), # No Turn Cost Tag

# Additional Configuration Fields (examples)
('share_space_max_spin', 'shareSpaceMax', 'value'),
('displace_direction_combo', 'displaceDirection', 'currentText'),
('buff_duration_spin', 'buffDuration', 'value'),
('carry_range_spin', 'carryRange', 'value'),
('delay_turn_check', 'delayTurnEnabled', 'checked'),
# ... and many more tag-specific configuration fields

# Special Data Collections (Ability Editor)
- Tag Selection: tag_groups → tags (array of selected ability tags)
- Range Pattern: range_pattern → rangeMask (8x8 grid pattern)
- Piece Position: range_piece_position → piecePosition (piece location in pattern)
- Inline Selectors: Multiple piece/ability selectors for complex targeting
```

---

## **🏷️ ABILITY TAGS CONFIGURATION**

### **🎯 Complete Ability Tags Reference (28 Total Tags)**

**Centralized Configuration** - All tags defined in `config.py` ABILITY_TAGS

#### **🟢 ACTION TAGS (15 tags)**
```python
# Primary action abilities that perform specific game actions
("move", "Teleports piece to target square within range")
("summon", "Creates new pieces at target locations")
("revival", "Resurrects destroyed pieces at target locations")
("capture", "Destroys pieces at target locations")
("carryPiece", "Allows piece to carry other pieces while moving")
("swapPlaces", "Exchanges positions with target piece")
("displacePiece", "Pushes target piece in specified direction")
("immobilize", "Prevents piece movement for specified turns")
("convertPiece", "Changes target pieces into different piece types")
("duplicate", "Creates copies of piece at offset positions")
("buffPiece", "Temporarily enhances target pieces")
("debuffPiece", "Temporarily weakens target pieces")
("addObstacle", "Places obstacles on target squares")
("removeObstacle", "Removes obstacles from target squares")
("trapTile", "Creates hidden traps on target squares")
```

#### **🟡 TARGETING TAGS (2 tags)**
```python
# Define how abilities target and affect areas
("range", "Defines targeting area for abilities")
("areaEffect", "Affects multiple squares around target")
```

#### **🔵 CONDITION TAGS (3 tags)**
```python
# Requirements and conditions for ability activation
("adjacencyRequired", "Ability only works when adjacent to specific pieces")
("losRequired", "Requires clear line of sight to target")
("noTurnCost", "Ability doesn't consume turn points")
```

#### **🔶 SPECIAL TAGS (8 tags)**
```python
# Special mechanics and advanced behaviors
("shareSpace", "Multiple pieces can occupy same square")
("delay", "Ability effect occurs after specified turns")
("passThrough", "Can target through other pieces")
("pulseEffect", "Repeating effect that triggers every N turns")
("fogOfWar", "Reveals hidden areas of the board")
("invisible", "Makes piece undetectable under certain conditions")
("reaction", "Triggers automatically in response to events")
("requiresStartingPosition", "Ability only works if piece hasn't moved from starting position")
```

### **🔧 TAG CONFIGURATION FIELDS**

**Each tag provides specific configuration options when selected. Examples:**

- **Summon Tag**: `summon_max_spin` → `summonMax`, `summon_selector` → `summonList`
- **Range Tag**: `range_friendly_only_check` → `rangeFriendlyOnly`, `range_enemy_only_check` → `rangeEnemyOnly`
- **Area Effect Tag**: `area_size_spin` → `areaSize`, `area_shape_combo` → `areaShape`
- **Adjacency Tag**: `adjacency_distance_spin` → `adjacencyDistance`, `adjacency_selector` → `adjacencyList`
- **Buff Tag**: `buff_duration_spin` → `buffDuration`, `buff_target_selector` → `buffTargetList`
- **Carry Tag**: `carry_range_spin` → `carryRange`, `carry_target_selector` → `carryList`
- **Delay Tag**: `delay_turn_check` → `delayTurnEnabled`, `delay_turn_spin` → `delayTurn`

*Each tag has multiple configuration fields that control its specific behavior and targeting options.*

---

## **🔄 ABILITY TAGS DATA FLOW**

### **Data Flow Process**

**UI Selection → EditorDataInterface → SimpleBridge → JSON → Load Back → UI Population**

```python
# Tag Selection and Storage
editor.tag_groups = {
    'move': QCheckBox,
    'summon': QCheckBox,
    'range': QCheckBox,
    # ... all 28 tags
}

# Data Collection
def collect_data_from_ui(editor_instance, "ability"):
    tags = []
    for tag, checkbox in editor_instance.tag_groups.items():
        if checkbox.isChecked():
            tags.append(tag)
    data['tags'] = tags

# JSON Storage Format
{
    "name": "TestAbility",
    "tags": ["move", "summon", "range", "adjacencyRequired", "shareSpace"]
}

# UI Population
def populate_ui_from_data(editor_instance, data, "ability"):
    for tag, checkbox in editor_instance.tag_groups.items():
        checkbox.blockSignals(True)  # Prevent interference
        checkbox.setChecked(tag in data['tags'])
        checkbox.blockSignals(False)
```

### **🎯 TAG CATEGORIES BREAKDOWN**

- **Action Tags (15)**: move, summon, revival, capture, carryPiece, swapPlaces, displacePiece, immobilize, convertPiece, duplicate, buffPiece, debuffPiece, addObstacle, removeObstacle, trapTile
- **Targeting Tags (2)**: range, areaEffect
- **Condition Tags (3)**: adjacencyRequired, losRequired, noTurnCost
- **Special Tags (8)**: shareSpace, delay, passThrough, pulseEffect, fogOfWar, invisible, reaction, requiresStartingPosition

---

## **🏗️ SYSTEM COMPONENTS**

### **🏗️ BaseEditor** (`editors/base_editor.py`)

**Standardized Editor Operations**:
- **Universal File Operations**: `new_data()`, `load_data()`, `save_data()`, `save_as_data()`
- **Consistent State Management**: `mark_unsaved_changes()`, `clear_unsaved_changes()`
- **SimpleBridge Integration**: Uses `simple_bridge` for all operations
- **Automatic Change Tracking**: Widget signal connections

**Abstract Methods** (Implemented by subclasses):
```python
def reset_form(self):           # Reset to default values
def post_load_update(self):     # Update UI after loading
def post_save_update(self):     # Update UI after saving
def save_as_data(self) -> bool: # Save with new filename
def refresh_file_lists(self):   # Refresh dropdowns/lists
```

### **🔧 SimpleBridge** (`utils/simple_bridge.py`)

**Direct JSON Operations**:
- **Used by**: ALL COMPONENTS - BaseEditor, PieceEditor, AbilityEditor, PieceTester, PieceAbilityManager, AdjacencyDialog, TesterEngine
- **Direct Save Operations**: `save_ability_from_ui()`, `save_piece_from_ui()`
- **Direct Load Operations**: `load_ability_for_ui()`, `load_piece_for_ui()`
- **File Listing**: `list_abilities()`, `list_pieces()`
- **No Migration**: Direct JSON read/write without schema validation
- **No Validation**: Pure data operations for maximum flexibility

### **📁 DirectDataManager** (`utils/direct_data_manager.py`)

**File Operations**:
- **Direct JSON Save/Load**: No validation or migration overhead
- **Directory Management**: Ensures `data/pieces/` and `data/abilities/` exist
- **File Naming**: Automatic filename cleaning and `.json` extension handling
- **Error Handling**: Returns success/error tuples for all operations

---

## **🚀 DATA FLOW EXAMPLES**

### **Creating New Ability**:
```python
# 1. User clicks "New Ability"
ability_editor.new_data()
    → BaseEditor.new_data()
    → ability_editor.reset_form()
    → EditorDataInterface sets default values
    → UI shows clean form

# 2. User fills in fields and saves
ability_editor.save_data()
    → BaseEditor.save_data()
    → EditorDataInterface.collect_data_from_ui()
    → SimpleBridge.save_ability_from_ui()
    → DirectDataManager.save_ability()
    → JSON file written
    → UI state cleared
```

### **Loading Existing Piece**:
```python
# 1. User selects piece from dropdown
piece_editor.load_data("knight")
    → BaseEditor.load_data()
    → SimpleBridge.load_piece_for_ui()
    → DirectDataManager.load_piece()
    → EditorDataInterface.populate_ui_from_data()
    → All widgets populated
    → UI state cleared
```

### **Editing and Change Tracking**:
```python
# 1. User changes widget value
widget.valueChanged.emit()
    → BaseEditor.mark_unsaved_changes()
    → Window title shows "*"
    → State tracked consistently
```

---

## **🎯 MAINTENANCE GUIDELINES**

### **System Maintenance**:
1. **Add New Fields**: Update `EditorDataInterface` field mappings
2. **Add New Tags**: Add to `config.py` ABILITY_TAGS, update UI
3. **UI Changes**: Ensure widgets follow naming conventions for auto-mapping

### **Quality Assurance**:
- **Single Source of Truth**: All data operations must go through SimpleBridge
- **No Direct JSON**: Never bypass the bridge layer
- **Consistent Patterns**: All editors inherit from BaseEditor
- **Universal Methods**: Use EditorDataInterface for all data operations

### **Data Integrity**:
- **Complete Save/Load Cycle**: All fields properly preserved
- **UI State Management**: Consistent change tracking across editors
- **Error Handling**: Graceful handling of missing widgets or data
- **Field Mapping Coverage**: Every UI widget has proper field mapping
