#!/usr/bin/env python3
"""
Test script for the Adventure Chess Movement System
Validates movement patterns, pathfinding, and validation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from schemas.base import Coordinate, MovementType
from core.tester_engine import PieceColor
from rule_engine.movement import MovementPatternProcessor, MovementValidator, PathfindingEngine
from rule_engine.movement.patterns import PatternType, MovementPattern
from rule_engine.movement.validator import ValidationLevel


def test_basic_movement_patterns():
    """Test basic movement pattern calculations"""
    print("🧪 Testing Basic Movement Patterns...")
    
    processor = MovementPatternProcessor()
    current_pos = Coordinate(row=3, col=3)  # Center of board
    
    # Test orthogonal movement
    piece_data = {
        'movement': {
            'type': 'orthogonal',
            'distance': 2
        },
        'can_capture': True,
        'owner': PieceColor.WHITE
    }
    
    # Mock game state
    class MockGameState:
        def get_piece_at(self, pos):
            return []  # No pieces for basic test
    
    game_state = MockGameState()
    
    result = processor.calculate_valid_moves(piece_data, current_pos, game_state)
    
    print(f"  ✅ Orthogonal movement (distance 2): {len(result.valid_moves)} valid moves")
    print(f"     Valid positions: {[(p.row, p.col) for p in result.valid_moves[:5]]}...")
    
    # Test diagonal movement
    piece_data['movement']['type'] = 'diagonal'
    result = processor.calculate_valid_moves(piece_data, current_pos, game_state)
    
    print(f"  ✅ Diagonal movement (distance 2): {len(result.valid_moves)} valid moves")
    
    # Test L-shape movement
    piece_data['movement']['type'] = 'lShape'
    result = processor.calculate_valid_moves(piece_data, current_pos, game_state)
    
    print(f"  ✅ L-shape movement: {len(result.valid_moves)} valid moves")


def test_custom_pattern_movement():
    """Test custom 8x8 pattern movement"""
    print("\n🧪 Testing Custom Pattern Movement...")
    
    processor = MovementPatternProcessor()
    current_pos = Coordinate(row=3, col=3)
    
    # Create a simple cross pattern
    custom_pattern = [[0 for _ in range(8)] for _ in range(8)]
    
    # Set piece position
    custom_pattern[3][3] = PatternType.EMPTY.value  # Piece position
    
    # Create cross pattern
    for i in range(8):
        if i != 3:
            custom_pattern[3][i] = PatternType.BOTH.value  # Horizontal line
            custom_pattern[i][3] = PatternType.BOTH.value  # Vertical line
    
    piece_data = {
        'movement': {
            'type': 'custom',
            'pattern': custom_pattern,
            'piece_position': [3, 3]
        },
        'can_capture': True,
        'owner': PieceColor.WHITE
    }
    
    class MockGameState:
        def get_piece_at(self, pos):
            return []
    
    game_state = MockGameState()
    
    result = processor.calculate_valid_moves(piece_data, current_pos, game_state)
    
    print(f"  ✅ Custom cross pattern: {len(result.valid_moves)} valid moves")
    print(f"     Sample positions: {[(p.row, p.col) for p in result.valid_moves[:5]]}...")


def test_movement_validation():
    """Test movement validation system"""
    print("\n🧪 Testing Movement Validation...")
    
    validator = MovementValidator(ValidationLevel.STANDARD)
    current_pos = Coordinate(row=1, col=1)
    target_pos = Coordinate(row=1, col=3)
    
    piece_data = {
        'id': 'test_piece',
        'movement': {
            'type': 'orthogonal',
            'distance': 3
        },
        'can_capture': True,
        'owner': PieceColor.WHITE,
        'abilities': []
    }
    
    class MockGameState:
        def get_piece_at(self, pos):
            return []
        
        def get_cell(self, pos):
            class MockCell:
                obstacles = []
            return MockCell()
    
    game_state = MockGameState()
    
    result = validator.validate_movement(piece_data, current_pos, target_pos, game_state)
    
    print(f"  ✅ Basic validation: {'PASS' if result.is_valid else 'FAIL'}")
    if not result.is_valid:
        print(f"     Error: {result.error_message}")
    
    # Test invalid movement (too far)
    far_target = Coordinate(row=1, col=6)  # Distance 5, but piece can only move 3
    result = validator.validate_movement(piece_data, current_pos, far_target, game_state)
    
    print(f"  ✅ Out of range validation: {'PASS' if not result.is_valid else 'FAIL'}")
    if not result.is_valid:
        print(f"     Expected error: {result.error_message}")


def test_pathfinding():
    """Test pathfinding and line-of-sight"""
    print("\n🧪 Testing Pathfinding...")
    
    from rule_engine.movement.pathfinding import PathfindingEngine, LineOfSightCalculator
    
    pathfinder = PathfindingEngine()
    los_calc = LineOfSightCalculator()
    
    start = Coordinate(row=0, col=0)
    end = Coordinate(row=3, col=3)
    
    class MockGameState:
        def get_piece_at(self, pos):
            # Block position (1,1)
            if pos.row == 1 and pos.col == 1:
                return [{'id': 'blocking_piece'}]
            return []
        
        def get_cell(self, pos):
            class MockCell:
                obstacles = []
            return MockCell()
    
    game_state = MockGameState()
    
    # Test direct path (should work for diagonal movement)
    path = pathfinder.find_path(start, end, game_state)
    print(f"  ✅ Direct diagonal path: {len(path)} steps")
    
    # Test line of sight
    los_result = los_calc.has_line_of_sight(start, end, game_state)
    print(f"  ✅ Line of sight: {'CLEAR' if los_result.has_line_of_sight else 'BLOCKED'}")
    if los_result.blocked_by:
        print(f"     Blocked by: {[(p.row, p.col) for p in los_result.blocked_by]}")


def test_movement_modifiers():
    """Test movement modifier system"""
    print("\n🧪 Testing Movement Modifiers...")
    
    from rule_engine.movement.modifiers import MovementModifierEngine, MovementModifier, ModifierType
    
    modifier_engine = MovementModifierEngine()
    
    # Create a range bonus modifier
    range_modifier = MovementModifier(
        modifier_id="test_range_bonus",
        modifier_type=ModifierType.RANGE_BONUS,
        source_ability="speed_boost",
        source_piece_id="caster",
        target_piece_id="test_piece",
        value=2
    )
    
    modifier_engine.add_modifier(range_modifier)
    
    # Test modifier application
    base_movement = {
        'movement': {
            'type': 'orthogonal',
            'distance': 1
        }
    }
    
    current_pos = Coordinate(row=2, col=2)
    modified_movement = modifier_engine.apply_modifiers_to_movement(
        "test_piece", base_movement, current_pos, None
    )
    
    original_distance = base_movement['movement']['distance']
    modified_distance = modified_movement['distance']
    
    print(f"  ✅ Range modifier: {original_distance} → {modified_distance}")
    
    # Test movement restrictions
    can_move, reason = modifier_engine.check_movement_allowed("test_piece")
    print(f"  ✅ Movement allowed: {can_move} ({reason})")
    
    # Add immobilize modifier
    immobilize_modifier = MovementModifier(
        modifier_id="test_immobilize",
        modifier_type=ModifierType.IMMOBILIZE,
        source_ability="hold_person",
        source_piece_id="caster",
        target_piece_id="test_piece"
    )
    
    modifier_engine.add_modifier(immobilize_modifier)
    
    can_move, reason = modifier_engine.check_movement_allowed("test_piece")
    print(f"  ✅ Movement with immobilize: {can_move} ({reason})")


def test_integration():
    """Test integration between all movement components"""
    print("\n🧪 Testing Movement System Integration...")
    
    # This would test the full integration with the rule engine
    # For now, just verify components can work together
    
    processor = MovementPatternProcessor()
    validator = MovementValidator()
    
    # Test that validator can use processor results
    piece_data = {
        'movement': {
            'type': 'orthogonal',
            'distance': 2
        },
        'can_capture': True,
        'owner': PieceColor.WHITE,
        'abilities': []
    }
    
    current_pos = Coordinate(row=2, col=2)
    target_pos = Coordinate(row=2, col=4)
    
    class MockGameState:
        def get_piece_at(self, pos):
            return []
        
        def get_cell(self, pos):
            class MockCell:
                obstacles = []
            return MockCell()
    
    game_state = MockGameState()
    
    # Get valid moves
    movement_result = processor.calculate_valid_moves(piece_data, current_pos, game_state)
    
    # Validate specific movement
    validation_result = validator.validate_movement(piece_data, current_pos, target_pos, game_state)
    
    print(f"  ✅ Integration test: {len(movement_result.valid_moves)} valid moves calculated")
    print(f"  ✅ Validation result: {'PASS' if validation_result.is_valid else 'FAIL'}")
    
    if validation_result.is_valid:
        print(f"     Can move: {validation_result.can_move}")
        print(f"     Can attack: {validation_result.can_attack}")
        print(f"     Movement cost: {validation_result.movement_cost}")


def main():
    """Run all movement system tests"""
    print("🚀 Adventure Chess Movement System Tests")
    print("=" * 50)
    
    try:
        test_basic_movement_patterns()
        test_custom_pattern_movement()
        test_movement_validation()
        test_pathfinding()
        test_movement_modifiers()
        test_integration()
        
        print("\n" + "=" * 50)
        print("✅ All movement system tests completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
