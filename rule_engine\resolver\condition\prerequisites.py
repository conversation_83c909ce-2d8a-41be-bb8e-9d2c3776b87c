#!/usr/bin/env python3
"""
Prerequisite Tag Resolvers for Adventure Chess Rule Engine
Handles prerequisite and restriction tags
"""

from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class RequiresStartingPositionTagResolver(BaseTagResolver):
    """Resolver for requiresStartingPosition tag"""
    
    def _get_tag_name(self) -> str:
        return "requiresStartingPosition"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(
            effect_type="prerequisite",
            priority=255,
            immediate=True,
            parameters={
                "prerequisite_type": "starting_position",
                "required": True
            }
        )]
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> tuple[bool, str]:
        is_valid, message = super().validate_prerequisites(ability, context)
        if not is_valid:
            return is_valid, message
        
        source_piece = context.game_state.get_piece(context.source_piece_id)
        if source_piece and hasattr(source_piece, 'starting_position'):
            current_pos = source_piece.position
            starting_pos = source_piece.starting_position
            
            if (current_pos.row != starting_pos.row or 
                current_pos.col != starting_pos.col):
                return False, "Ability requires piece to be at starting position"
        
        return True, "Prerequisites satisfied"


class OncePerTurnTagResolver(BaseTagResolver):
    """Resolver for oncePerTurn tag"""
    
    def _get_tag_name(self) -> str:
        return "oncePerTurn"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(
            effect_type="usage_restriction",
            priority=250,
            immediate=True,
            parameters={
                "restriction_type": "once_per_turn",
                "scope": "piece"
            }
        )]
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> tuple[bool, str]:
        is_valid, message = super().validate_prerequisites(ability, context)
        if not is_valid:
            return is_valid, message
        
        source_piece = context.game_state.get_piece(context.source_piece_id)
        if source_piece and hasattr(source_piece, 'abilities_used_this_turn'):
            ability_name = ability.name
            if ability_name in source_piece.abilities_used_this_turn:
                return False, f"Ability '{ability_name}' already used this turn"
        
        return True, "Prerequisites satisfied"


class RequiresAllyTagResolver(BaseTagResolver):
    """Resolver for requiresAlly tag"""
    
    def _get_tag_name(self) -> str:
        return "requiresAlly"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        ally_data = extract_tag_data(ability, "requiresAlly")
        
        return [TagEffect(
            effect_type="prerequisite",
            priority=245,
            immediate=True,
            parameters={
                "prerequisite_type": "requires_ally",
                "distance": ally_data.get("distance", 1),
                "piece_type": ally_data.get("piece_type"),
                "specific_piece": ally_data.get("specific_piece")
            }
        )]
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> tuple[bool, str]:
        is_valid, message = super().validate_prerequisites(ability, context)
        if not is_valid:
            return is_valid, message
        
        ally_data = extract_tag_data(ability, "requiresAlly")
        required_distance = ally_data.get("distance", 1)
        required_type = ally_data.get("piece_type")
        specific_piece = ally_data.get("specific_piece")
        
        source_piece = context.game_state.get_piece(context.source_piece_id)
        if not source_piece:
            return False, "Source piece not found"
        
        # Check for allies within required distance
        allies_found = self._find_allies_in_range(
            context, source_piece.position, required_distance, 
            required_type, specific_piece
        )
        
        if not allies_found:
            return False, f"No required ally found within distance {required_distance}"
        
        return True, "Prerequisites satisfied"
    
    def _find_allies_in_range(self, context: Any, center_pos, max_distance: int,
                             required_type: str = None, specific_piece: str = None) -> List[str]:
        """Find allied pieces within range"""
        allies = []
        
        for row in range(max(0, center_pos.row - max_distance), 
                        min(8, center_pos.row + max_distance + 1)):
            for col in range(max(0, center_pos.col - max_distance),
                           min(8, center_pos.col + max_distance + 1)):
                
                if row == center_pos.row and col == center_pos.col:
                    continue  # Skip self
                
                pieces_at_pos = context.game_state.get_piece_at_position(row, col)
                for piece in pieces_at_pos:
                    if piece.owner == context.current_player:
                        # Check type requirement
                        if required_type and piece.piece_type != required_type:
                            continue
                        
                        # Check specific piece requirement
                        if specific_piece and piece.id != specific_piece:
                            continue
                        
                        allies.append(piece.id)
        
        return allies


class RequiresEnemyTagResolver(BaseTagResolver):
    """Resolver for requiresEnemy tag"""
    
    def _get_tag_name(self) -> str:
        return "requiresEnemy"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        enemy_data = extract_tag_data(ability, "requiresEnemy")
        
        return [TagEffect(
            effect_type="prerequisite",
            priority=240,
            immediate=True,
            parameters={
                "prerequisite_type": "requires_enemy",
                "distance": enemy_data.get("distance", 1),
                "piece_type": enemy_data.get("piece_type"),
                "min_count": enemy_data.get("min_count", 1)
            }
        )]
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> tuple[bool, str]:
        is_valid, message = super().validate_prerequisites(ability, context)
        if not is_valid:
            return is_valid, message
        
        enemy_data = extract_tag_data(ability, "requiresEnemy")
        required_distance = enemy_data.get("distance", 1)
        required_type = enemy_data.get("piece_type")
        min_count = enemy_data.get("min_count", 1)
        
        source_piece = context.game_state.get_piece(context.source_piece_id)
        if not source_piece:
            return False, "Source piece not found"
        
        # Check for enemies within required distance
        enemies_found = self._find_enemies_in_range(
            context, source_piece.position, required_distance, required_type
        )
        
        if len(enemies_found) < min_count:
            return False, f"Need at least {min_count} enemy pieces within distance {required_distance}"
        
        return True, "Prerequisites satisfied"
    
    def _find_enemies_in_range(self, context: Any, center_pos, max_distance: int,
                              required_type: str = None) -> List[str]:
        """Find enemy pieces within range"""
        enemies = []
        
        for row in range(max(0, center_pos.row - max_distance), 
                        min(8, center_pos.row + max_distance + 1)):
            for col in range(max(0, center_pos.col - max_distance),
                           min(8, center_pos.col + max_distance + 1)):
                
                pieces_at_pos = context.game_state.get_piece_at_position(row, col)
                for piece in pieces_at_pos:
                    if piece.owner != context.current_player:
                        # Check type requirement
                        if required_type and piece.piece_type != required_type:
                            continue
                        
                        enemies.append(piece.id)
        
        return enemies
