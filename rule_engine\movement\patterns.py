#!/usr/bin/env python3
"""
Movement Pattern Processing for Adventure Chess Rule Engine
Handles all movement types including custom 8x8 patterns
"""

from typing import Dict, List, Optional, Tuple, Set, Any
from dataclasses import dataclass
from enum import Enum
import logging

# Import from schemas for data structures
from schemas.base import Coordinate, MovementType
from core.tester_engine import PieceColor


class PatternType(Enum):
    """Pattern types for custom movement (from pattern editor)"""
    EMPTY = 0      # Empty square
    MOVE = 1       # Move only
    ATTACK = 2     # Attack only  
    BOTH = 3       # Move and attack
    ACTION = 4     # Action ability
    ANY = 5        # Any turn ability


@dataclass
class MovementPattern:
    """Represents a movement pattern with validation"""
    pattern_type: MovementType
    distance: int = 1
    custom_grid: Optional[List[List[int]]] = None
    piece_position: Optional[Coordinate] = None
    can_capture: bool = True
    color_directional: bool = False
    
    def __post_init__(self):
        if self.custom_grid and not self.piece_position:
            # Default piece position to center if not specified
            self.piece_position = Coordinate(row=3, col=3)


@dataclass
class MovementResult:
    """Result of movement pattern calculation"""
    valid_moves: List[Coordinate]
    valid_attacks: List[Coordinate]
    valid_actions: List[Coordinate]
    blocked_positions: List[Coordinate]
    out_of_bounds: List[Coordinate]
    
    def get_all_valid_positions(self) -> List[Coordinate]:
        """Get all valid positions regardless of type"""
        all_positions = []
        all_positions.extend(self.valid_moves)
        all_positions.extend(self.valid_attacks)
        all_positions.extend(self.valid_actions)
        return list(set(all_positions))  # Remove duplicates


class MovementPatternProcessor:
    """
    Processes movement patterns and calculates valid moves
    Supports all movement types from your schema
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Cache for pattern calculations
        self._pattern_cache: Dict[str, MovementResult] = {}
        self._cache_enabled = True
    
    def calculate_valid_moves(self, piece_data: Dict[str, Any], current_position: Coordinate,
                            game_state: Any, include_attacks: bool = True) -> MovementResult:
        """
        Calculate all valid moves for a piece from its current position
        
        Args:
            piece_data: Piece definition data (from Pydantic schema)
            current_position: Current position of the piece
            game_state: Current game state
            include_attacks: Whether to include attack positions
            
        Returns:
            MovementResult with all valid positions
        """
        # Create cache key
        cache_key = self._create_cache_key(piece_data, current_position, game_state)
        
        if self._cache_enabled and cache_key in self._pattern_cache:
            return self._pattern_cache[cache_key]
        
        # Extract movement configuration
        movement_config = piece_data.get('movement', {})
        movement_type = movement_config.get('type', 'orthogonal')
        distance = movement_config.get('distance', 1)
        custom_pattern = movement_config.get('pattern')
        piece_position = movement_config.get('piece_position')
        can_capture = piece_data.get('can_capture', True)
        color_directional = piece_data.get('color_directional', False)
        
        # Create movement pattern
        pattern = MovementPattern(
            pattern_type=MovementType(movement_type),
            distance=distance,
            custom_grid=custom_pattern,
            piece_position=Coordinate(row=piece_position[0], col=piece_position[1]) if piece_position else None,
            can_capture=can_capture,
            color_directional=color_directional
        )
        
        # Calculate based on movement type
        if pattern.pattern_type == MovementType.CUSTOM:
            result = self._calculate_custom_pattern_moves(pattern, current_position, game_state)
        else:
            result = self._calculate_standard_pattern_moves(pattern, current_position, game_state)
        
        # Apply color directional modifications
        if pattern.color_directional:
            result = self._apply_color_directional_filter(result, piece_data.get('owner'), current_position)
        
        # Cache result
        if self._cache_enabled:
            self._pattern_cache[cache_key] = result
        
        return result
    
    def _calculate_standard_pattern_moves(self, pattern: MovementPattern, 
                                        current_position: Coordinate, game_state: Any) -> MovementResult:
        """Calculate moves for standard movement patterns"""
        valid_moves = []
        valid_attacks = []
        valid_actions = []
        blocked_positions = []
        out_of_bounds = []
        
        if pattern.pattern_type == MovementType.ORTHOGONAL:
            positions = self._get_orthogonal_positions(current_position, pattern.distance)
        elif pattern.pattern_type == MovementType.DIAGONAL:
            positions = self._get_diagonal_positions(current_position, pattern.distance)
        elif pattern.pattern_type == MovementType.ANY:
            positions = self._get_any_direction_positions(current_position, pattern.distance)
        elif pattern.pattern_type == MovementType.L_SHAPE:
            positions = self._get_l_shape_positions(current_position)
        else:
            positions = []
        
        # Validate each position
        for pos in positions:
            if not self._is_valid_board_position(pos):
                out_of_bounds.append(pos)
                continue
            
            # Check if position is blocked
            if self._is_position_blocked(pos, game_state, current_position):
                blocked_positions.append(pos)
                continue
            
            # Check if position is occupied
            occupants = game_state.get_piece_at(pos) if hasattr(game_state, 'get_piece_at') else []
            
            if not occupants:
                # Empty position - valid for movement
                valid_moves.append(pos)
            else:
                # Occupied position - check if can attack
                if pattern.can_capture:
                    valid_attacks.append(pos)
                else:
                    blocked_positions.append(pos)
        
        return MovementResult(
            valid_moves=valid_moves,
            valid_attacks=valid_attacks,
            valid_actions=valid_actions,
            blocked_positions=blocked_positions,
            out_of_bounds=out_of_bounds
        )
    
    def _calculate_custom_pattern_moves(self, pattern: MovementPattern,
                                      current_position: Coordinate, game_state: Any) -> MovementResult:
        """Calculate moves for custom 8x8 patterns"""
        if not pattern.custom_grid or not pattern.piece_position:
            return MovementResult([], [], [], [], [])
        
        valid_moves = []
        valid_attacks = []
        valid_actions = []
        blocked_positions = []
        out_of_bounds = []
        
        # Calculate offset from piece position in pattern to current position
        pattern_piece_row = pattern.piece_position.row
        pattern_piece_col = pattern.piece_position.col
        
        # Process each cell in the 8x8 pattern
        for pattern_row in range(8):
            for pattern_col in range(8):
                pattern_value = pattern.custom_grid[pattern_row][pattern_col]
                
                if pattern_value == PatternType.EMPTY.value:
                    continue  # Skip empty cells
                
                # Calculate world position
                row_offset = pattern_row - pattern_piece_row
                col_offset = pattern_col - pattern_piece_col
                world_pos = Coordinate(
                    row=current_position.row + row_offset,
                    col=current_position.col + col_offset
                )
                
                # Skip if out of bounds
                if not self._is_valid_board_position(world_pos):
                    out_of_bounds.append(world_pos)
                    continue
                
                # Skip self position
                if world_pos.row == current_position.row and world_pos.col == current_position.col:
                    continue
                
                # Check if position is blocked by pathfinding
                if self._is_position_blocked(world_pos, game_state, current_position):
                    blocked_positions.append(world_pos)
                    continue
                
                # Check occupancy and categorize based on pattern value
                occupants = game_state.get_piece_at(world_pos) if hasattr(game_state, 'get_piece_at') else []
                
                if pattern_value == PatternType.MOVE.value:
                    if not occupants:
                        valid_moves.append(world_pos)
                    else:
                        blocked_positions.append(world_pos)
                
                elif pattern_value == PatternType.ATTACK.value:
                    if occupants and pattern.can_capture:
                        valid_attacks.append(world_pos)
                    elif not occupants:
                        blocked_positions.append(world_pos)  # Attack-only cell with no target
                
                elif pattern_value == PatternType.BOTH.value:
                    if not occupants:
                        valid_moves.append(world_pos)
                    elif pattern.can_capture:
                        valid_attacks.append(world_pos)
                    else:
                        blocked_positions.append(world_pos)
                
                elif pattern_value == PatternType.ACTION.value:
                    valid_actions.append(world_pos)
                
                elif pattern_value == PatternType.ANY.value:
                    if not occupants:
                        valid_moves.append(world_pos)
                    elif pattern.can_capture:
                        valid_attacks.append(world_pos)
                    else:
                        valid_actions.append(world_pos)  # Can still use abilities
        
        return MovementResult(
            valid_moves=valid_moves,
            valid_attacks=valid_attacks,
            valid_actions=valid_actions,
            blocked_positions=blocked_positions,
            out_of_bounds=out_of_bounds
        )
    
    def _get_orthogonal_positions(self, center: Coordinate, distance: int) -> List[Coordinate]:
        """Get all orthogonal positions within distance"""
        positions = []
        directions = [(0, 1), (0, -1), (1, 0), (-1, 0)]  # right, left, down, up
        
        for dr, dc in directions:
            for i in range(1, distance + 1):
                new_row = center.row + dr * i
                new_col = center.col + dc * i
                # Only create coordinate if it's within bounds
                if 0 <= new_row < 8 and 0 <= new_col < 8:
                    pos = Coordinate(row=new_row, col=new_col)
                    positions.append(pos)
        
        return positions
    
    def _get_diagonal_positions(self, center: Coordinate, distance: int) -> List[Coordinate]:
        """Get all diagonal positions within distance"""
        positions = []
        directions = [(1, 1), (1, -1), (-1, 1), (-1, -1)]  # diagonal directions
        
        for dr, dc in directions:
            for i in range(1, distance + 1):
                new_row = center.row + dr * i
                new_col = center.col + dc * i
                # Only create coordinate if it's within bounds
                if 0 <= new_row < 8 and 0 <= new_col < 8:
                    pos = Coordinate(row=new_row, col=new_col)
                    positions.append(pos)
        
        return positions
    
    def _get_any_direction_positions(self, center: Coordinate, distance: int) -> List[Coordinate]:
        """Get all positions in any direction within distance"""
        positions = []
        
        # Combine orthogonal and diagonal
        positions.extend(self._get_orthogonal_positions(center, distance))
        positions.extend(self._get_diagonal_positions(center, distance))
        
        return positions
    
    def _get_l_shape_positions(self, center: Coordinate) -> List[Coordinate]:
        """Get all L-shape (knight) positions"""
        positions = []
        l_moves = [
            (2, 1), (2, -1), (-2, 1), (-2, -1),
            (1, 2), (1, -2), (-1, 2), (-1, -2)
        ]
        
        for dr, dc in l_moves:
            new_row = center.row + dr
            new_col = center.col + dc
            # Only create coordinate if it's within bounds
            if 0 <= new_row < 8 and 0 <= new_col < 8:
                pos = Coordinate(row=new_row, col=new_col)
                positions.append(pos)
        
        return positions
    
    def _is_valid_board_position(self, position: Coordinate) -> bool:
        """Check if position is within board bounds"""
        return 0 <= position.row < 8 and 0 <= position.col < 8
    
    def _is_position_blocked(self, target: Coordinate, game_state: Any, source: Coordinate) -> bool:
        """Check if position is blocked by obstacles or line-of-sight issues"""
        # This will be enhanced with pathfinding in the next component
        # For now, basic implementation
        return False
    
    def _apply_color_directional_filter(self, result: MovementResult, 
                                      owner: PieceColor, current_position: Coordinate) -> MovementResult:
        """Apply color directional movement restrictions"""
        if owner == PieceColor.WHITE:
            # White moves "up" (decreasing row numbers)
            filtered_moves = [pos for pos in result.valid_moves if pos.row <= current_position.row]
            filtered_attacks = [pos for pos in result.valid_attacks if pos.row <= current_position.row]
        else:
            # Black moves "down" (increasing row numbers)
            filtered_moves = [pos for pos in result.valid_moves if pos.row >= current_position.row]
            filtered_attacks = [pos for pos in result.valid_attacks if pos.row >= current_position.row]
        
        return MovementResult(
            valid_moves=filtered_moves,
            valid_attacks=filtered_attacks,
            valid_actions=result.valid_actions,  # Actions not affected by direction
            blocked_positions=result.blocked_positions,
            out_of_bounds=result.out_of_bounds
        )
    
    def _create_cache_key(self, piece_data: Dict[str, Any], position: Coordinate, game_state: Any) -> str:
        """Create cache key for movement calculation"""
        # Simple cache key - could be enhanced with game state hash
        movement_config = piece_data.get('movement', {})
        return f"{movement_config.get('type')}_{movement_config.get('distance')}_{position.row}_{position.col}"
    
    def clear_cache(self):
        """Clear movement pattern cache"""
        self._pattern_cache.clear()
        self.logger.debug("Movement pattern cache cleared")
    
    def set_cache_enabled(self, enabled: bool):
        """Enable or disable caching"""
        self._cache_enabled = enabled
        if not enabled:
            self.clear_cache()
