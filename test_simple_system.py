#!/usr/bin/env python3
"""
Test Simple System - Adventure Chess
Tests the new simplified system without legacy validation
"""

import sys
import os
import json

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_simple_bridge():
    """Test the simple bridge system"""
    print("=== Testing Simple Bridge System ===")
    
    try:
        from utils.simple_bridge import simple_bridge
        from utils.direct_data_manager import DirectDataManager
        
        # Test 1: Direct Data Manager
        print("\n--- Test 1: Direct Data Manager ---")
        
        test_piece_data = {
            'version': '1.0.0',
            'name': 'Simple Test Piece',
            'description': 'Testing simple system',
            'role': 'Commander',
            'movement': {
                'type': 'diagonal',
                'pattern': [[1 if i == j or i + j == 7 else 0 for j in range(8)] for i in range(8)],
                'piecePosition': [3, 3]
            },
            'canCapture': True,
            'abilities': [],
            'maxPoints': 3,
            'startingPoints': 2,
            'rechargeType': 'turnRecharge'
        }
        
        # Save directly
        success, error = DirectDataManager.save_piece(test_piece_data, 'simple_test_piece')
        
        if success:
            print("✓ Direct save successful")
        else:
            print(f"❌ Direct save failed: {error}")
            return False
        
        # Load directly
        loaded_data, error = DirectDataManager.load_piece('simple_test_piece')
        
        if loaded_data and not error:
            print("✓ Direct load successful")
            
            # Verify data
            if loaded_data.get('movement', {}).get('type') == 'diagonal':
                print("✓ Movement data preserved correctly")
            else:
                print(f"❌ Movement data corrupted: {loaded_data.get('movement', {}).get('type')}")
                return False
                
        else:
            print(f"❌ Direct load failed: {error}")
            return False
        
        # Test 2: Simple Bridge
        print("\n--- Test 2: Simple Bridge ---")
        
        # List pieces
        pieces = simple_bridge.list_pieces()
        if 'simple_test_piece' in pieces:
            print("✓ Simple bridge can list pieces")
        else:
            print("❌ Simple bridge cannot list pieces")
            return False
        
        # Load through bridge
        bridge_data, error = simple_bridge.load_piece_for_ui('simple_test_piece')
        
        if bridge_data and not error:
            print("✓ Simple bridge load successful")
            
            # Verify data
            if bridge_data.get('movement', {}).get('type') == 'diagonal':
                print("✓ Bridge preserves movement data")
            else:
                print(f"❌ Bridge corrupts movement data: {bridge_data.get('movement', {}).get('type')}")
                return False
                
        else:
            print(f"❌ Simple bridge load failed: {error}")
            return False
        
        # Test 3: Validation (Optional)
        print("\n--- Test 3: Optional Validation ---")
        
        is_valid, errors, warnings = simple_bridge.validate_piece_data(test_piece_data)
        
        if is_valid:
            print("✓ Validation passes")
            if warnings:
                print(f"⚠️  Warnings: {warnings}")
        else:
            print(f"❌ Validation failed: {errors}")
            return False
        
        # Clean up
        try:
            DirectDataManager.delete_piece('simple_test_piece')
            print("🧹 Test file cleaned up")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ Simple bridge test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_piece_editor_integration():
    """Test piece editor with simple system"""
    print("\n=== Testing Piece Editor Integration ===")
    
    try:
        from editors.piece_editor import PieceEditorWindow
        from utils.simple_bridge import simple_bridge
        
        # Create piece editor
        piece_editor = PieceEditorWindow()
        
        print("\n--- Test 1: Editor Creation ---")
        print("✓ Piece editor created successfully")
        
        # Test 2: Set diagonal movement
        print("\n--- Test 2: Set Movement Data ---")
        
        # Find diagonal button and click it
        diagonal_button = None
        for btn, movement_type in piece_editor.movement_pattern_buttons:
            if movement_type == "diagonal":
                diagonal_button = btn
                break
        
        if diagonal_button:
            diagonal_button.setChecked(True)
            piece_editor.select_movement_preset("diagonal", diagonal_button)
            print("✓ Diagonal movement selected")
        else:
            print("❌ Diagonal button not found")
            return False
        
        # Set piece name
        piece_editor.name_edit.setText("Integration Test Piece")
        
        # Test 3: Save through simple bridge
        print("\n--- Test 3: Save Through Simple Bridge ---")
        
        success, error = simple_bridge.save_piece_from_ui(piece_editor, 'integration_test_piece')
        
        if success:
            print("✓ Save through simple bridge successful")
        else:
            print(f"❌ Save through simple bridge failed: {error}")
            return False
        
        # Test 4: Load back and verify
        print("\n--- Test 4: Load Back and Verify ---")
        
        loaded_data, error = simple_bridge.load_piece_for_ui('integration_test_piece')
        
        if loaded_data and not error:
            print("✓ Load back successful")
            
            # Verify movement data
            movement = loaded_data.get('movement', {})
            if movement.get('type') == 'diagonal':
                print("✓ Movement type preserved")
            else:
                print(f"❌ Movement type corrupted: {movement.get('type')}")
                return False
            
            if 'pattern' in movement:
                print("✓ Movement pattern preserved")
            else:
                print("❌ Movement pattern missing")
                return False
                
        else:
            print(f"❌ Load back failed: {error}")
            return False
        
        # Test 5: Load into editor
        print("\n--- Test 5: Load Into Editor ---")
        
        # Reset editor first
        piece_editor.reset_form()
        
        # Load the data
        piece_editor.load_piece_data(loaded_data)
        
        # Check if movement type is correct
        if piece_editor.selected_movement_type == 'diagonal':
            print("✓ Movement type loaded correctly into editor")
        else:
            print(f"❌ Movement type not loaded correctly: {piece_editor.selected_movement_type}")
            return False
        
        # Clean up
        try:
            simple_bridge.delete_piece('integration_test_piece')
            print("🧹 Test file cleaned up")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ Piece editor integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_simple_system_tests():
    """Run all simple system tests"""
    print("🔍 SIMPLE SYSTEM TEST")
    print("=" * 60)
    
    tests = [
        test_simple_bridge,
        test_piece_editor_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} crashed: {e}")
    
    print("\n" + "=" * 60)
    print(f"✅ Tests passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All simple system tests passed!")
        print("✓ Legacy validation removed")
        print("✓ Direct save/load working")
        print("✓ Movement data preserved")
        print("✓ Editor integration successful")
        return True
    else:
        print("⚠️  Some simple system tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    # Create QApplication for testing
    from PyQt6.QtWidgets import QApplication
    app = QApplication(sys.argv)
    
    # Run the tests directly
    success = run_simple_system_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
