"""
Data validation utility for Adventure Chess
Provides comprehensive validation of pieces and abilities using Pydantic models
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional

# Add parent directory to path to import config
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config import PIECES_DIR, ABILITIES_DIR
from schemas import (
    pydantic_data_manager,
    DataMigrationManager,
    Piece,
    Ability
)

logger = logging.getLogger(__name__)


class DataValidator:
    """
    Comprehensive data validation using Pydantic models
    """
    
    def __init__(self):
        self.validation_results: Dict[str, Any] = {
            'pieces': {'valid': [], 'invalid': [], 'warnings': []},
            'abilities': {'valid': [], 'invalid': [], 'warnings': []}
        }
    
    def validate_all_data(self) -> Tuple[bool, Dict[str, Any]]:
        """
        Validate all pieces and abilities
        Returns: (all_valid, detailed_report)
        """
        logger.info("Starting comprehensive data validation...")
        
        # Validate pieces
        pieces_valid = self.validate_all_pieces()
        
        # Validate abilities
        abilities_valid = self.validate_all_abilities()
        
        # Generate summary report
        report = self._generate_validation_report()
        
        all_valid = pieces_valid and abilities_valid
        
        if all_valid:
            logger.info("All data validation passed!")
        else:
            logger.warning("Data validation found issues")
        
        return all_valid, report
    
    def validate_all_pieces(self) -> bool:
        """Validate all piece files"""
        pieces_dir = Path(PIECES_DIR)
        if not pieces_dir.exists():
            self.validation_results['pieces']['invalid'].append({
                'file': 'N/A',
                'error': 'Pieces directory not found'
            })
            return False
        
        all_valid = True
        
        for piece_file in pieces_dir.glob("*.json"):
            if piece_file.parent.name.startswith("backup"):
                continue  # Skip backup files
            
            try:
                # Load and validate using Pydantic
                piece, error = pydantic_data_manager.load_piece(piece_file.name)
                
                if piece is None:
                    self.validation_results['pieces']['invalid'].append({
                        'file': piece_file.name,
                        'error': error
                    })
                    all_valid = False
                else:
                    # Additional validation checks
                    warnings = self._validate_piece_content(piece, piece_file.name)
                    
                    if warnings:
                        self.validation_results['pieces']['warnings'].extend(warnings)
                    
                    self.validation_results['pieces']['valid'].append({
                        'file': piece_file.name,
                        'name': piece.name,
                        'warnings': len(warnings)
                    })
                
            except Exception as e:
                self.validation_results['pieces']['invalid'].append({
                    'file': piece_file.name,
                    'error': f"Exception during validation: {e}"
                })
                all_valid = False
        
        return all_valid
    
    def validate_all_abilities(self) -> bool:
        """Validate all ability files"""
        abilities_dir = Path(ABILITIES_DIR)
        if not abilities_dir.exists():
            self.validation_results['abilities']['invalid'].append({
                'file': 'N/A',
                'error': 'Abilities directory not found'
            })
            return False
        
        all_valid = True
        
        for ability_file in abilities_dir.glob("*.json"):
            if ability_file.parent.name.startswith("backup"):
                continue  # Skip backup files
            
            try:
                # Load and validate using Pydantic
                ability, error = pydantic_data_manager.load_ability(ability_file.name)
                
                if ability is None:
                    self.validation_results['abilities']['invalid'].append({
                        'file': ability_file.name,
                        'error': error
                    })
                    all_valid = False
                else:
                    # Additional validation checks
                    warnings = self._validate_ability_content(ability, ability_file.name)
                    
                    if warnings:
                        self.validation_results['abilities']['warnings'].extend(warnings)
                    
                    self.validation_results['abilities']['valid'].append({
                        'file': ability_file.name,
                        'name': ability.name,
                        'tags': len(ability.tags),
                        'warnings': len(warnings)
                    })
                
            except Exception as e:
                self.validation_results['abilities']['invalid'].append({
                    'file': ability_file.name,
                    'error': f"Exception during validation: {e}"
                })
                all_valid = False
        
        return all_valid
    
    def _validate_piece_content(self, piece: Piece, filename: str) -> List[Dict[str, str]]:
        """Validate piece content and return warnings"""
        warnings = []
        
        # Check for empty or placeholder names
        if not piece.name or piece.name.lower() in ['unnamed', 'unnamed piece', 'test']:
            warnings.append({
                'file': filename,
                'type': 'naming',
                'message': f"Piece has placeholder name: '{piece.name}'"
            })
        
        # Check movement configuration
        if piece.movement.type == "custom":
            if piece.movement.pattern is None:
                warnings.append({
                    'file': filename,
                    'type': 'movement',
                    'message': "Custom movement type requires a pattern"
                })
            if piece.movement.piece_position is None:
                warnings.append({
                    'file': filename,
                    'type': 'movement',
                    'message': "Custom movement type requires piece position"
                })
        
        # Check point system consistency
        if piece.max_points > 0:
            if piece.starting_points > piece.max_points:
                warnings.append({
                    'file': filename,
                    'type': 'points',
                    'message': f"Starting points ({piece.starting_points}) exceed max points ({piece.max_points})"
                })
            
            if piece.turn_points > piece.max_points:
                warnings.append({
                    'file': filename,
                    'type': 'points',
                    'message': f"Turn points ({piece.turn_points}) exceed max points ({piece.max_points})"
                })
        
        # Check ability references
        for ability_name in piece.abilities:
            if not self._ability_exists(ability_name):
                warnings.append({
                    'file': filename,
                    'type': 'abilities',
                    'message': f"Referenced ability not found: '{ability_name}'"
                })
        
        # Check promotion references
        for promotion in piece.promotions + piece.promotions_2nd:
            if not self._piece_exists(promotion):
                warnings.append({
                    'file': filename,
                    'type': 'promotions',
                    'message': f"Referenced promotion piece not found: '{promotion}'"
                })
        
        return warnings
    
    def _validate_ability_content(self, ability: Ability, filename: str) -> List[Dict[str, str]]:
        """Validate ability content and return warnings"""
        warnings = []
        
        # Check for empty or placeholder names
        if not ability.name or ability.name.lower() in ['unnamed', 'unnamed ability', 'test']:
            warnings.append({
                'file': filename,
                'type': 'naming',
                'message': f"Ability has placeholder name: '{ability.name}'"
            })
        
        # Check tag validation
        tag_errors = ability.validate_all_tags()
        for tag, error in tag_errors.items():
            warnings.append({
                'file': filename,
                'type': 'tag_validation',
                'message': f"Tag '{tag}' validation error: {error}"
            })
        
        # Check for empty tag list
        if not ability.tags:
            warnings.append({
                'file': filename,
                'type': 'tags',
                'message': "Ability has no tags defined"
            })
        
        # Check range configuration if range tag is present
        if 'range' in ability.tags:
            if ability.range_mask is None:
                warnings.append({
                    'file': filename,
                    'type': 'range',
                    'message': "Range tag requires range_mask to be defined"
                })
        
        # Check for conflicting range settings
        if ability.range_friendly_only and ability.range_enemy_only:
            warnings.append({
                'file': filename,
                'type': 'range',
                'message': "range_friendly_only and range_enemy_only cannot both be true"
            })
        
        return warnings
    
    def _ability_exists(self, ability_name: str) -> bool:
        """Check if an ability file exists"""
        abilities_dir = Path(ABILITIES_DIR)
        if not abilities_dir.exists():
            return False
        
        # Check for exact filename match
        ability_file = abilities_dir / f"{ability_name}.json"
        if ability_file.exists():
            return True
        
        # Check for case-insensitive match
        for file in abilities_dir.glob("*.json"):
            if file.stem.lower() == ability_name.lower():
                return True
        
        return False
    
    def _piece_exists(self, piece_name: str) -> bool:
        """Check if a piece file exists"""
        pieces_dir = Path(PIECES_DIR)
        if not pieces_dir.exists():
            return False
        
        # Check for exact filename match
        piece_file = pieces_dir / f"{piece_name}.json"
        if piece_file.exists():
            return True
        
        # Check for case-insensitive match
        for file in pieces_dir.glob("*.json"):
            if file.stem.lower() == piece_name.lower():
                return True
        
        return False
    
    def _generate_validation_report(self) -> Dict[str, Any]:
        """Generate a comprehensive validation report"""
        report = {
            'summary': {
                'pieces_valid': len(self.validation_results['pieces']['valid']),
                'pieces_invalid': len(self.validation_results['pieces']['invalid']),
                'pieces_warnings': len(self.validation_results['pieces']['warnings']),
                'abilities_valid': len(self.validation_results['abilities']['valid']),
                'abilities_invalid': len(self.validation_results['abilities']['invalid']),
                'abilities_warnings': len(self.validation_results['abilities']['warnings']),
            },
            'details': self.validation_results
        }
        
        # Calculate overall health score
        total_items = (report['summary']['pieces_valid'] + 
                      report['summary']['pieces_invalid'] + 
                      report['summary']['abilities_valid'] + 
                      report['summary']['abilities_invalid'])
        
        if total_items > 0:
            valid_items = report['summary']['pieces_valid'] + report['summary']['abilities_valid']
            report['summary']['health_score'] = (valid_items / total_items) * 100
        else:
            report['summary']['health_score'] = 0
        
        return report
    
    def print_validation_report(self, detailed: bool = False):
        """Print a human-readable validation report"""
        report = self._generate_validation_report()
        summary = report['summary']
        
        print("=== Data Validation Report ===")
        print(f"Overall Health Score: {summary['health_score']:.1f}%")
        print()
        
        print("📋 Summary:")
        print(f"  Pieces: {summary['pieces_valid']} valid, {summary['pieces_invalid']} invalid, {summary['pieces_warnings']} warnings")
        print(f"  Abilities: {summary['abilities_valid']} valid, {summary['abilities_invalid']} invalid, {summary['abilities_warnings']} warnings")
        print()
        
        if summary['pieces_invalid'] > 0:
            print("❌ Invalid Pieces:")
            for item in report['details']['pieces']['invalid']:
                print(f"  {item['file']}: {item['error']}")
            print()
        
        if summary['abilities_invalid'] > 0:
            print("❌ Invalid Abilities:")
            for item in report['details']['abilities']['invalid']:
                print(f"  {item['file']}: {item['error']}")
            print()
        
        if detailed and (summary['pieces_warnings'] > 0 or summary['abilities_warnings'] > 0):
            print("⚠️ Warnings:")
            for warning in report['details']['pieces']['warnings'] + report['details']['abilities']['warnings']:
                print(f"  {warning['file']} ({warning['type']}): {warning['message']}")


def run_validation(detailed: bool = False) -> bool:
    """
    Run comprehensive data validation
    Returns: True if all data is valid, False otherwise
    """
    validator = DataValidator()
    
    try:
        all_valid, report = validator.validate_all_data()
        validator.print_validation_report(detailed=detailed)
        return all_valid
        
    except Exception as e:
        print(f"❌ Validation failed with exception: {e}")
        return False


if __name__ == "__main__":
    # Run validation when script is executed directly
    import sys
    
    detailed = "--detailed" in sys.argv
    
    print("Starting Adventure Chess data validation...")
    print()
    
    success = run_validation(detailed=detailed)
    
    if success:
        print("\n✅ All data validation passed!")
    else:
        print("\n⚠️ Data validation found issues!")
        print("Use the migration utility to fix data format issues.")
    
    sys.exit(0 if success else 1)
