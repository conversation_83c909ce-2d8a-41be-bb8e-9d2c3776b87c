#!/usr/bin/env python3
"""Share Space Tag Resolver for Adventure Chess Rule Engine"""

from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class ShareSpaceTagResolver(BaseTagResolver):
    """
    Resolver for the 'shareSpace' tag
    Allows multiple pieces to occupy the same square with configurable targeting
    """
    
    def _get_tag_name(self) -> str:
        return "shareSpace"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.SPECIAL
    
    def _get_conflicts(self) -> List[str]:
        return []  # Share space doesn't conflict with other abilities
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        """Process share space ability"""
        share_data = extract_tag_data(ability, "shareSpace")
        
        return [TagEffect(
            effect_type="share_space",
            priority=10,  # High priority to apply early
            immediate=True,
            parameters={
                "action": "share_space",
                "max_pieces": share_data.get("share_space_max", 2),
                "same_type_only": share_data.get("share_space_same_type", False),
                "allow_friendly": share_data.get("share_space_friendly", True),
                "allow_enemy": share_data.get("share_space_enemy", False),
                "allow_any": share_data.get("share_space_any", False)
            }
        )]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        """Get targeting requirements for share space"""
        share_data = extract_tag_data(ability, "shareSpace")
        
        # Determine valid targets based on configuration
        valid_targets = []
        if share_data.get("share_space_friendly", True):
            valid_targets.append("friendly")
        if share_data.get("share_space_enemy", False):
            valid_targets.append("enemy")
        if share_data.get("share_space_any", False):
            valid_targets = ["any"]
        
        return {
            "requires_target": False,  # Share space is passive
            "target_type": "square",
            "valid_targets": valid_targets,
            "max_pieces_per_square": share_data.get("share_space_max", 2),
            "same_type_restriction": share_data.get("share_space_same_type", False)
        }
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> tuple[bool, str]:
        """Validate prerequisites for share space"""
        # Call parent validation first
        is_valid, message = super().validate_prerequisites(ability, context)
        if not is_valid:
            return is_valid, message
        
        share_data = extract_tag_data(ability, "shareSpace")
        
        # Validate configuration
        max_pieces = share_data.get("share_space_max", 2)
        if max_pieces < 2 or max_pieces > 8:
            return False, "Share space max pieces must be between 2 and 8"
        
        # Check that at least one targeting option is enabled
        allow_friendly = share_data.get("share_space_friendly", True)
        allow_enemy = share_data.get("share_space_enemy", False)
        allow_any = share_data.get("share_space_any", False)
        
        if not (allow_friendly or allow_enemy or allow_any):
            return False, "Share space must allow at least one type of piece targeting"
        
        return True, ""
    
    def get_effect_description(self, ability: Ability) -> str:
        """Get human-readable description of the share space effect"""
        share_data = extract_tag_data(ability, "shareSpace")
        
        max_pieces = share_data.get("share_space_max", 2)
        same_type = share_data.get("share_space_same_type", False)
        allow_friendly = share_data.get("share_space_friendly", True)
        allow_enemy = share_data.get("share_space_enemy", False)
        allow_any = share_data.get("share_space_any", False)
        
        description = f"Allows up to {max_pieces} pieces to occupy the same square"
        
        if same_type:
            description += " (same type only)"
        
        # Add targeting description
        if allow_any:
            description += " (any pieces)"
        else:
            targets = []
            if allow_friendly:
                targets.append("friendly")
            if allow_enemy:
                targets.append("enemy")
            if targets:
                description += f" ({' and '.join(targets)} pieces)"
        
        return description
