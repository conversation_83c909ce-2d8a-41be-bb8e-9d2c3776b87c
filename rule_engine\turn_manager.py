#!/usr/bin/env python3
"""
Turn Management System for Adventure Chess Rule Engine
Handles turn progression, phase management, and turn-based effects
"""

from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from enum import Enum
import logging
from datetime import datetime

from core.tester_engine import PieceColor


class TurnPhase(Enum):
    """Phases within a turn"""
    START = "start"
    MAIN = "main"
    END = "end"
    CLEANUP = "cleanup"


class TurnActionType(Enum):
    """Types of actions that can be taken during a turn"""
    MOVE = "move"
    ABILITY = "ability"
    PASS = "pass"
    SURRENDER = "surrender"


@dataclass
class TurnAction:
    """Represents an action taken during a turn"""
    action_id: str
    action_type: TurnActionType
    piece_id: str
    ability_name: Optional[str] = None
    target_positions: List[Any] = field(default_factory=list)
    cost: int = 0
    timestamp: datetime = field(default_factory=datetime.now)
    success: bool = False
    error_message: Optional[str] = None


@dataclass
class TurnState:
    """State information for a single turn"""
    turn_number: int
    current_player: PieceColor
    current_phase: TurnPhase = TurnPhase.START
    actions_taken: List[TurnAction] = field(default_factory=list)
    points_spent: int = 0
    max_points: int = 10
    started_at: datetime = field(default_factory=datetime.now)
    ended_at: Optional[datetime] = None
    
    def get_remaining_points(self) -> int:
        """Get remaining action points for this turn"""
        return max(0, self.max_points - self.points_spent)
    
    def can_afford_action(self, cost: int) -> bool:
        """Check if player can afford an action"""
        return self.get_remaining_points() >= cost
    
    def spend_points(self, amount: int) -> bool:
        """Spend action points"""
        if self.can_afford_action(amount):
            self.points_spent += amount
            return True
        return False


class TurnManager:
    """
    Manages turn progression and turn-based mechanics
    """
    
    def __init__(self, max_points_per_turn: int = 10):
        self.logger = logging.getLogger(__name__)
        
        # Turn state
        self.current_turn: Optional[TurnState] = None
        self.turn_history: List[TurnState] = []
        self.max_points_per_turn = max_points_per_turn
        
        # Phase handlers
        self.phase_handlers: Dict[TurnPhase, List[Callable]] = {
            TurnPhase.START: [],
            TurnPhase.MAIN: [],
            TurnPhase.END: [],
            TurnPhase.CLEANUP: []
        }
        
        # Turn listeners
        self.turn_listeners: List[Callable] = []
        self.action_listeners: List[Callable] = []
        
        self.logger.info("TurnManager initialized")
    
    def start_turn(self, player: PieceColor, turn_number: int = None) -> TurnState:
        """
        Start a new turn for the specified player
        
        Args:
            player: Player whose turn it is
            turn_number: Turn number (auto-incremented if None)
            
        Returns:
            New TurnState object
        """
        # End current turn if active
        if self.current_turn and not self.current_turn.ended_at:
            self.end_turn()
        
        # Determine turn number
        if turn_number is None:
            turn_number = len(self.turn_history) + 1
        
        # Create new turn state
        self.current_turn = TurnState(
            turn_number=turn_number,
            current_player=player,
            max_points=self.max_points_per_turn
        )
        
        # Process start phase
        self._process_phase(TurnPhase.START)
        
        # Move to main phase
        self.current_turn.current_phase = TurnPhase.MAIN
        
        # Notify listeners
        self._notify_turn_listeners("turn_started", self.current_turn)
        
        self.logger.info(f"Started turn {turn_number} for {player.value}")
        return self.current_turn
    
    def end_turn(self) -> Optional[TurnState]:
        """
        End the current turn
        
        Returns:
            Completed TurnState object
        """
        if not self.current_turn:
            return None
        
        # Process end phase
        self.current_turn.current_phase = TurnPhase.END
        self._process_phase(TurnPhase.END)
        
        # Process cleanup phase
        self.current_turn.current_phase = TurnPhase.CLEANUP
        self._process_phase(TurnPhase.CLEANUP)
        
        # Finalize turn
        self.current_turn.ended_at = datetime.now()
        completed_turn = self.current_turn
        
        # Archive turn
        self.turn_history.append(completed_turn)
        self.current_turn = None
        
        # Notify listeners
        self._notify_turn_listeners("turn_ended", completed_turn)
        
        self.logger.info(f"Ended turn {completed_turn.turn_number}")
        return completed_turn
    
    def execute_action(self, action_type: TurnActionType, piece_id: str,
                      ability_name: str = None, target_positions: List[Any] = None,
                      cost: int = 1) -> TurnAction:
        """
        Execute an action during the current turn
        
        Args:
            action_type: Type of action to execute
            piece_id: ID of the piece performing the action
            ability_name: Name of ability being used (if applicable)
            target_positions: Target positions for the action
            cost: Action point cost
            
        Returns:
            TurnAction object with execution results
        """
        if not self.current_turn:
            raise ValueError("No active turn")
        
        if self.current_turn.current_phase != TurnPhase.MAIN:
            raise ValueError("Actions can only be executed during main phase")
        
        # Create action object
        action = TurnAction(
            action_id=f"action_{len(self.current_turn.actions_taken) + 1}",
            action_type=action_type,
            piece_id=piece_id,
            ability_name=ability_name,
            target_positions=target_positions or [],
            cost=cost
        )
        
        # Check if action can be afforded
        if not self.current_turn.can_afford_action(cost):
            action.success = False
            action.error_message = f"Insufficient action points (need {cost}, have {self.current_turn.get_remaining_points()})"
        else:
            # Spend points
            self.current_turn.spend_points(cost)
            action.success = True
        
        # Record action
        self.current_turn.actions_taken.append(action)
        
        # Notify listeners
        self._notify_action_listeners(action)
        
        self.logger.debug(f"Executed action: {action_type.value} by {piece_id}")
        return action
    
    def get_current_turn(self) -> Optional[TurnState]:
        """Get the current turn state"""
        return self.current_turn
    
    def get_turn_history(self) -> List[TurnState]:
        """Get history of all completed turns"""
        return self.turn_history.copy()
    
    def get_last_turn(self) -> Optional[TurnState]:
        """Get the last completed turn"""
        return self.turn_history[-1] if self.turn_history else None
    
    def add_phase_handler(self, phase: TurnPhase, handler: Callable):
        """Add a handler for a specific turn phase"""
        self.phase_handlers[phase].append(handler)
    
    def add_turn_listener(self, listener: Callable):
        """Add a listener for turn events"""
        self.turn_listeners.append(listener)
    
    def add_action_listener(self, listener: Callable):
        """Add a listener for action events"""
        self.action_listeners.append(listener)
    
    def _process_phase(self, phase: TurnPhase):
        """Process all handlers for a specific phase"""
        for handler in self.phase_handlers[phase]:
            try:
                handler(self.current_turn)
            except Exception as e:
                self.logger.error(f"Error in phase handler for {phase.value}: {str(e)}")
    
    def _notify_turn_listeners(self, event_type: str, turn_state: TurnState):
        """Notify all turn listeners of an event"""
        for listener in self.turn_listeners:
            try:
                listener(event_type, turn_state)
            except Exception as e:
                self.logger.error(f"Error in turn listener: {str(e)}")
    
    def _notify_action_listeners(self, action: TurnAction):
        """Notify all action listeners of an action"""
        for listener in self.action_listeners:
            try:
                listener(action)
            except Exception as e:
                self.logger.error(f"Error in action listener: {str(e)}")
    
    def get_player_statistics(self, player: PieceColor) -> Dict[str, Any]:
        """Get statistics for a specific player"""
        player_turns = [turn for turn in self.turn_history if turn.current_player == player]
        
        if not player_turns:
            return {
                "total_turns": 0,
                "total_actions": 0,
                "total_points_spent": 0,
                "average_points_per_turn": 0,
                "average_actions_per_turn": 0
            }
        
        total_actions = sum(len(turn.actions_taken) for turn in player_turns)
        total_points = sum(turn.points_spent for turn in player_turns)
        
        return {
            "total_turns": len(player_turns),
            "total_actions": total_actions,
            "total_points_spent": total_points,
            "average_points_per_turn": total_points / len(player_turns),
            "average_actions_per_turn": total_actions / len(player_turns)
        }
    
    def reset(self):
        """Reset the turn manager to initial state"""
        self.current_turn = None
        self.turn_history.clear()
        self.logger.info("TurnManager reset")


# Utility functions for common turn management tasks

def create_turn_manager_with_handlers(status_effect_manager=None, event_system=None) -> TurnManager:
    """Create a turn manager with common handlers"""
    turn_manager = TurnManager()
    
    # Add status effect processing
    if status_effect_manager:
        def process_status_effects(turn_state):
            status_effect_manager.tick_effects(turn_state.turn_number)
        
        turn_manager.add_phase_handler(TurnPhase.END, process_status_effects)
    
    # Add event processing
    if event_system:
        def process_turn_events(turn_state):
            # Process any turn-based events
            pass
        
        turn_manager.add_phase_handler(TurnPhase.START, process_turn_events)
    
    return turn_manager
