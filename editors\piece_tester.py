#!/usr/bin/env python3
"""
Piece Tester for Adventure Chess - BASE FUNCTIONALITY
UI intact, logic engine removed, ready for new brain implementation
"""

# Standard library imports
import os
import json
from typing import Optional

# PyQt6 imports
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, QPushButton,
    QTextEdit, QGroupBox, QGridLayout, QFrame, QScrollArea,
    QButtonGroup, QRadioButton, QListWidget, QListWidgetItem,
    QMessageBox, QFileDialog, QSplitter, QCheckBox
)
from PyQt6.QtGui import QFont, QIcon, QPixmap, QPainter, QColor, QShortcut, QKeySequence
from PyQt6.QtCore import Qt, QTimer, pyqtSignal

# System imports
import sys
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

# Local imports
from config import PIECES_DIR, ICONS_DIR, TESTER_LOGS_DIR as LOGS_DIR, PIECE_TESTER_DEFAULT, PIECE_TESTER_MIN
from ui.ui_utils import setup_responsive_window, ResponsiveLayout, ResponsiveSplitter
from utils.utils import load_json_file, save_json_file


class BoardTile(QPushButton):
    """
    Enhanced board tile - UI intact, ready for new logic engine
    Handles piece display, overlays, and coordinate management
    """
    tileClicked = pyqtSignal(int, int)
    tileRightClicked = pyqtSignal(int, int)

    def __init__(self, row, col):
        super().__init__()
        self.row = row
        self.col = col
        self.piece_data = None  # Will hold piece data dict
        self.piece_color = None  # 'white' or 'black'
        self.overlays = []

        self.setFixedSize(64, 64)  # Standard chess square size
        self.clicked.connect(lambda: self.tileClicked.emit(self.row, self.col))

        # Set professional chess board colors
        if (row + col) % 2 == 0:
            self.base_color = "#f0d9b5"  # Light squares (cream)
            self.hover_color = "#fff2cc"  # Light hover
        else:
            self.base_color = "#b58863"  # Dark squares (brown)
            self.hover_color = "#8b6f47"  # Dark hover

        self.update_display()

    def set_piece(self, piece_data: Optional[dict], color: str = 'white'):
        """Set piece using piece data dict"""
        self.piece_data = piece_data
        self.piece_color = color
        self.update_display()

    def clear_piece(self):
        """Clear piece from tile"""
        self.piece_data = None
        self.piece_color = None
        self.update_display()

    def add_overlay(self, overlay_type):
        """Add visual overlay"""
        if overlay_type not in self.overlays:
            self.overlays.append(overlay_type)
        self.update_display()

    def remove_overlay(self, overlay_type):
        """Remove visual overlay"""
        if overlay_type in self.overlays:
            self.overlays.remove(overlay_type)
        self.update_display()

    def clear_overlays(self):
        """Clear all overlays"""
        self.overlays.clear()
        self.update_display()

    def update_display(self):
        """Update tile display with improved chess board styling"""
        # Base color
        color = self.base_color
        border = "1px solid #8b7355"  # Subtle border

        # Apply overlays with semi-transparent effects
        overlay_style = ""
        if 'move' in self.overlays:
            overlay_style = "border: 3px solid #4CAF50; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 " + color + ", stop:1 rgba(76, 175, 80, 0.3));"
        elif 'capture' in self.overlays:
            overlay_style = "border: 3px solid #F44336; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 " + color + ", stop:1 rgba(244, 67, 54, 0.3));"
        elif 'attack' in self.overlays:
            overlay_style = "border: 3px solid #F44336; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 " + color + ", stop:1 rgba(244, 67, 54, 0.3));"
        elif 'aoe' in self.overlays:
            overlay_style = "border: 3px solid #FF9800; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 " + color + ", stop:1 rgba(255, 152, 0, 0.3));"
        elif 'summon' in self.overlays:
            overlay_style = "border: 3px solid #9C27B0; background: qlineargradient(x1:0, y1:0, x2:1, y2:1, stop:0 " + color + ", stop:1 rgba(156, 39, 176, 0.3));"
        elif 'selected' in self.overlays:
            overlay_style = "border: 4px solid #FFD700;"

        # Enhanced styling
        self.setStyleSheet(f"""
            QPushButton {{
                background-color: {color};
                border: {border};
                border-radius: 2px;
                font-size: 32px;
                font-weight: bold;
                {overlay_style}
            }}
            QPushButton:hover {{
                background-color: {self.hover_color};
                border: 2px solid #5d4e37;
            }}
            QPushButton:pressed {{
                background-color: {self.hover_color};
                border: 2px solid #3d2e17;
            }}
        """)

        # Set piece icon or symbol
        if self.piece_data:
            icon_path = self.get_piece_icon_path()
            if icon_path and os.path.exists(icon_path):
                # Load and scale icon
                pixmap = QPixmap(icon_path)
                if not pixmap.isNull():
                    # Scale to fit tile with some padding
                    scaled_pixmap = pixmap.scaled(48, 48, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                    icon = QIcon(scaled_pixmap)
                    self.setIcon(icon)
                    self.setIconSize(scaled_pixmap.size())
                    self.setText("")  # Clear text when using icon
                else:
                    # Fallback to symbol if icon fails to load
                    self.setIcon(QIcon())
                    symbol = self.get_piece_symbol()
                    self.setText(symbol)
            else:
                # Fallback to symbol if no icon file
                self.setIcon(QIcon())
                symbol = self.get_piece_symbol()
                self.setText(symbol)

            # Enhanced tooltip with piece info
            piece_name = self.piece_data.get('name', 'Unknown')
            tooltip = f"<b>{piece_name}</b> ({self.piece_color})<br>"
            tooltip += f"Position: {chr(65+self.col)}{8-self.row}<br>"

            # Add piece stats if available
            max_points = self.piece_data.get('maxPoints', 0)
            starting_points = self.piece_data.get('startingPoints', 0)
            if max_points > 0:
                tooltip += f"Points: {starting_points}/{max_points}<br>"

            abilities = self.piece_data.get('abilities', [])
            if abilities:
                tooltip += f"Abilities: {len(abilities)}"

            self.setToolTip(tooltip)
        else:
            self.setIcon(QIcon())
            self.setText("")
            self.setToolTip(f"Empty square {chr(65+self.col)}{8-self.row}")

    def get_piece_symbol(self) -> str:
        """Get display symbol for piece"""
        if not self.piece_data:
            return ""

        piece_name = self.piece_data.get('name', 'Unknown')
        is_white = self.piece_color == 'white'

        symbols = {
            "King": "♔" if is_white else "♚",
            "Queen": "♕" if is_white else "♛",
            "Rook": "♖" if is_white else "♜",
            "Bishop": "♗" if is_white else "♝",
            "Knight": "♘" if is_white else "♞",
            "Pawn": "♙" if is_white else "♟",
        }

        return symbols.get(piece_name, "●" if is_white else "○")

    def get_piece_icon_path(self) -> Optional[str]:
        """Get the file path for piece icon"""
        if not self.piece_data:
            return None

        try:
            # Get appropriate icon based on color
            if self.piece_color == 'white':
                icon_filename = self.piece_data.get('whiteIcon', '')
            else:
                icon_filename = self.piece_data.get('blackIcon', '')

            if icon_filename:
                icon_path = os.path.join(ICONS_DIR, icon_filename)
                return icon_path
        except (KeyError, TypeError):
            pass

        return None

    def mousePressEvent(self, e):
        """Handle mouse press events"""
        if e.button() == Qt.MouseButton.RightButton:
            self.tileRightClicked.emit(self.row, self.col)
        else:
            super().mousePressEvent(e)


class PieceTester(QWidget):
    """
    Main piece tester class - UI intact, ready for new logic engine
    Provides interactive testing environment for Adventure Chess pieces
    """

    def __init__(self):
        super().__init__()
        self.setWindowTitle("Piece Tester - Adventure Chess")

        # Simple board state for now - will be replaced by new logic engine
        self.board = [[None for _ in range(8)] for _ in range(8)]
        self.board_colors = [[None for _ in range(8)] for _ in range(8)]
        self.current_turn = 1
        self.current_player = 'white'

        # UI state
        self.board_tiles = [[None for _ in range(8)] for _ in range(8)]
        self.selected_tile = None  # [row, col] for UI selection
        self.placement_mode = 'select'  # 'select', 'use', 'highlight', 'erase'
        self.highlight_color = 'move'
        self.selected_piece_data = None
        self.selected_color = 'white'

        # Load available pieces
        self.available_pieces = self.load_available_pieces()

        # Setup responsive sizing
        setup_responsive_window(self, PIECE_TESTER_DEFAULT, PIECE_TESTER_MIN)

        self.init_ui()
        self.reset_board()
        self.setup_shortcuts()

        # Log after UI is fully initialized
        if hasattr(self, 'log_text'):
            self.log("Piece Tester initialized - Ready for new logic engine integration")

    # ========== ZOOM FUNCTIONALITY ==========

    def zoom_in(self):
        """Zoom in the tester content"""
        # For piece tester, we can zoom the board and panels
        if hasattr(self, 'board_tiles'):
            # Increase board tile size
            for row in range(8):
                for col in range(8):
                    tile = self.board_tiles[row][col]
                    current_size = tile.size()
                    new_size = min(current_size.width() * 1.1, 100)  # Max 100px
                    tile.setFixedSize(int(new_size), int(new_size))

    def zoom_out(self):
        """Zoom out the tester content"""
        # For piece tester, we can zoom the board and panels
        if hasattr(self, 'board_tiles'):
            # Decrease board tile size
            for row in range(8):
                for col in range(8):
                    tile = self.board_tiles[row][col]
                    current_size = tile.size()
                    new_size = max(current_size.width() * 0.9, 32)  # Min 32px
                    tile.setFixedSize(int(new_size), int(new_size))

    def reset_zoom(self):
        """Reset zoom to 100%"""
        # Reset board tiles to default size
        if hasattr(self, 'board_tiles'):
            for row in range(8):
                for col in range(8):
                    tile = self.board_tiles[row][col]
                    tile.setFixedSize(64, 64)  # Default size
    
    def init_ui(self):
        """Initialize UI - keeping original layout"""
        # Use responsive splitter
        main_splitter = ResponsiveSplitter(Qt.Orientation.Horizontal)

        # Left panel - Controls
        left_panel = self.create_left_panel()
        main_splitter.addWidget(left_panel)

        # Center - Board
        board_widget = self.create_board_widget()

        # Create a container to control board alignment
        board_container = QWidget()
        board_layout = QVBoxLayout(board_container)
        board_layout.setContentsMargins(0, 0, 0, 0)
        board_layout.setSpacing(0)
        board_layout.addWidget(board_widget)
        board_layout.addStretch()  # Push board to top

        main_splitter.addWidget(board_container)

        # Right panel - Info and Log
        right_panel = self.create_right_panel()
        main_splitter.addWidget(right_panel)

        # Set splitter sizes
        main_splitter.setSizes([140, 320, 140])
        main_splitter.setCollapsible(0, False)
        main_splitter.setCollapsible(1, False)
        main_splitter.setCollapsible(2, False)

        main_splitter.setStretchFactor(0, 0)
        main_splitter.setStretchFactor(1, 1)
        main_splitter.setStretchFactor(2, 0)

        # Ensure content aligns to top
        main_splitter.setChildrenCollapsible(False)

        main_layout = ResponsiveLayout.create_vbox(margin=0, spacing=0)
        main_layout.addWidget(main_splitter)

        self.setLayout(main_layout)
    
    def create_left_panel(self):
        """Create left control panel"""
        panel = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)

        # Mode selection - keeping original modes
        mode_group = QGroupBox("Mode")
        mode_layout = QVBoxLayout()

        self.mode_button_group = QButtonGroup()

        self.select_radio = QRadioButton("Select")
        self.select_radio.setChecked(True)
        self.mode_button_group.addButton(self.select_radio, 0)
        mode_layout.addWidget(self.select_radio)

        self.use_radio = QRadioButton("Use")
        self.mode_button_group.addButton(self.use_radio, 1)
        mode_layout.addWidget(self.use_radio)

        self.highlight_radio = QRadioButton("Highlight")
        self.mode_button_group.addButton(self.highlight_radio, 2)
        mode_layout.addWidget(self.highlight_radio)

        self.erase_radio = QRadioButton("Erase")
        self.mode_button_group.addButton(self.erase_radio, 3)
        mode_layout.addWidget(self.erase_radio)

        mode_group.setLayout(mode_layout)
        layout.addWidget(mode_group)

        # Connect mode button group to handler
        self.mode_button_group.buttonClicked.connect(self.on_mode_changed)

        # Piece selection
        piece_group = QGroupBox("Piece Selection")
        piece_layout = QVBoxLayout()

        self.piece_combo = QComboBox()
        self.populate_piece_combo()
        piece_layout.addWidget(QLabel("Piece:"))
        piece_layout.addWidget(self.piece_combo)

        # Color selection with enhanced styling
        color_label = QLabel("Color:")
        color_label.setStyleSheet("font-weight: bold;")
        piece_layout.addWidget(color_label)

        color_layout = QHBoxLayout()
        self.white_radio = QRadioButton("⚪ White")
        self.white_radio.setChecked(True)
        self.white_radio.setStyleSheet("""
            QRadioButton {
                font-weight: bold;
                color: #e2e8f0;
            }
            QRadioButton::indicator::checked {
                background-color: #f7fafc;
                border: 2px solid #4a5568;
            }
        """)

        self.black_radio = QRadioButton("⚫ Black")
        self.black_radio.setStyleSheet("""
            QRadioButton {
                font-weight: bold;
                color: #e2e8f0;
            }
            QRadioButton::indicator::checked {
                background-color: #2d3748;
                border: 2px solid #718096;
            }
        """)

        # Connect color change handlers
        self.white_radio.toggled.connect(self.on_color_changed)
        self.black_radio.toggled.connect(self.on_color_changed)

        color_layout.addWidget(self.white_radio)
        color_layout.addWidget(self.black_radio)
        piece_layout.addLayout(color_layout)

        piece_group.setLayout(piece_layout)
        layout.addWidget(piece_group)

        # Turn controls - keeping original functionality
        turn_group = QGroupBox("Turn Control")
        turn_layout = QVBoxLayout()

        self.turn_label = QLabel("Turn 1 - White")
        turn_layout.addWidget(self.turn_label)

        self.next_turn_btn = QPushButton("Next Turn")
        self.next_turn_btn.clicked.connect(self.next_turn)
        turn_layout.addWidget(self.next_turn_btn)

        self.auto_turn_check = QCheckBox("Auto Turn (5s)")
        self.auto_turn_check.toggled.connect(self.toggle_auto_turn)
        turn_layout.addWidget(self.auto_turn_check)

        turn_group.setLayout(turn_layout)
        layout.addWidget(turn_group)

        # Game controls
        game_group = QGroupBox("Game Control")
        game_layout = QVBoxLayout()

        self.reset_btn = QPushButton("Reset Board")
        self.reset_btn.clicked.connect(self.reset_board)
        game_layout.addWidget(self.reset_btn)

        self.save_btn = QPushButton("Save State")
        self.save_btn.clicked.connect(self.save_state)
        game_layout.addWidget(self.save_btn)

        self.load_btn = QPushButton("Load State")
        self.load_btn.clicked.connect(self.load_state)
        game_layout.addWidget(self.load_btn)

        game_group.setLayout(game_layout)
        layout.addWidget(game_group)

        layout.addStretch()
        panel.setLayout(layout)
        return panel
    
    def create_board_widget(self):
        """MIGRATED: Create enhanced chess board widget"""
        # Create container with chess board styling
        container = QWidget()
        container.setFixedSize(580, 580)  # Larger to accommodate labels
        container.setStyleSheet("""
            QWidget {
                background-color: #2d1810;
                border: 3px solid #8b7355;
                border-radius: 5px;
            }
        """)
        
        # Main layout for the entire board area
        main_layout = QVBoxLayout(container)
        main_layout.setContentsMargins(10, 5, 10, 10)
        
        # Top rank labels (8, 7, 6, ...)
        top_labels = QHBoxLayout()
        top_labels.addWidget(QLabel(""))  # Corner space
        for col in range(8):
            label = QLabel(chr(65 + col))  # A, B, C, ...
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            label.setStyleSheet("color: #f0d9b5; font-weight: bold; font-size: 14px;")
            label.setFixedWidth(64)
            top_labels.addWidget(label)
        main_layout.addLayout(top_labels)
        
        # Board area with file labels
        board_area = QHBoxLayout()
        
        # Left file labels (8, 7, 6, ...)
        left_labels = QVBoxLayout()
        for row in range(8):
            label = QLabel(str(8 - row))  # 8, 7, 6, ...
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            label.setStyleSheet("color: #f0d9b5; font-weight: bold; font-size: 14px;")
            label.setFixedHeight(64)
            left_labels.addWidget(label)
        board_area.addLayout(left_labels)
        
        # Actual chess board
        board_widget = QWidget()
        board_widget.setFixedSize(512, 512)  # 8 * 64
        layout = QGridLayout(board_widget)
        layout.setSpacing(0)  # No spacing for seamless board
        layout.setContentsMargins(0, 0, 0, 0)
        
        # Create tiles with new coordinate system
        for row in range(8):
            for col in range(8):
                tile = BoardTile(row, col)
                tile.tileClicked.connect(self.on_tile_clicked)
                tile.tileRightClicked.connect(self.on_tile_right_clicked)
                layout.addWidget(tile, row, col)
                self.board_tiles[row][col] = tile
        
        board_widget.setLayout(layout)
        board_area.addWidget(board_widget)
        main_layout.addLayout(board_area)
        
        return container
    
    def create_right_panel(self):
        """Create right info panel"""
        panel = QWidget()
        layout = QVBoxLayout()
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # Piece info
        self.piece_info_group = QGroupBox("Piece Information")
        info_layout = QVBoxLayout()
        
        self.piece_info_text = QTextEdit()
        self.piece_info_text.setMaximumHeight(200)
        self.piece_info_text.setReadOnly(True)
        info_layout.addWidget(self.piece_info_text)
        
        self.piece_info_group.setLayout(info_layout)
        layout.addWidget(self.piece_info_group)
        
        # Game log
        self.log_group = QGroupBox("Game Log")
        log_layout = QVBoxLayout()
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        # Log controls
        log_controls = QHBoxLayout()
        self.clear_log_btn = QPushButton("Clear")
        self.clear_log_btn.clicked.connect(self.clear_log)
        self.save_log_btn = QPushButton("Save Log")
        self.save_log_btn.clicked.connect(self.save_log)
        log_controls.addWidget(self.clear_log_btn)
        log_controls.addWidget(self.save_log_btn)
        log_layout.addLayout(log_controls)
        
        self.log_group.setLayout(log_layout)
        layout.addWidget(self.log_group)
        
        # Statistics - NEW: Enhanced with logic engine data
        self.stats_group = QGroupBox("Statistics")
        stats_layout = QVBoxLayout()
        
        self.stats_label = QLabel("No statistics available")
        stats_layout.addWidget(self.stats_label)
        
        self.stats_group.setLayout(stats_layout)
        layout.addWidget(self.stats_group)
        
        panel.setLayout(layout)
        return panel
    
    def load_available_pieces(self):
        """Load available pieces using standardized SimpleBridge"""
        pieces = {}

        try:
            # Use SimpleBridge to get piece list and load pieces
            from utils.simple_bridge import simple_bridge
            piece_names = simple_bridge.list_pieces()

            for piece_name in piece_names:
                try:
                    piece_data, error = simple_bridge.load_piece_for_ui(piece_name)
                    if piece_data and not error:
                        pieces[piece_data.get('name', piece_name)] = piece_data
                    elif error:
                        # Use print instead of log during initialization
                        print(f"Error loading piece {piece_name}: {error}")
                except Exception as e:
                    print(f"Error loading piece {piece_name}: {str(e)}")

        except Exception as e:
            print(f"Error loading pieces: {str(e)}")

        return pieces
    
    def populate_piece_combo(self):
        """Populate piece combo box"""
        self.piece_combo.clear()
        for piece_name in sorted(self.available_pieces.keys()):
            self.piece_combo.addItem(piece_name)
        
        # Connect selection change
        self.piece_combo.currentTextChanged.connect(self.on_piece_selected)
        
        # Select first piece if available
        if self.piece_combo.count() > 0:
            self.on_piece_selected(self.piece_combo.currentText())
    
    def on_piece_selected(self, piece_name):
        """Handle piece selection"""
        if piece_name in self.available_pieces:
            self.selected_piece_data = self.available_pieces[piece_name]
            if hasattr(self, 'log_text'):
                self.log(f"Selected piece: {piece_name}")
    
    def on_tile_clicked(self, row: int, col: int):
        """Handle tile clicks - ready for new logic engine"""
        if self.placement_mode == 'select':
            self.select_piece_mode_click(row, col)
        elif self.placement_mode == 'use':
            self.use_piece_mode_click(row, col)
        elif self.placement_mode == 'highlight':
            self.toggle_highlight(row, col)
        elif self.placement_mode == 'erase':
            self.erase_piece(row, col)
        else:
            # Default to placing piece
            self.place_piece(row, col)

    def on_tile_right_clicked(self, row: int, col: int):
        """Handle right clicks on tiles"""
        piece_data = self.board[row][col]
        piece_color = self.board_colors[row][col]

        if piece_data:
            self.show_piece_context_menu(piece_data, piece_color, row, col)
        else:
            self.log(f"Right-clicked empty tile at {chr(65+col)}{8-row}")
    
    def on_mode_changed(self, button):
        """Handle mode selection changes"""
        button_id = self.mode_button_group.id(button)
        mode_map = {
            0: 'select',
            1: 'use',
            2: 'highlight',
            3: 'erase'
        }

        old_mode = self.placement_mode
        self.placement_mode = mode_map.get(button_id, 'select')

        # Clear overlays when changing modes
        if old_mode != self.placement_mode:
            self.clear_all_overlays()

        self.log(f"Mode changed to: {self.placement_mode}")

    def on_color_changed(self):
        """Handle color selection changes"""
        if self.white_radio.isChecked():
            self.selected_color = "white"
            selected_color = "White"
        else:
            self.selected_color = "black"
            selected_color = "Black"

        self.log(f"Color changed to: {selected_color}")
    
    def select_piece_mode_click(self, row: int, col: int):
        """Handle piece selection AND placement in select mode"""
        # In select mode, we can both select existing pieces and place new ones

        piece_data = self.board[row][col]
        piece_color = self.board_colors[row][col]

        if piece_data:
            # If there's a piece here, select it
            self.selected_tile = [row, col]

            # Clear previous selection overlays
            self.clear_all_overlays()

            # Highlight selected piece
            self.board_tiles[row][col].add_overlay('selected')

            self.update_piece_info_panel(piece_data, piece_color)
            piece_name = piece_data.get('name', 'Unknown')
            self.log(f"Selected {piece_name} ({piece_color}) at {chr(65+col)}{8-row}")
        else:
            # If there's no piece here, place a new piece
            self.place_piece(row, col)
    
    def use_piece_mode_click(self, row: int, col: int):
        """Handle piece usage - improved with range validation and selection clearing"""
        if not self.selected_tile:
            # Select piece to use
            piece_data = self.board[row][col]
            piece_color = self.board_colors[row][col]

            if piece_data:
                self.selected_tile = [row, col]

                # Show movement options (placeholder)
                self.show_movement_overlay(row, col)
                self.update_piece_info_panel(piece_data, piece_color)
                piece_name = piece_data.get('name', 'Unknown')
                self.log(f"Selected {piece_name} for action")
            else:
                self.log("No piece to select for action")
        else:
            # Check if clicked position is within range/valid for action
            if self.is_valid_action_target(row, col):
                # Execute action with selected piece
                self.execute_piece_action(row, col)
            else:
                # Click outside valid range - check if clicking on a different piece
                piece_data = self.board[row][col]
                if piece_data:
                    # Clicking on a different piece - select it instead
                    piece_color = self.board_colors[row][col]
                    self.selected_tile = [row, col]
                    self.show_movement_overlay(row, col)
                    self.update_piece_info_panel(piece_data, piece_color)
                    piece_name = piece_data.get('name', 'Unknown')
                    self.log(f"Selected {piece_name} for action")
                else:
                    # Click outside range on empty square - clear selection
                    self.log("Clicked outside valid range - clearing selection")
                    self.clear_selection()

    def is_valid_action_target(self, target_row: int, target_col: int) -> bool:
        """Check if target position is valid for the selected piece's action"""
        if not self.selected_tile:
            return False

        # Check if target has a movement overlay (indicates valid move)
        target_tile = self.board_tiles[target_row][target_col]
        return 'move' in target_tile.overlays or 'capture' in target_tile.overlays or 'aoe' in target_tile.overlays

    def execute_piece_action(self, target_row: int, target_col: int):
        """Execute piece action with proper rule engine validation"""
        if not self.selected_tile:
            return

        source_row, source_col = self.selected_tile
        piece_data = self.board[source_row][source_col]
        piece_color = self.board_colors[source_row][source_col]

        if not piece_data:
            self.log("Selected piece not found")
            return

        # Validate move using rule engine
        try:
            from schemas.base import Coordinate
            from rule_engine.movement.validator import MovementValidator, ValidationLevel

            validator = MovementValidator(ValidationLevel.STANDARD)
            game_state = self._create_enhanced_game_state()

            current_position = Coordinate(row=source_row, col=source_col)
            target_position = Coordinate(row=target_row, col=target_col)

            # Validate the movement
            validation_result = validator.validate_movement(
                piece_data, current_position, target_position, game_state
            )

            if not validation_result.is_valid:
                self.log(f"Invalid move: {validation_result.error_message}")
                return

        except Exception as e:
            self.log(f"Validation error: {str(e)}, allowing move")

        piece_name = piece_data.get('name', 'Unknown')
        target_piece = self.board[target_row][target_col]

        if target_piece is None:
            # Move to empty square
            self.board[target_row][target_col] = piece_data
            self.board_colors[target_row][target_col] = piece_color
            self.board[source_row][source_col] = None
            self.board_colors[source_row][source_col] = None

            self.log(f"Moved {piece_name} to {chr(65+target_col)}{8-target_row}")
        else:
            # Capture enemy piece
            target_color = self.board_colors[target_row][target_col]
            if piece_color != target_color:
                target_name = target_piece.get('name', 'Unknown')

                # Execute capture
                self.board[target_row][target_col] = piece_data
                self.board_colors[target_row][target_col] = piece_color
                self.board[source_row][source_col] = None
                self.board_colors[source_row][source_col] = None

                self.log(f"{piece_name} captured {target_name} at {chr(65+target_col)}{8-target_row}")
            else:
                self.log("Cannot capture your own piece")
                return

        self.update_board_display()
        self.clear_selection()
    
    def place_piece(self, row: int, col: int):
        """Place piece using simple board state"""
        if not self.selected_piece_data:
            self.log("No piece selected for placement")
            return

        # Check if position is occupied
        if self.board[row][col] is not None:
            self.log(f"Position {chr(65+col)}{8-row} is already occupied")
            return

        # Place piece on simple board
        self.board[row][col] = self.selected_piece_data
        self.board_colors[row][col] = self.selected_color

        self.update_board_display()
        piece_name = self.selected_piece_data.get('name', 'Unknown')
        self.log(f"Placed {piece_name} ({self.selected_color}) at {chr(65+col)}{8-row}")
    
    def erase_piece(self, row: int, col: int):
        """Erase piece from simple board state"""
        piece_data = self.board[row][col]

        if not piece_data:
            self.log(f"No piece to erase at {chr(65+col)}{8-row}")
            return

        piece_name = piece_data.get('name', 'Unknown')
        self.log(f"Erased {piece_name} at {chr(65+col)}{8-row}")

        self.board[row][col] = None
        self.board_colors[row][col] = None

        # Clear selection if erased piece was selected
        if self.selected_tile and self.selected_tile == [row, col]:
            self.clear_selection()

        self.update_board_display()
    
    def toggle_highlight(self, row: int, col: int):
        """Toggle highlight on tile (unchanged)"""
        tile = self.board_tiles[row][col]
        if self.highlight_color in tile.overlays:
            tile.remove_overlay(self.highlight_color)
        else:
            tile.add_overlay(self.highlight_color)
        
        self.log(f"Toggled {self.highlight_color} highlight at {chr(65+col)}{8-row}")
    
    def show_movement_overlay(self, source_row: int, source_col: int):
        """Show movement overlay using proper rule engine validation"""
        self.clear_all_overlays()

        # Get piece data
        piece_data = self.board[source_row][source_col]
        if not piece_data:
            return

        # Use rule engine to calculate valid moves
        try:
            from schemas.base import Coordinate
            from rule_engine.movement.patterns import MovementPatternProcessor

            # Create movement processor
            processor = MovementPatternProcessor()

            # Convert current board state to enhanced game state for validation
            game_state = self._create_enhanced_game_state()
            current_position = Coordinate(row=source_row, col=source_col)

            # Calculate valid moves using rule engine
            movement_result = processor.calculate_valid_moves(piece_data, current_position, game_state)

            # Apply overlays based on rule engine results
            for move_pos in movement_result.valid_moves:
                if 0 <= move_pos.row < 8 and 0 <= move_pos.col < 8:
                    self.board_tiles[move_pos.row][move_pos.col].add_overlay('move')

            for attack_pos in movement_result.valid_attacks:
                if 0 <= attack_pos.row < 8 and 0 <= attack_pos.col < 8:
                    self.board_tiles[attack_pos.row][attack_pos.col].add_overlay('capture')

            for action_pos in movement_result.valid_actions:
                if 0 <= action_pos.row < 8 and 0 <= action_pos.col < 8:
                    self.board_tiles[action_pos.row][action_pos.col].add_overlay('aoe')

            # Log movement info
            piece_name = piece_data.get('name', 'Unknown')
            move_count = len(movement_result.valid_moves)
            attack_count = len(movement_result.valid_attacks)
            action_count = len(movement_result.valid_actions)
            self.log(f"{piece_name}: {move_count} moves, {attack_count} attacks, {action_count} actions available")

        except Exception as e:
            # Fallback to simple adjacent movement if rule engine fails
            self.log(f"Rule engine error: {str(e)}, using fallback movement")
            self._show_fallback_movement_overlay(source_row, source_col)

        # Highlight selected piece
        self.board_tiles[source_row][source_col].add_overlay('selected')

    def _create_enhanced_game_state(self):
        """Create enhanced game state from current board state for rule engine"""
        from rule_engine.game_state import EnhancedGameState, EnhancedPieceState
        from schemas.base import Coordinate
        from core.tester_engine import PieceColor

        game_state = EnhancedGameState()

        # Convert board pieces to enhanced pieces
        for row in range(8):
            for col in range(8):
                piece_data = self.board[row][col]
                piece_color = self.board_colors[row][col]

                if piece_data:
                    # Convert color string to PieceColor enum
                    color_enum = PieceColor.WHITE if piece_color == 'white' else PieceColor.BLACK

                    # Create enhanced piece state
                    piece_id = f"piece_{row}_{col}"
                    enhanced_piece = EnhancedPieceState(
                        id=piece_id,
                        piece_type=piece_data.get('role', 'unknown'),
                        owner=color_enum,
                        position=Coordinate(row=row, col=col),
                        current_points=piece_data.get('startingPoints', 1),
                        max_points=piece_data.get('maxPoints', 1)
                    )

                    game_state.pieces[piece_id] = enhanced_piece

        return game_state

    def _show_fallback_movement_overlay(self, source_row: int, source_col: int):
        """Fallback movement overlay for when rule engine fails"""
        # Simple adjacent movement as fallback
        for row in range(max(0, source_row-1), min(8, source_row+2)):
            for col in range(max(0, source_col-1), min(8, source_col+2)):
                if row != source_row or col != source_col:
                    if self.board[row][col] is None:
                        # Empty square - show as move option
                        self.board_tiles[row][col].add_overlay('move')
                    else:
                        # Occupied square - show as capture option if different color
                        source_color = self.board_colors[source_row][source_col]
                        target_color = self.board_colors[row][col]
                        if source_color != target_color:
                            self.board_tiles[row][col].add_overlay('capture')

    def clear_all_overlays(self):
        """Clear all tile overlays"""
        for row in range(8):
            for col in range(8):
                self.board_tiles[row][col].clear_overlays()
    
    def clear_selection(self):
        """Clear current selection"""
        self.selected_tile = None
        self.clear_all_overlays()
        self.update_piece_info_panel(None, None)

    def update_board_display(self):
        """Update board display using simple board state"""
        # Clear all tiles first
        for row in range(8):
            for col in range(8):
                self.board_tiles[row][col].clear_piece()

        # Set pieces from simple board state
        for row in range(8):
            for col in range(8):
                piece_data = self.board[row][col]
                piece_color = self.board_colors[row][col]
                if piece_data:
                    self.board_tiles[row][col].set_piece(piece_data, piece_color)

        # Update turn display
        self.turn_label.setText(f"Turn {self.current_turn} - {self.current_player.title()}")

        # Update statistics
        self.update_statistics()
    
    def update_piece_info_panel(self, piece_data: Optional[dict], piece_color: Optional[str]):
        """Update piece info using simple piece data"""
        if not piece_data:
            self.piece_info_text.setText("No piece selected")
            return

        piece_name = piece_data.get('name', 'Unknown')
        info_text = f"<h3>{piece_name} ({piece_color.title() if piece_color else 'Unknown'})</h3>"

        if self.selected_tile:
            row, col = self.selected_tile
            info_text += f"<b>Position:</b> {chr(65+col)}{8-row}<br>"

        # Show piece properties
        max_points = piece_data.get('maxPoints', 0)
        starting_points = piece_data.get('startingPoints', 0)
        if max_points > 0:
            info_text += f"<b>Points:</b> {starting_points}/{max_points}<br>"

        role = piece_data.get('role', 'Unknown')
        info_text += f"<b>Role:</b> {role}<br>"

        can_capture = piece_data.get('canCapture', False)
        info_text += f"<b>Can Capture:</b> {'Yes' if can_capture else 'No'}<br>"

        movement = piece_data.get('movement', {})
        if movement:
            move_type = movement.get('type', 'Unknown')
            move_distance = movement.get('distance', 0)
            info_text += f"<b>Movement:</b> {move_type.title()}"
            if move_distance > 0:
                info_text += f" (Range: {move_distance})"
            info_text += "<br>"

        abilities = piece_data.get('abilities', [])
        if abilities:
            info_text += f"<b>Abilities:</b> {len(abilities)}<br>"
            for ability in abilities[:5]:  # Show first 5 abilities
                info_text += f"  • {ability}<br>"
            if len(abilities) > 5:
                info_text += f"  ... and {len(abilities) - 5} more<br>"

        self.piece_info_text.setHtml(info_text)
    
    def update_statistics(self):
        """Update statistics using simple board state"""
        white_pieces = 0
        black_pieces = 0
        total_pieces = 0

        for row in range(8):
            for col in range(8):
                if self.board[row][col] is not None:
                    total_pieces += 1
                    if self.board_colors[row][col] == 'white':
                        white_pieces += 1
                    else:
                        black_pieces += 1

        stats_text = f"Turn: {self.current_turn}\n"
        stats_text += f"Current Player: {self.current_player.title()}\n"
        stats_text += f"White Pieces: {white_pieces}\n"
        stats_text += f"Black Pieces: {black_pieces}\n"
        stats_text += f"Total Pieces: {total_pieces}"

        self.stats_label.setText(stats_text)

    def show_piece_context_menu(self, piece_data: dict, piece_color: str, row: int, col: int):
        """Show context menu for piece"""
        # For now, just show piece info
        self.selected_tile = [row, col]
        self.update_piece_info_panel(piece_data, piece_color)
        piece_name = piece_data.get('name', 'Unknown')
        self.log(f"Showing info for {piece_name} at {chr(65+col)}{8-row}")
    
    def next_turn(self):
        """Advance turn using simple turn counter"""
        self.current_turn += 1
        # Switch current player
        self.current_player = 'black' if self.current_player == 'white' else 'white'

        self.log(f"Advanced to Turn {self.current_turn} - {self.current_player.title()}")
        self.update_board_display()
        self.clear_selection()

    def toggle_auto_turn(self, enabled: bool):
        """Toggle automatic turn advancement"""
        if not hasattr(self, 'auto_turn_timer'):
            self.auto_turn_timer = QTimer()
            self.auto_turn_timer.timeout.connect(self.next_turn)

        if enabled:
            self.auto_turn_timer.start(5000)  # 5 seconds
            self.log("Auto turn enabled (5 seconds)")
        else:
            self.auto_turn_timer.stop()
            self.log("Auto turn disabled")

    def reset_board(self):
        """Reset board to empty state"""
        # Reset simple board state
        self.board = [[None for _ in range(8)] for _ in range(8)]
        self.board_colors = [[None for _ in range(8)] for _ in range(8)]
        self.current_turn = 1
        self.current_player = 'white'
        self.selected_tile = None

        self.clear_all_overlays()
        self.update_board_display()
        self.update_piece_info_panel(None, None)

        self.log("Board reset - ready for new logic engine")
    
    def save_state(self):
        """Save simple board state"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Game State", os.path.join(LOGS_DIR, "game_state.json"), "JSON Files (*.json)"
        )

        if filename:
            try:
                # Convert simple board state to serializable format
                state_data = {
                    'turn_counter': self.current_turn,
                    'current_player': self.current_player,
                    'board': [],
                    'board_colors': []
                }

                # Save board state
                for row in range(8):
                    board_row = []
                    color_row = []
                    for col in range(8):
                        board_row.append(self.board[row][col])
                        color_row.append(self.board_colors[row][col])
                    state_data['board'].append(board_row)
                    state_data['board_colors'].append(color_row)

                with open(filename, 'w') as f:
                    json.dump(state_data, f, indent=2)

                self.log(f"Game state saved to {filename}")
                QMessageBox.information(self, "Success", "Game state saved successfully!")

            except Exception as e:
                self.log(f"Error saving state: {str(e)}")
                QMessageBox.warning(self, "Error", f"Failed to save state: {str(e)}")
    
    def load_state(self):
        """Load simple board state"""
        filename, _ = QFileDialog.getOpenFileName(
            self, "Load Game State", LOGS_DIR, "JSON Files (*.json)"
        )

        if filename:
            try:
                with open(filename, 'r') as f:
                    state_data = json.load(f)

                # Load simple board state
                self.current_turn = state_data.get('turn_counter', 1)
                self.current_player = state_data.get('current_player', 'white')

                # Load board data
                board_data = state_data.get('board', [])
                color_data = state_data.get('board_colors', [])

                # Reset board first
                self.board = [[None for _ in range(8)] for _ in range(8)]
                self.board_colors = [[None for _ in range(8)] for _ in range(8)]

                # Load pieces
                for row in range(min(8, len(board_data))):
                    for col in range(min(8, len(board_data[row]))):
                        self.board[row][col] = board_data[row][col]
                        if row < len(color_data) and col < len(color_data[row]):
                            self.board_colors[row][col] = color_data[row][col]

                self.clear_selection()
                self.update_board_display()

                self.log(f"Game state loaded from {filename}")
                QMessageBox.information(self, "Success", "Game state loaded successfully!")

            except Exception as e:
                self.log(f"Error loading state: {str(e)}")
                QMessageBox.warning(self, "Error", f"Failed to load state: {str(e)}")
    
    def setup_shortcuts(self):
        """Setup keyboard shortcuts (unchanged)"""
        # Turn controls
        next_turn_shortcut = QShortcut(QKeySequence("Space"), self)
        next_turn_shortcut.activated.connect(self.next_turn)
        
        reset_shortcut = QShortcut(QKeySequence("Ctrl+R"), self)
        reset_shortcut.activated.connect(self.reset_board)
        
        # Mode shortcuts - Fixed to actually change mode
        select_shortcut = QShortcut(QKeySequence("1"), self)
        select_shortcut.activated.connect(lambda: self.set_mode_by_shortcut(self.select_radio))
        
        use_shortcut = QShortcut(QKeySequence("2"), self)
        use_shortcut.activated.connect(lambda: self.set_mode_by_shortcut(self.use_radio))
        
        highlight_shortcut = QShortcut(QKeySequence("3"), self)
        highlight_shortcut.activated.connect(lambda: self.set_mode_by_shortcut(self.highlight_radio))
        
        erase_shortcut = QShortcut(QKeySequence("4"), self)
        erase_shortcut.activated.connect(lambda: self.set_mode_by_shortcut(self.erase_radio))
        
        # File operations
        save_shortcut = QShortcut(QKeySequence("Ctrl+S"), self)
        save_shortcut.activated.connect(self.save_state)
        
        load_shortcut = QShortcut(QKeySequence("Ctrl+O"), self)
        load_shortcut.activated.connect(self.load_state)
        
        self.log("Keyboard shortcuts enabled")
    
    def set_mode_by_shortcut(self, radio_button):
        """Set mode via keyboard shortcut - ensures both visual and actual mode change"""
        radio_button.setChecked(True)
        self.on_mode_changed(radio_button)
    
    def log(self, message: str):
        """Add message to log"""
        self.log_text.append(f"[Turn {self.current_turn}] {message}")
        # Auto-scroll to bottom
        scrollbar = self.log_text.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())
    
    def clear_log(self):
        """Clear the log"""
        self.log_text.clear()
        self.log("Log cleared")
    
    def save_log(self):
        """Save log to file"""
        filename, _ = QFileDialog.getSaveFileName(
            self, "Save Log", os.path.join(LOGS_DIR, "piece_tester_log.txt"), "Text Files (*.txt)"
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write(self.log_text.toPlainText())
                QMessageBox.information(self, "Success", f"Log saved to {filename}")
            except Exception as e:
                QMessageBox.warning(self, "Error", f"Failed to save log: {str(e)}")


if __name__ == "__main__":
    import sys
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    tester = PieceTester()
    tester.show()
    sys.exit(app.exec())