#!/usr/bin/env python3
"""Swap Places Tag Resolver for Adventure Chess Rule Engine"""

from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect

class SwapPlacesTagResolver(BaseTagResolver):
    def _get_tag_name(self) -> str:
        return "swapPlaces"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.ACTION
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(effect_type="swap_places", priority=70, immediate=True, parameters={"action": "swap_places"})]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        return {"requires_target": True, "target_type": "piece", "min_targets": 1, "max_targets": 2}
