#!/usr/bin/env python3
"""
Comprehensive Ability Tags Data Flow Test
Verifies the complete path: UI Selection → EditorDataInterface → SimpleBridge → JSON → Load Back
"""

import sys
import os
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_ability_tags_data_flow():
    """Test the complete ability tags data flow"""
    
    print("=== ABILITY TAGS DATA FLOW VERIFICATION ===")
    
    # Test 1: Verify tag count and structure
    print("\n1. Verifying Tag Count and Structure...")
    
    try:
        from config import ABILITY_TAGS, get_canonical_ability_tags
        
        # Count tags by category
        action_count = len(ABILITY_TAGS['action'])
        targeting_count = len(ABILITY_TAGS['targeting'])
        condition_count = len(ABILITY_TAGS['condition'])
        special_count = len(ABILITY_TAGS['special'])
        total_count = action_count + targeting_count + condition_count + special_count
        
        print(f"✓ Action Tags: {action_count}")
        print(f"✓ Targeting Tags: {targeting_count}")
        print(f"✓ Condition Tags: {condition_count}")
        print(f"✓ Special Tags: {special_count}")
        print(f"✓ TOTAL TAGS: {total_count}")
        
        # Verify canonical tags function
        canonical_tags = get_canonical_ability_tags()
        if len(canonical_tags) == total_count:
            print(f"✓ Canonical tags function returns correct count: {len(canonical_tags)}")
        else:
            print(f"❌ Canonical tags mismatch: {len(canonical_tags)} vs {total_count}")
            return False
            
    except Exception as e:
        print(f"❌ Tag structure verification failed: {e}")
        return False
    
    # Test 2: Test UI tag collection
    print("\n2. Testing UI Tag Collection...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # Create minimal QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from editors.ability_editor import AbilityEditorWindow
        from utils.simple_bridge import simple_bridge
        
        # Create ability editor instance
        editor = AbilityEditorWindow()
        
        # Verify tag_groups exists and has correct count
        if hasattr(editor, 'tag_groups'):
            ui_tag_count = len(editor.tag_groups)
            print(f"✓ UI tag_groups created with {ui_tag_count} tags")
            
            if ui_tag_count == total_count:
                print(f"✓ UI tag count matches config: {ui_tag_count}")
            else:
                print(f"❌ UI tag count mismatch: {ui_tag_count} vs {total_count}")
                return False
        else:
            print("❌ tag_groups not found in ability editor")
            return False
        
        # Test selecting specific tags - block signals to prevent interference
        test_tags = ['move', 'summon', 'range', 'adjacencyRequired', 'shareSpace']
        for tag in test_tags:
            if tag in editor.tag_groups:
                checkbox = editor.tag_groups[tag]
                checkbox.blockSignals(True)
                checkbox.setChecked(True)
                checkbox.blockSignals(False)
                print(f"✓ Selected tag: {tag}")
            else:
                print(f"❌ Tag not found in UI: {tag}")
                return False
        
        # Collect data using SimpleBridge
        ability_data = simple_bridge.get_ability_data_from_ui(editor)
        
        # Verify tags are collected
        if 'tags' in ability_data:
            collected_tags = ability_data['tags']
            print(f"✓ Tags collected: {collected_tags}")
            
            # Verify all selected tags are present
            for tag in test_tags:
                if tag in collected_tags:
                    print(f"✓ Tag {tag} properly collected")
                else:
                    print(f"❌ Tag {tag} missing from collected data")
                    return False
        else:
            print("❌ No tags field in collected data")
            return False
            
    except Exception as e:
        print(f"❌ UI tag collection test failed: {e}")
        return False
    
    # Test 3: Test save/load cycle
    print("\n3. Testing Save/Load Cycle...")
    
    try:
        # Set basic ability info
        editor.name_edit.setText("TestTagsAbility")
        editor.description_edit.setPlainText("Test ability for tags data flow")
        editor.cost_spin.setValue(3)
        
        # Save the ability
        success, error = simple_bridge.save_ability_from_ui(editor, "TestTagsAbility")
        
        if not success:
            print(f"❌ Save failed: {error}")
            return False
        
        print("✓ Ability saved successfully")
        
        # Load the ability back
        loaded_data, load_error = simple_bridge.load_ability_for_ui("TestTagsAbility")
        
        if load_error:
            print(f"❌ Load failed: {load_error}")
            return False
        
        # Verify tags are preserved
        if 'tags' in loaded_data:
            loaded_tags = loaded_data['tags']
            print(f"✓ Tags loaded: {loaded_tags}")
            
            # Check each tag is preserved
            for tag in test_tags:
                if tag in loaded_tags:
                    print(f"✓ Tag {tag} preserved in save/load")
                else:
                    print(f"❌ Tag {tag} lost in save/load")
                    return False
        else:
            print("❌ No tags field in loaded data")
            return False
            
    except Exception as e:
        print(f"❌ Save/load cycle test failed: {e}")
        return False
    
    # Test 4: Test UI population from loaded data
    print("\n4. Testing UI Population from Loaded Data...")
    
    try:
        # Create new editor instance
        editor2 = AbilityEditorWindow()
        
        # Load data into UI
        editor2.set_widget_values_from_data(loaded_data)
        
        # Verify tags are checked in UI
        for tag in test_tags:
            if tag in editor2.tag_groups:
                if editor2.tag_groups[tag].isChecked():
                    print(f"✓ Tag {tag} properly restored in UI")
                else:
                    print(f"❌ Tag {tag} not checked after loading")
                    return False
            else:
                print(f"❌ Tag {tag} not found in new editor")
                return False
        
        # Verify unchecked tags remain unchecked
        unchecked_tags = ['capture', 'revival', 'losRequired']
        for tag in unchecked_tags:
            if tag in editor2.tag_groups:
                if not editor2.tag_groups[tag].isChecked():
                    print(f"✓ Tag {tag} correctly unchecked")
                else:
                    print(f"❌ Tag {tag} incorrectly checked")
                    return False
                    
    except Exception as e:
        print(f"❌ UI population test failed: {e}")
        return False
    
    # Test 5: Test JSON structure
    print("\n5. Testing JSON Structure...")
    
    try:
        # Check the actual JSON file
        json_file = os.path.join("data", "abilities", "TestTagsAbility.json")
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            print(f"✓ JSON file exists: {json_file}")
            
            # Verify JSON structure
            if 'tags' in json_data:
                json_tags = json_data['tags']
                print(f"✓ JSON contains tags: {json_tags}")
                
                # Verify tags are stored as list
                if isinstance(json_tags, list):
                    print(f"✓ Tags stored as list in JSON")
                else:
                    print(f"❌ Tags not stored as list: {type(json_tags)}")
                    return False
                
                # Verify tag content
                for tag in test_tags:
                    if tag in json_tags:
                        print(f"✓ Tag {tag} in JSON")
                    else:
                        print(f"❌ Tag {tag} missing from JSON")
                        return False
            else:
                print("❌ No tags field in JSON")
                return False
        else:
            print(f"❌ JSON file not found: {json_file}")
            return False
            
    except Exception as e:
        print(f"❌ JSON structure test failed: {e}")
        return False
    
    # Test 6: Clean up
    print("\n6. Cleaning Up...")
    
    try:
        from utils.direct_data_manager import DirectDataManager
        success, error = DirectDataManager.delete_ability("TestTagsAbility")
        if success:
            print("✓ Test ability cleaned up")
        else:
            print(f"⚠️ Cleanup warning: {error}")
            
    except Exception as e:
        print(f"⚠️ Cleanup failed: {e}")
    
    print("\n✅ ALL ABILITY TAGS DATA FLOW TESTS PASSED!")
    print("\nData Flow Summary:")
    print(f"  ✓ {total_count} ability tags verified in config")
    print("  ✓ UI tag_groups properly created and populated")
    print("  ✓ EditorDataInterface collects selected tags")
    print("  ✓ SimpleBridge handles tag data correctly")
    print("  ✓ JSON save/load preserves all tag selections")
    print("  ✓ UI population restores tag checkboxes correctly")
    print("  ✓ Complete data integrity maintained")
    
    print(f"\n🎯 VERIFIED: {total_count} ability tags with complete data flow!")
    
    return True

if __name__ == "__main__":
    success = test_ability_tags_data_flow()
    sys.exit(0 if success else 1)
