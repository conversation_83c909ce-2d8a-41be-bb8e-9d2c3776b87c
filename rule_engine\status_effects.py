#!/usr/bin/env python3
"""
Status Effect System for Adventure Chess Rule Engine
Manages temporary and permanent effects on pieces and game state
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
import logging
import uuid
from datetime import datetime

from schemas.base import Coordinate
from core.tester_engine import PieceColor


class StatusEffectType(Enum):
    """Types of status effects"""
    BUFF = "buff"
    DEBUFF = "debuff"
    IMMUNITY = "immunity"
    PROTECTION = "protection"
    MOVEMENT = "movement"
    VISIBILITY = "visibility"
    SPECIAL = "special"


class StatusEffectDuration(Enum):
    """Duration types for status effects"""
    PERMANENT = -1
    UNTIL_END_OF_TURN = 0
    TURNS = 1  # Specific number of turns
    CONDITIONAL = 2  # Until condition is met


@dataclass
class StatusEffect:
    """
    Represents a status effect applied to a piece or game element
    """
    effect_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    effect_type: StatusEffectType = StatusEffectType.SPECIAL
    duration_type: StatusEffectDuration = StatusEffectDuration.TURNS
    duration_remaining: int = 1
    
    # Source information
    source_piece_id: Optional[str] = None
    source_ability: Optional[str] = None
    applied_turn: int = 0
    
    # Effect parameters
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    # Conditions
    condition: Optional[str] = None
    stackable: bool = False
    removable: bool = True
    
    # Metadata
    created_at: datetime = field(default_factory=datetime.now)
    description: str = ""
    
    def is_expired(self, current_turn: int) -> bool:
        """Check if the effect has expired"""
        if self.duration_type == StatusEffectDuration.PERMANENT:
            return False
        elif self.duration_type == StatusEffectDuration.UNTIL_END_OF_TURN:
            return current_turn > self.applied_turn
        elif self.duration_type == StatusEffectDuration.TURNS:
            return self.duration_remaining <= 0
        elif self.duration_type == StatusEffectDuration.CONDITIONAL:
            # Conditional expiry handled elsewhere
            return False
        return False
    
    def tick_duration(self):
        """Reduce duration by one turn"""
        if self.duration_type == StatusEffectDuration.TURNS and self.duration_remaining > 0:
            self.duration_remaining -= 1


class StatusEffectManager:
    """
    Manages status effects for pieces and game state
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Effect storage
        self.piece_effects: Dict[str, List[StatusEffect]] = {}
        self.global_effects: List[StatusEffect] = []
        
        # Effect processors
        self.effect_processors: Dict[str, callable] = {}
        
        # Initialize built-in processors
        self._initialize_processors()
        
        self.logger.info("StatusEffectManager initialized")
    
    def apply_effect(self, target_piece_id: str, effect: StatusEffect) -> bool:
        """
        Apply a status effect to a piece
        
        Args:
            target_piece_id: ID of the target piece
            effect: StatusEffect to apply
            
        Returns:
            True if effect was applied successfully
        """
        # Check if effect can be applied
        if not self._can_apply_effect(target_piece_id, effect):
            return False
        
        # Initialize piece effects if needed
        if target_piece_id not in self.piece_effects:
            self.piece_effects[target_piece_id] = []
        
        # Handle stacking
        if not effect.stackable:
            # Remove existing effects of the same name
            self.piece_effects[target_piece_id] = [
                e for e in self.piece_effects[target_piece_id] 
                if e.name != effect.name
            ]
        
        # Apply the effect
        self.piece_effects[target_piece_id].append(effect)
        
        # Process immediate effects
        self._process_effect_application(target_piece_id, effect)
        
        self.logger.debug(f"Applied effect '{effect.name}' to piece {target_piece_id}")
        return True
    
    def remove_effect(self, target_piece_id: str, effect_id: str) -> bool:
        """
        Remove a specific status effect from a piece
        
        Args:
            target_piece_id: ID of the target piece
            effect_id: ID of the effect to remove
            
        Returns:
            True if effect was removed
        """
        if target_piece_id not in self.piece_effects:
            return False
        
        original_count = len(self.piece_effects[target_piece_id])
        self.piece_effects[target_piece_id] = [
            e for e in self.piece_effects[target_piece_id] 
            if e.effect_id != effect_id
        ]
        
        removed = len(self.piece_effects[target_piece_id]) < original_count
        
        if removed:
            self.logger.debug(f"Removed effect {effect_id} from piece {target_piece_id}")
        
        return removed
    
    def get_piece_effects(self, piece_id: str) -> List[StatusEffect]:
        """Get all status effects for a piece"""
        return self.piece_effects.get(piece_id, [])
    
    def has_effect(self, piece_id: str, effect_name: str) -> bool:
        """Check if a piece has a specific effect"""
        effects = self.get_piece_effects(piece_id)
        return any(effect.name == effect_name for effect in effects)
    
    def get_effect_value(self, piece_id: str, effect_name: str, parameter: str, default: Any = None) -> Any:
        """Get a parameter value from a specific effect"""
        effects = self.get_piece_effects(piece_id)
        for effect in effects:
            if effect.name == effect_name:
                return effect.parameters.get(parameter, default)
        return default
    
    def tick_effects(self, current_turn: int):
        """Process all effects for a turn"""
        # Tick piece effects
        for piece_id, effects in self.piece_effects.items():
            # Tick duration and remove expired effects
            active_effects = []
            for effect in effects:
                effect.tick_duration()
                if not effect.is_expired(current_turn):
                    active_effects.append(effect)
                else:
                    self._process_effect_removal(piece_id, effect)
            
            self.piece_effects[piece_id] = active_effects
        
        # Tick global effects
        active_global = []
        for effect in self.global_effects:
            effect.tick_duration()
            if not effect.is_expired(current_turn):
                active_global.append(effect)
        
        self.global_effects = active_global
        
        self.logger.debug(f"Ticked effects for turn {current_turn}")
    
    def _can_apply_effect(self, target_piece_id: str, effect: StatusEffect) -> bool:
        """Check if an effect can be applied to a piece"""
        # Check for immunities
        if self.has_effect(target_piece_id, f"{effect.name}_immune"):
            return False
        
        # Check for protection effects
        if effect.effect_type == StatusEffectType.DEBUFF:
            if self.has_effect(target_piece_id, "protected"):
                return False
        
        return True
    
    def _process_effect_application(self, piece_id: str, effect: StatusEffect):
        """Process immediate effects when an effect is applied"""
        processor_name = f"apply_{effect.name}"
        if processor_name in self.effect_processors:
            self.effect_processors[processor_name](piece_id, effect)
    
    def _process_effect_removal(self, piece_id: str, effect: StatusEffect):
        """Process effects when an effect is removed"""
        processor_name = f"remove_{effect.name}"
        if processor_name in self.effect_processors:
            self.effect_processors[processor_name](piece_id, effect)
    
    def _initialize_processors(self):
        """Initialize built-in effect processors"""
        self.effect_processors.update({
            "apply_immobilized": self._apply_immobilized,
            "remove_immobilized": self._remove_immobilized,
            "apply_invisible": self._apply_invisible,
            "remove_invisible": self._remove_invisible,
            "apply_protected": self._apply_protected,
            "remove_protected": self._remove_protected,
        })
    
    def _apply_immobilized(self, piece_id: str, effect: StatusEffect):
        """Apply immobilization effect"""
        self.logger.debug(f"Piece {piece_id} is now immobilized")
    
    def _remove_immobilized(self, piece_id: str, effect: StatusEffect):
        """Remove immobilization effect"""
        self.logger.debug(f"Piece {piece_id} is no longer immobilized")
    
    def _apply_invisible(self, piece_id: str, effect: StatusEffect):
        """Apply invisibility effect"""
        self.logger.debug(f"Piece {piece_id} is now invisible")
    
    def _remove_invisible(self, piece_id: str, effect: StatusEffect):
        """Remove invisibility effect"""
        self.logger.debug(f"Piece {piece_id} is no longer invisible")
    
    def _apply_protected(self, piece_id: str, effect: StatusEffect):
        """Apply protection effect"""
        self.logger.debug(f"Piece {piece_id} is now protected")
    
    def _remove_protected(self, piece_id: str, effect: StatusEffect):
        """Remove protection effect"""
        self.logger.debug(f"Piece {piece_id} is no longer protected")


# Utility functions for creating common status effects

def create_immobilize_effect(duration: int = 1, source_piece_id: str = None, 
                           source_ability: str = None) -> StatusEffect:
    """Create an immobilization effect"""
    return StatusEffect(
        name="immobilized",
        effect_type=StatusEffectType.MOVEMENT,
        duration_type=StatusEffectDuration.TURNS,
        duration_remaining=duration,
        source_piece_id=source_piece_id,
        source_ability=source_ability,
        description=f"Cannot move for {duration} turn(s)"
    )


def create_invisibility_effect(duration: int = 1, break_on_action: bool = True) -> StatusEffect:
    """Create an invisibility effect"""
    return StatusEffect(
        name="invisible",
        effect_type=StatusEffectType.VISIBILITY,
        duration_type=StatusEffectDuration.TURNS,
        duration_remaining=duration,
        parameters={
            "break_on_action": break_on_action,
            "break_on_damage": True
        },
        description=f"Invisible for {duration} turn(s)"
    )


def create_protection_effect(duration: int = 1, protection_type: str = "all") -> StatusEffect:
    """Create a protection effect"""
    return StatusEffect(
        name="protected",
        effect_type=StatusEffectType.PROTECTION,
        duration_type=StatusEffectDuration.TURNS,
        duration_remaining=duration,
        parameters={
            "protection_type": protection_type
        },
        description=f"Protected from {protection_type} for {duration} turn(s)"
    )


def create_buff_effect(stat_name: str, bonus_value: int, duration: int = 1) -> StatusEffect:
    """Create a stat buff effect"""
    return StatusEffect(
        name=f"{stat_name}_buff",
        effect_type=StatusEffectType.BUFF,
        duration_type=StatusEffectDuration.TURNS,
        duration_remaining=duration,
        parameters={
            "stat": stat_name,
            "bonus": bonus_value
        },
        stackable=True,
        description=f"+{bonus_value} {stat_name} for {duration} turn(s)"
    )
