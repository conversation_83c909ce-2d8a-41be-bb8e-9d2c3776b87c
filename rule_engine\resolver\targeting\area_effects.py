#!/usr/bin/env python3
"""
Area Effect Tag Resolvers for Adventure Chess Rule Engine
Handles area-of-effect and line-of-sight targeting
"""

from typing import List, Dict, Any
from schemas import Ability
from schemas.base import Coordinate
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class AreaOfEffectTagResolver(BaseTagResolver):
    """Resolver for area-of-effect targeting"""
    
    def _get_tag_name(self) -> str:
        return "areaOfEffect"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.TARGETING
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        aoe_data = extract_tag_data(ability, "areaOfEffect")
        
        return [TagEffect(
            effect_type="area_effect",
            priority=180,
            immediate=True,
            parameters={
                "effect_type": "area_of_effect",
                "shape": aoe_data.get("shape", "circle"),
                "size": aoe_data.get("size", 1),
                "include_center": aoe_data.get("include_center", True),
                "exclude_caster": aoe_data.get("exclude_caster", True),
                "friendly_fire": aoe_data.get("friendly_fire", False)
            }
        )]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        aoe_data = extract_tag_data(ability, "areaOfEffect")
        
        return {
            "requires_target": True,
            "target_type": "position",
            "area_effect": True,
            "area_shape": aoe_data.get("shape", "circle"),
            "area_size": aoe_data.get("size", 1),
            "min_targets": 1,
            "max_targets": aoe_data.get("max_affected", 20)
        }


class LineOfSightTagResolver(BaseTagResolver):
    """Resolver for line-of-sight requirements"""
    
    def _get_tag_name(self) -> str:
        return "lineOfSight"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.TARGETING
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        los_data = extract_tag_data(ability, "lineOfSight")
        
        return [TagEffect(
            effect_type="targeting_constraint",
            priority=185,
            immediate=True,
            parameters={
                "constraint_type": "line_of_sight",
                "required": los_data.get("required", True),
                "ignore_pieces": los_data.get("ignore_pieces", False),
                "ignore_obstacles": los_data.get("ignore_obstacles", False),
                "see_invisible": los_data.get("see_invisible", False)
            }
        )]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        los_data = extract_tag_data(ability, "lineOfSight")
        
        return {
            "line_of_sight": los_data.get("required", True),
            "ignore_pieces": los_data.get("ignore_pieces", False),
            "ignore_obstacles": los_data.get("ignore_obstacles", False),
            "see_invisible": los_data.get("see_invisible", False)
        }
