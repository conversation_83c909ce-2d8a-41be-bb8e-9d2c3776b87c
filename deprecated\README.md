# Deprecated Code Archive

This folder contains legacy code that has been replaced by the SimpleBridge system.

## ⚠️ DO NOT USE THESE FILES

The files in this folder are **deprecated** and should not be imported or used in the application. They are kept here for reference purposes only.

## 📁 Contents

### `pydantic_bridge.py`
- **Status**: DEPRECATED ❌
- **Replaced by**: `utils/simple_bridge.py` ✅
- **Description**: Legacy bridge layer between Pydantic models and UI components
- **Removal Date**: 2025-06-23
- **Reason**: Replaced by SimpleBridge for unified data flow

### `migrate_data.py`
- **Status**: DEPRECATED ❌
- **Replaced by**: N/A (migration completed)
- **Description**: Data migration utility for transitioning to Pydantic models
- **Removal Date**: 2025-06-23
- **Reason**: Migration process completed, no longer needed

## 🔄 Migration Summary

All components have been successfully migrated to use SimpleBridge:

### ✅ COMPLETED MIGRATIONS
- **Piece Ability Manager** → SimpleBridge
- **Adjacency Editor Dialog** → SimpleBridge
- **Ability Editor** → SimpleBridge (legacy references removed)
- **Piece Editor** → SimpleBridge (legacy references removed)
- **UI Inline Selection Widgets** → SimpleBridge

### 🗑️ REMOVED COMPONENTS
- **Piece Tester** → Removed (application now focuses on editors only)
- **Tester Engine** → Removed (application now focuses on editors only)
- **Rule Engine** → Removed (application now focuses on editors only)

### 🎯 SINGLE SOURCE OF TRUTH
SimpleBridge (`utils/simple_bridge.py`) is now the **only** bridge used throughout the application for:
- Loading pieces from `data/pieces/`
- Loading abilities from `data/abilities/`
- Saving piece data
- Saving ability data
- UI data collection and population

## 🗑️ Safe to Delete

These files can be safely deleted after confirming no external tools or scripts reference them. They are kept temporarily for reference during the transition period.

## 📚 Current Architecture

```
UI Components → SimpleBridge → DirectDataManager → JSON Files
                     ↓
              EditorDataInterface
                     ↓
              Unified Field Mappings
```

**SimpleBridge provides**:
- Consistent data flow
- Unified error handling
- Standardized save/load operations
- Complete field coverage
- Verified data integrity

---

*Last Updated: 2025-06-23*
*Migration completed by: Augment Agent*
