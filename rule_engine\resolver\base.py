#!/usr/bin/env python3
"""
Base Tag Resolver System for Adventure Chess Rule Engine
Provides foundation for modular tag processing
"""

from typing import Dict, List, Optional, Any, Type, Tuple
from abc import ABC, abstractmethod
from dataclasses import dataclass
from enum import Enum
import logging

from schemas import Ability


class TagCategory(Enum):
    """Categories of ability tags"""
    ACTION = "action"           # Direct game actions (move, capture, summon, etc.)
    TARGETING = "targeting"     # Targeting modifiers (range, areaEffect)
    CONDITION = "condition"     # Prerequisites and conditions (adjacency, los, etc.)
    SPECIAL = "special"         # Special mechanics (reaction, pulse, fog, etc.)


@dataclass
class TagEffect:
    """Represents an effect generated by a tag processor"""
    effect_type: str
    priority: int = 0  # Higher priority effects execute first
    immediate: bool = True  # False for delayed effects
    parameters: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


class BaseTagResolver(ABC):
    """
    Base class for all tag processors
    Each tag gets its own resolver for modular processing
    """
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self.tag_name = self._get_tag_name()
        self.category = self._get_category()
        self.conflicts_with = self._get_conflicts()
        self.requires_tags = self._get_requirements()
        
    @abstractmethod
    def _get_tag_name(self) -> str:
        """Return the name of the tag this resolver handles"""
        pass
    
    @abstractmethod
    def _get_category(self) -> TagCategory:
        """Return the category of this tag"""
        pass
    
    def _get_conflicts(self) -> List[str]:
        """Return list of conflicting tags (override in subclasses)"""
        return []
    
    def _get_requirements(self) -> List[str]:
        """Return list of required tags (override in subclasses)"""
        return []
    
    @abstractmethod
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        """
        Process the tag and return list of effects
        
        Args:
            ability: The ability containing this tag
            context: ActionContext with game state and action details
            
        Returns:
            List of TagEffect objects to be applied
        """
        pass
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> Tuple[bool, str]:
        """
        Validate that prerequisites for this tag are met
        
        Returns:
            (is_valid, error_message)
        """
        # Check required tags
        for required_tag in self.requires_tags:
            if required_tag not in ability.tags:
                return False, f"Tag '{self.tag_name}' requires '{required_tag}' tag"
        
        # Check for conflicting tags
        for conflict_tag in self.conflicts_with:
            if conflict_tag in ability.tags:
                return False, f"Tag '{self.tag_name}' conflicts with '{conflict_tag}'"
        
        return True, "Prerequisites satisfied"
    
    def calculate_cost_modifier(self, base_cost: int, ability: Ability, context: Any) -> int:
        """
        Calculate cost modifications for this tag (override in subclasses)
        
        Returns:
            Modified cost (can be different from base_cost)
        """
        return base_cost
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        """
        Get targeting requirements for this tag (override in subclasses)
        
        Returns:
            Dictionary with targeting requirements
        """
        return {}
    
    def get_range_modifier(self, base_range: int, ability: Ability, context: Any) -> int:
        """
        Get range modifications for this tag (override in subclasses)
        
        Returns:
            Modified range
        """
        return base_range


class TagRegistry:
    """
    Registry for all tag processors
    Manages loading and accessing tag resolvers
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.processors: Dict[str, BaseTagResolver] = {}
        self.categories: Dict[TagCategory, List[str]] = {
            TagCategory.ACTION: [],
            TagCategory.TARGETING: [],
            TagCategory.CONDITION: [],
            TagCategory.SPECIAL: []
        }
        
    def register_processor(self, processor: BaseTagResolver):
        """Register a tag processor"""
        tag_name = processor.tag_name
        self.processors[tag_name] = processor
        self.categories[processor.category].append(tag_name)
        self.logger.debug(f"Registered processor for tag: {tag_name}")
    
    def get_processor(self, tag_name: str) -> Optional[BaseTagResolver]:
        """Get processor for a specific tag"""
        return self.processors.get(tag_name)
    
    def get_processors_by_category(self, category: TagCategory) -> List[BaseTagResolver]:
        """Get all processors in a category"""
        tag_names = self.categories.get(category, [])
        return [self.processors[name] for name in tag_names if name in self.processors]
    
    def initialize_processors(self):
        """Initialize all available tag processors"""
        # Register actual tag resolvers
        self._register_action_resolvers()
        self._register_targeting_resolvers()
        self._register_condition_resolvers()
        self._register_special_resolvers()

        # Register placeholders for any remaining tags
        self._register_remaining_placeholders()

        self.logger.info(f"Initialized {len(self.processors)} tag processors")

    def _register_action_resolvers(self):
        """Register action tag resolvers"""
        # Import each resolver individually to handle import errors gracefully
        resolver_imports = [
            ("move", ".action.move", "MoveTagResolver"),
            ("capture", ".action.capture", "CaptureTagResolver"),
            ("summon", ".action.summon", "SummonTagResolver"),
            ("revival", ".action.revival", "RevivalTagResolver"),
            ("carryPiece", ".action.carry_piece", "CarryPieceTagResolver"),
            ("swapPlaces", ".action.swap_places", "SwapPlacesTagResolver"),
            ("displacePiece", ".action.displace_piece", "DisplacePieceTagResolver"),
            ("immobilize", ".action.immobilize", "ImmobilizeTagResolver"),
            ("convertPiece", ".action.convert_piece", "ConvertPieceTagResolver"),
            ("duplicate", ".action.duplicate", "DuplicateTagResolver"),
            ("buffPiece", ".action.buff_piece", "BuffPieceTagResolver"),
            ("debuffPiece", ".action.debuff_piece", "DebuffPieceTagResolver"),
            ("addObstacle", ".action.add_obstacle", "AddObstacleTagResolver"),
            ("removeObstacle", ".action.remove_obstacle", "RemoveObstacleTagResolver"),
            ("trapTile", ".action.trap_tile", "TrapTileTagResolver")
        ]

        action_resolvers = []
        failed_imports = []

        for tag_name, module_path, class_name in resolver_imports:
            try:
                module = __import__(module_path, fromlist=[class_name], level=1)
                resolver_class = getattr(module, class_name)
                action_resolvers.append(resolver_class())
                self.logger.debug(f"Successfully imported {class_name}")
            except (ImportError, AttributeError) as e:
                self.logger.debug(f"Could not import {class_name} from {module_path}: {e}")
                failed_imports.append(tag_name)

        # Register successfully imported resolvers
        for resolver in action_resolvers:
            self.register_processor(resolver)

        # Register placeholders for failed imports
        if failed_imports:
            self._register_placeholder_processors(failed_imports, TagCategory.ACTION)

    def _register_targeting_resolvers(self):
        """Register targeting tag resolvers"""
        targeting_resolvers = []

        # Try to import each resolver individually
        resolver_imports = [
            ("adjacency", ".targeting.adjacency", "AdjacencyTagResolver"),
            ("orthogonal", ".targeting.movement_patterns", "OrthogonalTagResolver"),
            ("diagonal", ".targeting.movement_patterns", "DiagonalTagResolver"),
            ("any", ".targeting.movement_patterns", "AnyDirectionTagResolver"),
            ("lShape", ".targeting.movement_patterns", "LShapeTagResolver"),
            ("teleport", ".targeting.movement_patterns", "TeleportTagResolver"),
            ("areaOfEffect", ".targeting.area_effects", "AreaOfEffectTagResolver"),
            ("lineOfSight", ".targeting.area_effects", "LineOfSightTagResolver"),
            ("range", ".targeting.range_modifiers", "RangeTagResolver"),
            ("customPattern", ".targeting.range_modifiers", "CustomPatternTagResolver")
        ]

        failed_imports = []

        for tag_name, module_path, class_name in resolver_imports:
            try:
                module = __import__(module_path, fromlist=[class_name], level=1)
                resolver_class = getattr(module, class_name)
                targeting_resolvers.append(resolver_class())
                self.logger.debug(f"Successfully imported {class_name}")
            except (ImportError, AttributeError) as e:
                self.logger.debug(f"Could not import {class_name} from {module_path}: {e}")
                failed_imports.append(tag_name)

        # Register successfully imported resolvers
        for resolver in targeting_resolvers:
            self.register_processor(resolver)

        # Register placeholders for failed imports
        if failed_imports:
            self._register_placeholder_processors(failed_imports, TagCategory.TARGETING)

    def _register_condition_resolvers(self):
        """Register condition tag resolvers"""
        condition_resolvers = []

        # Try to import each resolver individually
        resolver_imports = [
            ("noTurnCost", ".condition.cost_modifiers", "NoTurnCostTagResolver"),
            ("costPerDistance", ".condition.cost_modifiers", "CostPerDistanceTagResolver"),
            ("costPerTarget", ".condition.cost_modifiers", "CostPerTargetTagResolver"),
            ("variableCost", ".condition.cost_modifiers", "VariableCostTagResolver"),
            ("requiresStartingPosition", ".condition.prerequisites", "RequiresStartingPositionTagResolver"),
            ("oncePerTurn", ".condition.prerequisites", "OncePerTurnTagResolver"),
            ("requiresAlly", ".condition.prerequisites", "RequiresAllyTagResolver"),
            ("requiresEnemy", ".condition.prerequisites", "RequiresEnemyTagResolver"),
            ("protect", ".condition.status_effects", "ProtectTagResolver"),
            ("cannotDie", ".condition.status_effects", "CannotDieTagResolver"),
            ("invisible", ".condition.status_effects", "InvisibleTagResolver"),
            ("truesight", ".condition.status_effects", "TruesightTagResolver"),
            ("immobilizeImmune", ".condition.status_effects", "ImmobilizeImmuneTagResolver")
        ]

        failed_imports = []

        for tag_name, module_path, class_name in resolver_imports:
            try:
                module = __import__(module_path, fromlist=[class_name], level=1)
                resolver_class = getattr(module, class_name)
                condition_resolvers.append(resolver_class())
                self.logger.debug(f"Successfully imported {class_name}")
            except (ImportError, AttributeError) as e:
                self.logger.debug(f"Could not import {class_name} from {module_path}: {e}")
                failed_imports.append(tag_name)

        # Register successfully imported resolvers
        for resolver in condition_resolvers:
            self.register_processor(resolver)

        # Register placeholders for failed imports
        if failed_imports:
            self._register_placeholder_processors(failed_imports, TagCategory.CONDITION)

    def _register_special_resolvers(self):
        """Register special tag resolvers"""
        special_resolvers = []

        # Try to import each resolver individually
        resolver_imports = [
            ("shareSpace", ".special.share_space", "ShareSpaceTagResolver")
        ]

        failed_imports = []

        for tag_name, module_path, class_name in resolver_imports:
            try:
                module = __import__(module_path, fromlist=[class_name], level=1)
                resolver_class = getattr(module, class_name)
                special_resolvers.append(resolver_class())
                self.logger.debug(f"Successfully imported {class_name}")
            except (ImportError, AttributeError) as e:
                self.logger.debug(f"Could not import {class_name} from {module_path}: {e}")
                failed_imports.append(tag_name)

        # Register successfully imported resolvers
        for resolver in special_resolvers:
            self.register_processor(resolver)

        # Register placeholders for failed imports
        if failed_imports:
            self._register_placeholder_processors(failed_imports, TagCategory.SPECIAL)

    def _register_remaining_placeholders(self):
        """Register placeholders for any remaining unimplemented tags"""
        # Special tags that don't have resolvers yet
        self._register_placeholder_processors([
            "reaction", "pulseEffect", "fogOfWar", "delay", "passThrough"
        ], TagCategory.SPECIAL)
    
    def _register_placeholder_processors(self, tag_names: List[str], category: TagCategory):
        """Register placeholder processors for development"""
        for tag_name in tag_names:
            processor = PlaceholderTagResolver(tag_name, category)
            self.register_processor(processor)
    
    def validate_tag_combination(self, tags: List[str]) -> Tuple[bool, List[str]]:
        """
        Validate a combination of tags for conflicts
        
        Returns:
            (is_valid, list_of_errors)
        """
        errors = []
        
        # Check each tag against others
        for i, tag1 in enumerate(tags):
            processor1 = self.get_processor(tag1)
            if not processor1:
                errors.append(f"Unknown tag: {tag1}")
                continue
                
            for tag2 in tags[i+1:]:
                processor2 = self.get_processor(tag2)
                if not processor2:
                    continue
                    
                # Check for conflicts
                if tag2 in processor1.conflicts_with:
                    errors.append(f"Tag '{tag1}' conflicts with '{tag2}'")
        
        return len(errors) == 0, errors
    
    def get_processing_order(self, tags: List[str]) -> List[str]:
        """
        Get optimal processing order for tags based on dependencies
        
        Returns:
            List of tags in processing order
        """
        # Simple implementation - will be enhanced
        # Process by category order: targeting -> condition -> action -> special
        
        ordered_tags = []
        
        for category in [TagCategory.TARGETING, TagCategory.CONDITION, TagCategory.ACTION, TagCategory.SPECIAL]:
            category_tags = [tag for tag in tags if tag in self.categories[category]]
            ordered_tags.extend(category_tags)
        
        # Add any unrecognized tags at the end
        unrecognized = [tag for tag in tags if tag not in ordered_tags]
        ordered_tags.extend(unrecognized)
        
        return ordered_tags


class PlaceholderTagResolver(BaseTagResolver):
    """
    Placeholder resolver for tags not yet implemented
    Allows the system to function while tag processors are being developed
    """
    
    def __init__(self, tag_name: str, category: TagCategory):
        self._tag_name = tag_name
        self._category = category
        super().__init__()
    
    def _get_tag_name(self) -> str:
        return self._tag_name
    
    def _get_category(self) -> TagCategory:
        return self._category
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        """Return placeholder effect"""
        self.logger.debug(f"Processing placeholder for tag: {self.tag_name}")
        
        return [TagEffect(
            effect_type=f"placeholder_{self.tag_name}",
            priority=0,
            parameters={"tag": self.tag_name, "placeholder": True}
        )]


# Utility functions for tag processing

def extract_tag_data(ability: Ability, tag_name: str) -> Dict[str, Any]:
    """
    Extract tag-specific data from ability extra_fields
    
    Args:
        ability: The ability object
        tag_name: Name of the tag to extract data for
        
    Returns:
        Dictionary with tag-specific configuration
    """
    tag_data = {}
    
    # Extract from extra_fields based on tag name
    if hasattr(ability, 'extra_fields') and ability.extra_fields:
        # Look for tag-specific fields
        for field_name, value in ability.extra_fields.items():
            if field_name.startswith(tag_name.lower()):
                # Remove tag prefix and convert to snake_case
                clean_name = field_name[len(tag_name):].lstrip('_').lower()
                tag_data[clean_name] = value
    
    # Extract from direct ability fields
    tag_fields = _get_tag_field_mapping().get(tag_name, [])
    for field_name in tag_fields:
        if hasattr(ability, field_name):
            value = getattr(ability, field_name)
            if value is not None:
                tag_data[field_name] = value
    
    return tag_data


def _get_tag_field_mapping() -> Dict[str, List[str]]:
    """
    Get mapping of tags to their associated ability fields
    Based on the field reference documentation and updated schema aliases
    """
    return {
        # Targeting tags
        "range": ["rangeMask", "piecePosition", "rangeFriendlyOnly", "rangeEnemyOnly", "rangeIncludeStart", "rangeIncludeSelf"],
        "areaEffect": ["areaSize", "areaShape"],

        # Action tags
        "move": ["move_distance", "move_direction"],
        "summon": ["summonMax", "summonList"],
        "revival": ["revivalSacrifice", "revivalMaxCost", "revivalWithPoints", "revivalPoints"],
        "capture": ["captureTarget"],
        "carryPiece": ["carryRange", "carryDropOnDeath", "carryShareAbilities", "carryList", "carryStartingPiece"],
        "buffPiece": ["buffDuration", "buffTargetList", "buffAbilities"],
        "debuffPiece": ["debuffDuration", "debuffTargetList", "debuffPreventAbilities"],
        "immobilize": ["immobilizeDuration", "immobilizeDurationEnabled", "immobilizeTargetList"],
        "displacePiece": ["displaceDirection", "displaceDistance", "displaceTargetList", "displaceTargetType", "displaceCustom"],
        "convertPiece": ["convertTargetList", "convertTargetType"],
        "duplicate": ["duplicateOffset", "duplicateLimitAmount", "duplicateLimit"],
        "addObstacle": ["obstacleType"],
        "removeObstacle": ["removeObstacleType"],
        "trapTile": ["trapCapture", "trapImmobilize", "trapImmobilizeAmount", "trapTeleport", "trapTeleportPattern", "trapAddAbility", "trapAbility"],

        # Condition tags
        "adjacencyRequired": ["adjacencyList", "adjacencyDistance"],
        "losRequired": ["losIgnoreFriendly", "losIgnoreEnemy", "losIgnoreAll", "preventLos"],
        "noTurnCost": ["noTurnCostLimit"],

        # Special tags
        "shareSpace": ["shareSpaceMax", "shareSpaceSameType", "shareSpaceFriendly", "shareSpaceEnemy", "shareSpaceAny"],
        "delay": ["delayTurn", "delayTurnAmount", "delayAction", "delayActionAmount"],
        "pulseEffect": ["pulseInterval", "pulseEnabled"],
        "invisible": ["invisibleRevealMove", "invisibleRevealMoveAmount", "invisibleRevealCapture", "invisibleRevealCaptureAmount", "invisibleRevealAction", "invisibleRevealActionAmount", "invisibleRevealLos"],
        "fogOfWar": ["fogVisionType", "fogRadius", "fogDuration", "fogCost", "fogCustomRange"],
        "passThrough": ["passThroughList", "passThroughCapture"],
        "reaction": ["reactionTargets", "reactionEvent", "reactionUsesAction"]
    }
