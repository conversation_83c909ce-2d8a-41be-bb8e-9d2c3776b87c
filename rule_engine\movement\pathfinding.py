#!/usr/bin/env python3
"""
Pathfinding and Line-of-Sight for Adventure Chess Rule Engine
Advanced collision detection and path validation
"""

from typing import Dict, List, Optional, Tuple, Set, Any
from dataclasses import dataclass
from enum import Enum
import logging
import math

# Import from schemas for data structures
from schemas.base import Coordinate
from core.tester_engine import PieceColor


class ObstacleType(Enum):
    """Types of obstacles that can block movement"""
    PIECE = "piece"           # Another piece
    OBSTACLE = "obstacle"     # Board obstacle
    SPECIAL_ZONE = "special"  # Special board zone
    TRAP = "trap"            # Trap tile


@dataclass
class PathSegment:
    """Represents a segment of a path"""
    start: Coordinate
    end: Coordinate
    distance: float
    blocked: bool = False
    blocking_obstacle: Optional[ObstacleType] = None
    blocking_position: Optional[Coordinate] = None


@dataclass
class LineOfSightResult:
    """Result of line-of-sight calculation"""
    has_line_of_sight: bool
    blocked_by: List[Coordinate]
    path_length: float
    clear_path: List[Coordinate]
    
    def is_clear(self) -> bool:
        """Check if line of sight is completely clear"""
        return self.has_line_of_sight and len(self.blocked_by) == 0


class PathfindingEngine:
    """
    Advanced pathfinding engine for movement validation
    Handles obstacles, piece blocking, and special movement abilities
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # Pathfinding configuration
        self.allow_diagonal_movement = True
        self.piece_blocking_enabled = True
        self.obstacle_blocking_enabled = True
        
    def find_path(self, start: Coordinate, end: Coordinate, game_state: Any,
                 piece_id: str = None, abilities: List[str] = None) -> List[Coordinate]:
        """
        Find a valid path from start to end position
        
        Args:
            start: Starting position
            end: Target position
            game_state: Current game state
            piece_id: ID of piece moving (for ability checks)
            abilities: List of piece abilities that might affect pathfinding
            
        Returns:
            List of coordinates representing the path, empty if no path found
        """
        # Check for special movement abilities
        if abilities:
            if "teleport" in abilities:
                # Teleport ignores obstacles
                return [start, end]
            
            if "passThrough" in abilities:
                # Pass through ignores piece blocking
                return self._find_direct_path(start, end, ignore_pieces=True, game_state=game_state)
        
        # Standard pathfinding
        return self._find_standard_path(start, end, game_state, piece_id)
    
    def _find_standard_path(self, start: Coordinate, end: Coordinate, 
                          game_state: Any, piece_id: str = None) -> List[Coordinate]:
        """Find path using standard movement rules"""
        # For most chess-like movement, we use direct line validation
        # rather than complex A* pathfinding
        
        # Check if direct path is possible
        direct_path = self._find_direct_path(start, end, game_state=game_state)
        if direct_path:
            return direct_path
        
        # If direct path is blocked, check for alternative routes
        # (This would be expanded for more complex pathfinding scenarios)
        return []
    
    def _find_direct_path(self, start: Coordinate, end: Coordinate,
                         ignore_pieces: bool = False, game_state: Any = None) -> List[Coordinate]:
        """Find direct path between two points"""
        path = []
        
        # Calculate direction
        row_diff = end.row - start.row
        col_diff = end.col - start.col
        
        # Determine step direction
        row_step = 0 if row_diff == 0 else (1 if row_diff > 0 else -1)
        col_step = 0 if col_diff == 0 else (1 if col_diff > 0 else -1)
        
        # Check if movement is valid (orthogonal, diagonal, or L-shape)
        if not self._is_valid_movement_direction(row_diff, col_diff):
            return []
        
        # Generate path
        current_row, current_col = start.row, start.col
        path.append(Coordinate(row=current_row, col=current_col))
        
        while current_row != end.row or current_col != end.col:
            current_row += row_step
            current_col += col_step

            # Check bounds before creating coordinate
            if not (0 <= current_row < 8 and 0 <= current_col < 8):
                return []

            pos = Coordinate(row=current_row, col=current_col)
            
            # Check for obstacles (except destination)
            if pos.row != end.row or pos.col != end.col:
                if self._is_position_blocked(pos, game_state, ignore_pieces):
                    return []
            
            path.append(pos)
        
        return path
    
    def _is_valid_movement_direction(self, row_diff: int, col_diff: int) -> bool:
        """Check if movement direction is valid for chess-like movement"""
        # Orthogonal movement
        if row_diff == 0 or col_diff == 0:
            return True
        
        # Diagonal movement
        if abs(row_diff) == abs(col_diff):
            return True
        
        # L-shape movement (knight)
        if (abs(row_diff) == 2 and abs(col_diff) == 1) or (abs(row_diff) == 1 and abs(col_diff) == 2):
            return True
        
        return False
    
    def _is_valid_position(self, pos: Coordinate) -> bool:
        """Check if position is within board bounds"""
        return 0 <= pos.row < 8 and 0 <= pos.col < 8
    
    def _is_position_blocked(self, pos: Coordinate, game_state: Any, ignore_pieces: bool = False) -> bool:
        """Check if position is blocked by obstacles or pieces"""
        if not game_state:
            return False
        
        # Check for pieces
        if not ignore_pieces and self.piece_blocking_enabled:
            if hasattr(game_state, 'get_piece_at'):
                pieces_at_pos = game_state.get_piece_at(pos)
                if pieces_at_pos:
                    return True
        
        # Check for obstacles
        if self.obstacle_blocking_enabled:
            if hasattr(game_state, 'get_cell'):
                cell = game_state.get_cell(pos)
                if hasattr(cell, 'obstacles') and cell.obstacles:
                    return True
        
        return False


class LineOfSightCalculator:
    """
    Calculates line-of-sight for abilities and targeting
    Supports various LoS rules and obstacle types
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # LoS configuration
        self.ignore_friendly_pieces = False
        self.ignore_enemy_pieces = False
        self.ignore_all_pieces = False
        self.prevent_los = False
    
    def has_line_of_sight(self, start: Coordinate, end: Coordinate, game_state: Any,
                         source_piece_id: str = None, los_config: Dict[str, Any] = None) -> LineOfSightResult:
        """
        Check if there's line of sight between two positions
        
        Args:
            start: Starting position
            end: Target position
            game_state: Current game state
            source_piece_id: ID of piece checking LoS
            los_config: LoS configuration from ability
            
        Returns:
            LineOfSightResult with detailed information
        """
        # Apply configuration
        if los_config:
            self.ignore_friendly_pieces = los_config.get('ignore_friendly', False)
            self.ignore_enemy_pieces = los_config.get('ignore_enemy', False)
            self.ignore_all_pieces = los_config.get('ignore_all', False)
            self.prevent_los = los_config.get('prevent_los', False)
        
        # If LoS is prevented, return blocked
        if self.prevent_los:
            return LineOfSightResult(
                has_line_of_sight=False,
                blocked_by=[],
                path_length=0,
                clear_path=[]
            )
        
        # Calculate LoS using Bresenham's line algorithm
        return self._calculate_bresenham_los(start, end, game_state, source_piece_id)
    
    def _calculate_bresenham_los(self, start: Coordinate, end: Coordinate,
                               game_state: Any, source_piece_id: str = None) -> LineOfSightResult:
        """Calculate line of sight using Bresenham's line algorithm"""
        blocked_by = []
        clear_path = []
        
        # Get source piece for owner information
        source_owner = None
        if source_piece_id and game_state and hasattr(game_state, 'get_piece'):
            source_piece = game_state.get_piece(source_piece_id)
            if source_piece:
                source_owner = source_piece.owner
        
        # Bresenham's line algorithm
        x0, y0 = start.col, start.row
        x1, y1 = end.col, end.row
        
        dx = abs(x1 - x0)
        dy = abs(y1 - y0)
        sx = 1 if x0 < x1 else -1
        sy = 1 if y0 < y1 else -1
        err = dx - dy
        
        x, y = x0, y0
        
        while True:
            pos = Coordinate(row=y, col=x)
            clear_path.append(pos)
            
            # Don't check start and end positions for blocking
            if not ((x == x0 and y == y0) or (x == x1 and y == y1)):
                if self._is_los_blocked_at_position(pos, game_state, source_owner):
                    blocked_by.append(pos)
            
            # Check if we've reached the end
            if x == x1 and y == y1:
                break
            
            # Bresenham step
            e2 = 2 * err
            if e2 > -dy:
                err -= dy
                x += sx
            if e2 < dx:
                err += dx
                y += sy
        
        # Calculate path length
        path_length = math.sqrt((end.row - start.row) ** 2 + (end.col - start.col) ** 2)
        
        return LineOfSightResult(
            has_line_of_sight=len(blocked_by) == 0,
            blocked_by=blocked_by,
            path_length=path_length,
            clear_path=clear_path
        )
    
    def _is_los_blocked_at_position(self, pos: Coordinate, game_state: Any, source_owner: PieceColor = None) -> bool:
        """Check if LoS is blocked at a specific position"""
        if not game_state:
            return False
        
        # Check for pieces
        if hasattr(game_state, 'get_piece_at'):
            pieces_at_pos = game_state.get_piece_at(pos)
            for piece in pieces_at_pos:
                # Apply ignore rules
                if self.ignore_all_pieces:
                    continue
                
                if source_owner:
                    if piece.owner == source_owner and self.ignore_friendly_pieces:
                        continue
                    if piece.owner != source_owner and self.ignore_enemy_pieces:
                        continue
                
                # Piece blocks LoS
                return True
        
        # Check for obstacles
        if hasattr(game_state, 'get_cell'):
            cell = game_state.get_cell(pos)
            if hasattr(cell, 'obstacles') and cell.obstacles:
                return True
        
        return False
    
    def calculate_range(self, start: Coordinate, end: Coordinate) -> int:
        """Calculate range between two positions"""
        # Use Chebyshev distance (max of row/col difference) for chess-like movement
        return max(abs(end.row - start.row), abs(end.col - start.col))
    
    def get_positions_in_range(self, center: Coordinate, range_value: int,
                             include_center: bool = False) -> List[Coordinate]:
        """Get all positions within range of center"""
        positions = []
        
        for row in range(max(0, center.row - range_value), min(8, center.row + range_value + 1)):
            for col in range(max(0, center.col - range_value), min(8, center.col + range_value + 1)):
                pos = Coordinate(row=row, col=col)
                
                # Skip center if not included
                if not include_center and pos.row == center.row and pos.col == center.col:
                    continue
                
                # Check if within range
                if self.calculate_range(center, pos) <= range_value:
                    positions.append(pos)
        
        return positions
    
    def get_positions_in_line(self, start: Coordinate, direction: Tuple[int, int],
                            max_distance: int = 8) -> List[Coordinate]:
        """Get all positions in a line from start position"""
        positions = []
        dr, dc = direction
        
        for i in range(1, max_distance + 1):
            new_row = start.row + dr * i
            new_col = start.col + dc * i

            # Check bounds before creating coordinate
            if not (0 <= new_row < 8 and 0 <= new_col < 8):
                break

            pos = Coordinate(row=new_row, col=new_col)
            positions.append(pos)
        
        return positions
