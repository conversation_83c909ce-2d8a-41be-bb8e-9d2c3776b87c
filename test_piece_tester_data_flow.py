#!/usr/bin/env python3
"""
Test Piece Tester Data Flow
Verify that all piece editor fields are properly saved and loaded in the piece tester
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_piece_tester_data_flow():
    """Test that piece tester properly loads all piece editor fields"""
    
    print("=== PIECE TESTER DATA FLOW TEST ===")
    
    # Test 1: Create a test piece with all fields set
    print("\n1. Creating Test Piece with All Fields...")
    
    try:
        from PyQt6.QtWidgets import QApplication
        import sys
        
        # Create minimal QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        from editors.piece_editor import PieceEditorWindow
        from utils.simple_bridge import simple_bridge
        
        # Create piece editor instance
        editor = PieceEditorWindow()
        
        # Set comprehensive test data
        editor.name_edit.setText("TestDataFlowPiece")
        editor.desc_edit.setPlainText("Test piece for data flow verification")
        editor.role_combo.setCurrentText("Supporter")
        editor.can_castle_check.setChecked(True)
        editor.track_starting_position_check.setChecked(True)
        editor.color_directional_check.setChecked(True)
        editor.can_capture_check.setChecked(False)  # Test False value
        
        # Set recharge system
        editor.enable_recharge_check.setChecked(True)
        editor.max_points_spin.setValue(5)
        editor.starting_points_spin.setValue(3)
        editor.recharge_combo.setCurrentText("turnRecharge")
        editor.turn_points_spin.setValue(2)
        
        # Select some abilities if available
        if hasattr(editor, 'ability_checkboxes') and editor.ability_checkboxes:
            ability_names = list(editor.ability_checkboxes.keys())[:2]  # Select first 2
            for ability_name in ability_names:
                editor.ability_checkboxes[ability_name].setChecked(True)
            editor.on_ability_checkbox_changed()  # Update abilities list
        
        # Collect the data
        piece_data = simple_bridge.get_piece_data_from_ui(editor)
        
        # Verify all fields are collected
        expected_fields = {
            'name': 'TestDataFlowPiece',
            'role': 'Supporter',
            'canCastle': True,
            'trackStartingPosition': True,
            'colorDirectional': True,
            'canCapture': False,
            'enableRecharge': True,
            'maxPoints': 5,
            'startingPoints': 3,
            'rechargeType': 'turnRecharge',
            'turnPoints': 2
        }
        
        all_fields_correct = True
        for field, expected_value in expected_fields.items():
            actual_value = piece_data.get(field)
            if actual_value != expected_value:
                print(f"❌ Field {field}: expected {expected_value}, got {actual_value}")
                all_fields_correct = False
        
        if all_fields_correct:
            print("✓ All piece editor fields properly collected")
        else:
            return False
            
    except Exception as e:
        print(f"❌ Test piece creation failed: {e}")
        return False
    
    # Test 2: Save the piece and verify it can be loaded
    print("\n2. Testing Save and Load Cycle...")
    
    try:
        # Save the piece
        success, error = simple_bridge.save_piece_from_ui(editor, "TestDataFlowPiece")
        
        if not success:
            print(f"❌ Save failed: {error}")
            return False
        
        print("✓ Test piece saved successfully")
        
        # Load the piece back
        loaded_data, load_error = simple_bridge.load_piece_for_ui("TestDataFlowPiece")
        
        if load_error:
            print(f"❌ Load failed: {load_error}")
            return False
        
        # Verify critical fields are preserved
        critical_fields = ['name', 'canCapture', 'colorDirectional', 'canCastle', 'role']
        for field in critical_fields:
            if loaded_data.get(field) != piece_data.get(field):
                print(f"❌ Field {field} not preserved: {piece_data.get(field)} → {loaded_data.get(field)}")
                return False
        
        print("✓ All critical fields preserved in save/load cycle")
        
    except Exception as e:
        print(f"❌ Save/load test failed: {e}")
        return False
    
    # Test 3: Test piece tester integration
    print("\n3. Testing Piece Tester Integration...")
    
    try:
        from editors.piece_tester import PieceTester
        
        # Create piece tester instance
        tester = PieceTester()
        
        # Load available pieces
        available_pieces = tester.load_available_pieces()
        
        # Check if our test piece is available
        if "TestDataFlowPiece" not in available_pieces:
            print("❌ Test piece not found in piece tester")
            return False
        
        # Get the piece data from tester
        tester_piece_data = available_pieces["TestDataFlowPiece"]
        
        # Verify key fields match
        key_fields = ['name', 'role', 'canCapture', 'colorDirectional']
        for field in key_fields:
            original_value = piece_data.get(field)
            tester_value = tester_piece_data.get(field)
            if original_value != tester_value:
                print(f"❌ Piece tester field mismatch {field}: {original_value} vs {tester_value}")
                return False
        
        print("✓ Piece tester loads all fields correctly")
        
    except Exception as e:
        print(f"❌ Piece tester integration test failed: {e}")
        return False
    
    # Test 4: Test UI population from loaded data
    print("\n4. Testing UI Population from Loaded Data...")
    
    try:
        # Create a new editor instance
        editor2 = PieceEditorWindow()
        
        # Load the saved data into the new editor
        editor2.set_widget_values_from_data(loaded_data)
        
        # Verify UI state matches original
        ui_checks = [
            (editor2.name_edit.text(), "TestDataFlowPiece", "name"),
            (editor2.role_combo.currentText(), "Supporter", "role"),
            (editor2.can_castle_check.isChecked(), True, "can_castle"),
            (editor2.color_directional_check.isChecked(), True, "color_directional"),
            (editor2.can_capture_check.isChecked(), False, "can_capture"),
            (editor2.max_points_spin.value(), 5, "max_points"),
            (editor2.starting_points_spin.value(), 3, "starting_points")
        ]
        
        all_ui_correct = True
        for actual, expected, field_name in ui_checks:
            if actual != expected:
                print(f"❌ UI field {field_name}: expected {expected}, got {actual}")
                all_ui_correct = False
        
        if all_ui_correct:
            print("✓ UI properly populated from loaded data")
        else:
            return False
            
    except Exception as e:
        print(f"❌ UI population test failed: {e}")
        return False
    
    # Test 5: Clean up test file
    print("\n5. Cleaning Up...")
    
    try:
        from utils.direct_data_manager import DirectDataManager
        success, error = DirectDataManager.delete_piece("TestDataFlowPiece")
        if success:
            print("✓ Test piece cleaned up")
        else:
            print(f"⚠️ Cleanup warning: {error}")
            
    except Exception as e:
        print(f"⚠️ Cleanup failed: {e}")
    
    print("\n✅ ALL PIECE TESTER DATA FLOW TESTS PASSED!")
    print("\nData Flow Summary:")
    print("  ✓ Piece Editor → SimpleBridge → JSON file")
    print("  ✓ JSON file → SimpleBridge → Piece Tester")
    print("  ✓ JSON file → SimpleBridge → UI Population")
    print("  ✓ All fields including new checkbox properly handled")
    print("  ✓ Can Capture checkbox works correctly (False value tested)")
    print("  ✓ Complete data integrity maintained")
    
    return True

if __name__ == "__main__":
    success = test_piece_tester_data_flow()
    sys.exit(0 if success else 1)
