#!/usr/bin/env python3
"""
Test Piece Loading Fix - Adventure Chess
Tests that piece loading works correctly with the simple bridge
"""

import sys
import os

# Add the project root to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_piece_loading():
    """Test piece loading functionality"""
    print("=== Testing Piece Loading with Simple Bridge ===")
    
    try:
        from editors.piece_editor import PieceEditorWindow
        from utils.simple_bridge import simple_bridge
        
        # Create piece editor
        piece_editor = PieceEditorWindow()
        
        print("\n--- Test 1: List Available Pieces ---")
        
        # Test listing pieces
        pieces = simple_bridge.list_pieces()
        print(f"✓ Found {len(pieces)} pieces: {pieces[:5]}...")  # Show first 5
        
        if not pieces:
            print("❌ No pieces found")
            return False
        
        # Test 2: Load a piece
        print("\n--- Test 2: Load Adventure Bishop ---")
        
        piece_data, error = simple_bridge.load_piece_for_ui("Adventure Bishop")
        
        if piece_data and not error:
            print("✓ Adventure Bishop loaded successfully")
            print(f"  Name: {piece_data.get('name', 'Unknown')}")
            print(f"  Movement type: {piece_data.get('movement', {}).get('type', 'Unknown')}")
            
            # Verify movement data structure
            movement = piece_data.get('movement', {})
            if 'type' in movement and 'pattern' in movement and 'piecePosition' in movement:
                print("✓ Movement data has correct structure")
            else:
                print(f"❌ Movement data structure incorrect: {list(movement.keys())}")
                return False
                
        else:
            print(f"❌ Failed to load Adventure Bishop: {error}")
            return False
        
        # Test 3: Load into editor
        print("\n--- Test 3: Load into Editor ---")
        
        piece_editor.load_piece_data(piece_data)
        
        # Check if data loaded correctly
        if piece_editor.name_edit.text() == piece_data.get('name', ''):
            print("✓ Piece name loaded correctly")
        else:
            print(f"❌ Piece name not loaded correctly")
            return False
        
        if piece_editor.selected_movement_type == piece_data.get('movement', {}).get('type', ''):
            print("✓ Movement type loaded correctly")
        else:
            print(f"❌ Movement type not loaded correctly: {piece_editor.selected_movement_type} vs {piece_data.get('movement', {}).get('type', '')}")
            return False
        
        # Test 4: Dropdown functionality
        print("\n--- Test 4: Dropdown Functionality ---")
        
        piece_editor.refresh_load_piece_dropdown()
        
        # Check if dropdown has items
        if piece_editor.load_piece_combo.count() > 1:  # More than just placeholder
            print("✓ Dropdown populated with pieces")
        else:
            print("❌ Dropdown not populated")
            return False
        
        # Test 5: Save and reload cycle
        print("\n--- Test 5: Save and Reload Cycle ---")
        
        # Modify the piece slightly
        piece_editor.name_edit.setText("Test Modified Bishop")
        
        # Save
        success, error = simple_bridge.save_piece_from_ui(piece_editor, "test_modified_bishop")
        
        if success:
            print("✓ Modified piece saved successfully")
        else:
            print(f"❌ Failed to save modified piece: {error}")
            return False
        
        # Load back
        loaded_data, error = simple_bridge.load_piece_for_ui("test_modified_bishop")
        
        if loaded_data and not error:
            print("✓ Modified piece loaded back successfully")
            
            if loaded_data.get('name') == "Test Modified Bishop":
                print("✓ Modifications preserved")
            else:
                print(f"❌ Modifications not preserved: {loaded_data.get('name')}")
                return False
                
        else:
            print(f"❌ Failed to load modified piece: {error}")
            return False
        
        # Clean up
        try:
            simple_bridge.delete_piece("test_modified_bishop")
            print("🧹 Test file cleaned up")
        except:
            pass
        
        return True
        
    except Exception as e:
        print(f"❌ Piece loading test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_piece_loading_tests():
    """Run piece loading tests"""
    print("🔍 PIECE LOADING FIX VERIFICATION")
    print("=" * 60)
    
    success = test_piece_loading()
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 Piece loading test passed!")
        print("✓ Simple bridge working correctly")
        print("✓ Piece loading and saving functional")
        print("✓ Editor integration successful")
        print("✓ Dropdown functionality working")
        return True
    else:
        print("⚠️  Piece loading test failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    # Create QApplication for testing
    from PyQt6.QtWidgets import QApplication
    app = QApplication(sys.argv)
    
    # Run the tests directly
    success = run_piece_loading_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)
