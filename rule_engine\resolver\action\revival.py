#!/usr/bin/env python3
"""
Revival Tag Resolver for Adventure Chess Rule Engine
Handles piece revival with various revival modes and costs
"""

from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class RevivalTagResolver(BaseTagResolver):
    """Resolver for the 'revival' tag"""
    
    def _get_tag_name(self) -> str:
        return "revival"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.ACTION
    
    def _get_conflicts(self) -> List[str]:
        return ["banish"]
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        """Process revival tag and return revival effects"""
        effects = []
        revival_data = extract_tag_data(ability, "revival")
        
        # Basic revival effect
        revival_effect = TagEffect(
            effect_type="revival",
            priority=85,
            immediate=True,
            parameters={
                "action": "revival",
                "mode": revival_data.get("mode", "standard"),
                "max_cost": revival_data.get("max_cost", 0),
                "self_sacrifice": revival_data.get("sacrifice", False),
                "with_points": revival_data.get("with_points", False),
                "points": revival_data.get("points", 0)
            }
        )
        effects.append(revival_effect)
        
        return effects
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        """Get targeting requirements for revival abilities"""
        return {
            "requires_target": True,
            "target_type": "position",
            "min_targets": 1,
            "max_targets": 1,
            "target_must_be_empty": True,
            "range_required": True
        }
