#!/usr/bin/env python3
"""Trap Tile Tag Resolver"""
from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect

class TrapTileTagResolver(BaseTagResolver):
    def _get_tag_name(self) -> str: return "trapTile"
    def _get_category(self) -> TagCategory: return TagCategory.ACTION
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(effect_type="trap_tile", priority=30, immediate=True, parameters={"action": "trap_tile"})]
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        return {"requires_target": True, "target_type": "position", "min_targets": 1, "max_targets": 5}
