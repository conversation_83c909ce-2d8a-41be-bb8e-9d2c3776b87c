#!/usr/bin/env python3
"""Add Obstacle Tag Resolver"""
from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect

class AddObstacleTagResolver(BaseTagResolver):
    def _get_tag_name(self) -> str: return "addObstacle"
    def _get_category(self) -> TagCategory: return TagCategory.ACTION
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(effect_type="add_obstacle", priority=40, immediate=True, parameters={"action": "add_obstacle"})]
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        return {"requires_target": True, "target_type": "position", "min_targets": 1, "max_targets": 3}
