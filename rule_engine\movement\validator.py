#!/usr/bin/env python3
"""
Movement Validator for Adventure Chess Rule Engine
Comprehensive movement validation with ability integration
"""

from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
import logging

# Import from schemas for data structures
from schemas.base import Coordinate
from core.tester_engine import PieceColor

from .patterns import MovementPatternProcessor, MovementResult
from .pathfinding import PathfindingEngine, LineOfSightCalculator


class ValidationLevel(Enum):
    """Movement validation strictness levels"""
    BASIC = "basic"           # Basic bounds and collision checking
    STANDARD = "standard"     # Standard chess-like validation
    ADVANCED = "advanced"     # Full ability integration and complex rules


@dataclass
class MovementValidationResult:
    """Result of movement validation"""
    is_valid: bool
    can_move: bool = False
    can_attack: bool = False
    can_use_ability: bool = False

    # Detailed information
    target_position: Optional[Coordinate] = None
    path: Optional[List[Coordinate]] = None
    blocked_by: Optional[List[Coordinate]] = None
    error_message: str = ""
    warning_message: str = ""

    # Cost information
    movement_cost: int = 0
    additional_costs: Optional[Dict[str, int]] = None
    
    def __post_init__(self):
        if self.path is None:
            self.path = []
        if self.blocked_by is None:
            self.blocked_by = []
        if self.additional_costs is None:
            self.additional_costs = {}


class MovementValidator:
    """
    Comprehensive movement validator
    Integrates pattern processing, pathfinding, and ability effects
    """
    
    def __init__(self, validation_level: ValidationLevel = ValidationLevel.STANDARD):
        self.logger = logging.getLogger(__name__)
        self.validation_level = validation_level
        
        # Component systems
        self.pattern_processor = MovementPatternProcessor()
        self.pathfinding_engine = PathfindingEngine()
        self.los_calculator = LineOfSightCalculator()
        
        # Validation configuration
        self.strict_bounds_checking = True
        self.allow_piece_stacking = False
        self.require_valid_path = True
        
    def validate_movement(self, piece_data: Dict[str, Any], current_position: Coordinate,
                         target_position: Coordinate, game_state: Any,
                         context: Any = None) -> MovementValidationResult:
        """
        Validate a movement from current to target position
        
        Args:
            piece_data: Piece definition data
            current_position: Current piece position
            target_position: Desired target position
            game_state: Current game state
            context: Action context with additional information
            
        Returns:
            MovementValidationResult with detailed validation information
        """
        result = MovementValidationResult(
            is_valid=False,
            target_position=target_position
        )
        
        try:
            # Basic validation
            if not self._validate_basic_requirements(piece_data, current_position, target_position, result):
                return result
            
            # Get valid moves for this piece
            movement_result = self.pattern_processor.calculate_valid_moves(
                piece_data, current_position, game_state
            )
            
            # Check if target is in valid moves
            if not self._validate_target_in_valid_moves(target_position, movement_result, result):
                return result
            
            # Validate path if required
            if self.require_valid_path:
                if not self._validate_movement_path(piece_data, current_position, target_position, 
                                                  game_state, result):
                    return result
            
            # Check for movement restrictions
            if not self._validate_movement_restrictions(piece_data, current_position, target_position,
                                                      game_state, context, result):
                return result
            
            # Calculate movement cost
            self._calculate_movement_cost(piece_data, current_position, target_position, 
                                        game_state, context, result)
            
            # Advanced validation if enabled
            if self.validation_level == ValidationLevel.ADVANCED:
                if not self._validate_advanced_requirements(piece_data, current_position, target_position,
                                                          game_state, context, result):
                    return result
            
            # If we get here, movement is valid
            result.is_valid = True
            
        except Exception as e:
            self.logger.error(f"Error validating movement: {str(e)}")
            result.error_message = f"Validation error: {str(e)}"
        
        return result
    
    def get_all_valid_moves(self, piece_data: Dict[str, Any], current_position: Coordinate,
                          game_state: Any, context: Any = None) -> MovementResult:
        """
        Get all valid moves for a piece at current position
        
        Returns:
            MovementResult with all valid positions categorized
        """
        return self.pattern_processor.calculate_valid_moves(piece_data, current_position, game_state)
    
    def _validate_basic_requirements(self, piece_data: Dict[str, Any], current_position: Coordinate,
                                   target_position: Coordinate, result: MovementValidationResult) -> bool:
        """Validate basic movement requirements"""
        # Check bounds
        if self.strict_bounds_checking:
            if not (0 <= target_position.row < 8 and 0 <= target_position.col < 8):
                result.error_message = "Target position is outside board bounds"
                return False
        
        # Check not moving to same position
        if current_position.row == target_position.row and current_position.col == target_position.col:
            result.error_message = "Cannot move to current position"
            return False
        
        # Check piece can move (not immobilized, etc.)
        if not self._can_piece_move(piece_data):
            result.error_message = "Piece cannot move (immobilized or committed)"
            return False
        
        return True
    
    def _validate_target_in_valid_moves(self, target_position: Coordinate, movement_result: MovementResult,
                                      result: MovementValidationResult) -> bool:
        """Check if target position is in the list of valid moves"""
        all_valid = movement_result.get_all_valid_positions()
        
        for pos in all_valid:
            if pos.row == target_position.row and pos.col == target_position.col:
                # Determine what type of action is valid at this position
                if target_position in movement_result.valid_moves:
                    result.can_move = True
                if target_position in movement_result.valid_attacks:
                    result.can_attack = True
                if target_position in movement_result.valid_actions:
                    result.can_use_ability = True
                return True
        
        result.error_message = "Target position is not reachable by piece movement pattern"
        return False
    
    def _validate_movement_path(self, piece_data: Dict[str, Any], current_position: Coordinate,
                              target_position: Coordinate, game_state: Any,
                              result: MovementValidationResult) -> bool:
        """Validate the path from current to target position"""
        # Get piece abilities for pathfinding
        abilities = piece_data.get('abilities', [])
        ability_names = []
        for ability in abilities:
            if isinstance(ability, str):
                ability_names.append(ability)
            elif isinstance(ability, dict):
                ability_names.append(ability.get('name', ''))
        
        # Find path
        path = self.pathfinding_engine.find_path(
            current_position, target_position, game_state,
            piece_data.get('id'), ability_names
        )
        
        if not path:
            result.error_message = "No valid path to target position"
            return False
        
        result.path = path
        return True
    
    def _validate_movement_restrictions(self, piece_data: Dict[str, Any], current_position: Coordinate,
                                      target_position: Coordinate, game_state: Any, context: Any,
                                      result: MovementValidationResult) -> bool:
        """Validate movement restrictions from abilities and status effects"""
        # Check for immobilization
        if self._is_piece_immobilized(piece_data, game_state):
            result.error_message = "Piece is immobilized and cannot move"
            return False
        
        # Check for commitment
        if self._is_piece_committed(piece_data):
            result.error_message = "Piece is committed and cannot move"
            return False
        
        # Check for starting position requirement
        if self._requires_starting_position(piece_data) and self._has_moved_from_start(piece_data):
            result.error_message = "Piece has moved from starting position and cannot use this movement"
            return False
        
        # Check for color directional restrictions
        if piece_data.get('color_directional', False):
            if not self._validate_color_directional_movement(piece_data, current_position, target_position):
                result.error_message = "Movement violates color directional restrictions"
                return False
        
        return True
    
    def _calculate_movement_cost(self, piece_data: Dict[str, Any], current_position: Coordinate,
                               target_position: Coordinate, game_state: Any, context: Any,
                               result: MovementValidationResult):
        """Calculate the cost of movement"""
        base_cost = 0  # Basic movement is usually free
        
        # Check for movement cost modifiers from abilities
        abilities = piece_data.get('abilities', [])
        for ability in abilities:
            if isinstance(ability, dict) and 'noTurnCost' in ability.get('tags', []):
                # Movement doesn't cost a turn
                base_cost = 0
                break
        
        # Calculate distance-based costs if applicable
        distance = max(abs(target_position.row - current_position.row),
                      abs(target_position.col - current_position.col))
        
        # Add any additional costs from context
        if context and hasattr(context, 'cost'):
            base_cost += context.cost.modifier_cost
        
        result.movement_cost = base_cost
    
    def _validate_advanced_requirements(self, piece_data: Dict[str, Any], current_position: Coordinate,
                                      target_position: Coordinate, game_state: Any, context: Any,
                                      result: MovementValidationResult) -> bool:
        """Advanced validation including complex ability interactions"""
        # Check for adjacency requirements
        if self._has_adjacency_requirement(piece_data):
            if not self._validate_adjacency_requirement(piece_data, current_position, game_state):
                result.error_message = "Adjacency requirement not met"
                return False
        
        # Check for line of sight requirements
        if self._has_los_requirement(piece_data):
            los_result = self.los_calculator.has_line_of_sight(
                current_position, target_position, game_state,
                piece_data.get('id')
            )
            if not los_result.has_line_of_sight:
                result.error_message = "Line of sight blocked"
                result.blocked_by = los_result.blocked_by
                return False
        
        return True
    
    def _can_piece_move(self, piece_data: Dict[str, Any]) -> bool:
        """Check if piece can move based on its current state"""
        # This would check piece state for immobilization, commitment, etc.
        # For now, basic implementation
        return True
    
    def _is_piece_immobilized(self, piece_data: Dict[str, Any], game_state: Any) -> bool:
        """Check if piece is immobilized"""
        # This would check status effects
        return False
    
    def _is_piece_committed(self, piece_data: Dict[str, Any]) -> bool:
        """Check if piece is committed"""
        # This would check commitment state
        return False
    
    def _requires_starting_position(self, piece_data: Dict[str, Any]) -> bool:
        """Check if piece has abilities requiring starting position"""
        abilities = piece_data.get('abilities', [])
        for ability in abilities:
            if isinstance(ability, dict) and 'requiresStartingPosition' in ability.get('tags', []):
                return True
        return False
    
    def _has_moved_from_start(self, piece_data: Dict[str, Any]) -> bool:
        """Check if piece has moved from starting position"""
        # This would check piece movement history
        return False
    
    def _validate_color_directional_movement(self, piece_data: Dict[str, Any], 
                                           current_position: Coordinate, target_position: Coordinate) -> bool:
        """Validate color directional movement restrictions"""
        owner = piece_data.get('owner')
        if owner == PieceColor.WHITE:
            # White moves "up" (decreasing row numbers)
            return target_position.row <= current_position.row
        else:
            # Black moves "down" (increasing row numbers)
            return target_position.row >= current_position.row
    
    def _has_adjacency_requirement(self, piece_data: Dict[str, Any]) -> bool:
        """Check if piece has adjacency requirements"""
        abilities = piece_data.get('abilities', [])
        for ability in abilities:
            if isinstance(ability, dict) and 'adjacencyRequired' in ability.get('tags', []):
                return True
        return False
    
    def _validate_adjacency_requirement(self, piece_data: Dict[str, Any], 
                                      position: Coordinate, game_state: Any) -> bool:
        """Validate adjacency requirements"""
        # This would check for required adjacent pieces
        return True
    
    def _has_los_requirement(self, piece_data: Dict[str, Any]) -> bool:
        """Check if piece has line of sight requirements"""
        abilities = piece_data.get('abilities', [])
        for ability in abilities:
            if isinstance(ability, dict) and 'losRequired' in ability.get('tags', []):
                return True
        return False
    
    def set_validation_level(self, level: ValidationLevel):
        """Set validation strictness level"""
        self.validation_level = level
        self.logger.debug(f"Movement validation level set to: {level.value}")
    
    def clear_caches(self):
        """Clear all movement caches"""
        self.pattern_processor.clear_cache()
        self.logger.debug("Movement validator caches cleared")
