#!/usr/bin/env python3
"""
Cost Modifier Tag Resolvers for Adventure Chess Rule Engine
Handles cost-related conditional tags
"""

from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class NoTurnCostTagResolver(BaseTagResolver):
    """Resolver for noTurnCost tag"""
    
    def _get_tag_name(self) -> str:
        return "noTurnCost"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(
            effect_type="cost_modifier",
            priority=250,
            immediate=True,
            parameters={
                "modifier_type": "no_turn_cost",
                "cost_override": 0
            }
        )]
    
    def calculate_cost_modifier(self, base_cost: int, ability: Ability, context: Any) -> int:
        return 0  # No turn cost


class CostPerDistanceTagResolver(BaseTagResolver):
    """Resolver for costPerDistance tag"""
    
    def _get_tag_name(self) -> str:
        return "costPerDistance"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        cost_data = extract_tag_data(ability, "costPerDistance")
        
        return [TagEffect(
            effect_type="cost_modifier",
            priority=240,
            immediate=True,
            parameters={
                "modifier_type": "cost_per_distance",
                "cost_per_unit": cost_data.get("cost", 1),
                "max_cost": cost_data.get("max_cost", 10)
            }
        )]
    
    def calculate_cost_modifier(self, base_cost: int, ability: Ability, context: Any) -> int:
        cost_data = extract_tag_data(ability, "costPerDistance")
        
        if context.targeting.primary_target and context.source_position:
            target = context.targeting.primary_target
            source = context.source_position
            distance = max(abs(target.row - source.row), abs(target.col - source.col))
            
            additional_cost = distance * cost_data.get("cost", 1)
            max_cost = cost_data.get("max_cost", 10)
            
            return min(base_cost + additional_cost, max_cost)
        
        return base_cost


class CostPerTargetTagResolver(BaseTagResolver):
    """Resolver for costPerTarget tag"""
    
    def _get_tag_name(self) -> str:
        return "costPerTarget"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        cost_data = extract_tag_data(ability, "costPerTarget")
        
        return [TagEffect(
            effect_type="cost_modifier",
            priority=235,
            immediate=True,
            parameters={
                "modifier_type": "cost_per_target",
                "cost_per_target": cost_data.get("cost", 1),
                "first_target_free": cost_data.get("first_free", False)
            }
        )]
    
    def calculate_cost_modifier(self, base_cost: int, ability: Ability, context: Any) -> int:
        cost_data = extract_tag_data(ability, "costPerTarget")
        
        target_count = len(context.targeting.all_targets)
        cost_per_target = cost_data.get("cost", 1)
        first_free = cost_data.get("first_free", False)
        
        if first_free and target_count > 0:
            additional_cost = (target_count - 1) * cost_per_target
        else:
            additional_cost = target_count * cost_per_target
        
        return base_cost + additional_cost


class VariableCostTagResolver(BaseTagResolver):
    """Resolver for variableCost tag"""
    
    def _get_tag_name(self) -> str:
        return "variableCost"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.CONDITION
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        cost_data = extract_tag_data(ability, "variableCost")
        
        return [TagEffect(
            effect_type="cost_modifier",
            priority=230,
            immediate=True,
            parameters={
                "modifier_type": "variable_cost",
                "min_cost": cost_data.get("min", 1),
                "max_cost": cost_data.get("max", 10),
                "cost_factor": cost_data.get("factor", "points_spent")
            }
        )]
    
    def calculate_cost_modifier(self, base_cost: int, ability: Ability, context: Any) -> int:
        cost_data = extract_tag_data(ability, "variableCost")
        
        min_cost = cost_data.get("min", 1)
        max_cost = cost_data.get("max", 10)
        factor = cost_data.get("factor", "points_spent")
        
        if factor == "points_spent":
            # Cost based on points player wants to spend
            desired_cost = getattr(context, 'desired_cost', base_cost)
            return max(min_cost, min(max_cost, desired_cost))
        
        elif factor == "piece_value":
            # Cost based on piece's current value
            source_piece = context.game_state.get_piece(context.source_piece_id)
            if source_piece:
                piece_value = getattr(source_piece, 'current_points', 1)
                return max(min_cost, min(max_cost, piece_value))
        
        return base_cost
