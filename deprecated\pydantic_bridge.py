"""
DEPRECATED: Legacy Bridge layer between Pydantic models and existing UI components

⚠️ WARNING: This module is deprecated and no longer used by the main application.
All components have been migrated to use SimpleBridge for direct JSON operations.

This file is kept for:
- Legacy compatibility during transition
- Validation utilities that may still be needed
- Migration tools and data conversion

Use utils.simple_bridge.SimpleBridge for all new development.
"""

import logging
from typing import Dict, List, Any, Optional, Tuple, Union

from schemas import (
    Piece, Ability, pydantic_data_manager, 
    CompatibilityLayer, DataMigrationManager
)

logger = logging.getLogger(__name__)


class PydanticBridge:
    """
    Bridge layer that allows existing UI code to work with Pydantic models
    without requiring immediate refactoring of all UI components
    """
    
    # ========== PIECE OPERATIONS ==========
    
    @staticmethod
    def get_piece_data_from_ui(parent) -> Dict[str, Any]:
        """
        Get piece data from UI components using standardized EditorDataInterface
        This replaces the old DataManager.get_piece_data method
        """
        try:
            # Use standardized data collection through EditorDataInterface
            from utils.editor_data_interface import EditorDataInterface
            legacy_data = EditorDataInterface.collect_data_from_ui(parent, "piece")

            # Migrate to Pydantic model for validation
            piece, warnings = DataMigrationManager.migrate_piece_dict_to_model(legacy_data)

            if piece is None:
                logger.error("Failed to create piece model from UI data")
                return legacy_data  # Return original data if migration fails

            # Log any warnings
            for warning in warnings:
                logger.warning(f"Piece data warning: {warning}")

            # Return as dictionary for UI compatibility
            return piece.to_legacy_dict()

        except Exception as e:
            logger.error(f"Error getting piece data from UI: {e}")
            # Fallback to EditorDataInterface
            try:
                from utils.editor_data_interface import EditorDataInterface
                return EditorDataInterface.collect_data_from_ui(parent, "piece")
            except Exception as fallback_error:
                logger.error(f"Fallback also failed: {fallback_error}")
                return {'version': '1.0.0', 'name': '', 'description': '', 'role': 'Commander'}
    
    @staticmethod
    def set_piece_data_to_ui(parent, data: Dict[str, Any]):
        """
        Set piece data to UI components using standardized EditorDataInterface
        This replaces the old DataManager.set_piece_data method
        """
        try:
            # Migrate data to ensure it's valid
            piece, warnings = DataMigrationManager.migrate_piece_dict_to_model(data)

            if piece is None:
                logger.error("Failed to migrate piece data for UI")
                # Fallback to EditorDataInterface
                from utils.editor_data_interface import EditorDataInterface
                EditorDataInterface.populate_ui_from_data(parent, data, "piece")
                return

            # Log any warnings
            for warning in warnings:
                logger.warning(f"Piece data warning: {warning}")

            # Convert back to dictionary and set to UI using standardized interface
            validated_data = piece.to_legacy_dict()
            from utils.editor_data_interface import EditorDataInterface
            EditorDataInterface.populate_ui_from_data(parent, validated_data, "piece")

            logger.info("Piece data validated and set to UI successfully")

        except Exception as e:
            logger.error(f"Error setting piece data to UI: {e}")
            # Fallback to EditorDataInterface
            try:
                from utils.editor_data_interface import EditorDataInterface
                EditorDataInterface.populate_ui_from_data(parent, data, "piece")
            except Exception as fallback_error:
                logger.error(f"Fallback also failed: {fallback_error}")
    
    @staticmethod
    def load_piece_for_ui(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Load piece for UI consumption (returns dictionary)
        This replaces direct piece manager calls
        """
        return CompatibilityLayer.load_piece_as_dict(filename)
    
    @staticmethod
    def save_piece_from_ui(parent, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save piece from UI data with Pydantic validation
        This replaces direct piece manager calls
        """
        try:
            # Get data from UI
            piece_data = PydanticBridge.get_piece_data_from_ui(parent)
            
            # Save using Pydantic system
            return CompatibilityLayer.save_piece_from_dict(piece_data, filename)
            
        except Exception as e:
            logger.error(f"Error saving piece from UI: {e}")
            return False, str(e)
    
    # ========== ABILITY OPERATIONS ==========
    
    @staticmethod
    def get_ability_data_from_ui(parent) -> Dict[str, Any]:
        """
        Get ability data from UI components using standardized EditorDataInterface
        This replaces the old CompleteDataManager.get_complete_ability_data method
        """
        try:
            # Use standardized data collection through EditorDataInterface
            from utils.editor_data_interface import EditorDataInterface
            legacy_data = EditorDataInterface.collect_data_from_ui(parent, "ability")

            # Migrate to Pydantic model for validation
            ability, warnings = DataMigrationManager.migrate_ability_dict_to_model(legacy_data)

            if ability is None:
                logger.error("Failed to create ability model from UI data")
                return legacy_data  # Return original data if migration fails

            # Log any warnings
            for warning in warnings:
                logger.warning(f"Ability data warning: {warning}")

            # Return as dictionary for UI compatibility
            return ability.to_legacy_dict()

        except Exception as e:
            logger.error(f"Error getting ability data from UI: {e}")
            # Fallback to EditorDataInterface
            try:
                from utils.editor_data_interface import EditorDataInterface
                return EditorDataInterface.collect_data_from_ui(parent, "ability")
            except Exception as fallback_error:
                logger.error(f"Fallback also failed: {fallback_error}")
                return {'version': '1.0.0', 'name': '', 'description': '', 'cost': 0, 'tags': []}
    
    @staticmethod
    def set_ability_data_to_ui(parent, data: Dict[str, Any]):
        """
        Set ability data to UI components using standardized EditorDataInterface
        This replaces the old CompleteDataManager.set_complete_ability_data method
        """
        try:
            # Migrate data to ensure it's valid
            ability, warnings = DataMigrationManager.migrate_ability_dict_to_model(data)

            if ability is None:
                logger.error("Failed to migrate ability data for UI")
                # Fallback to EditorDataInterface
                from utils.editor_data_interface import EditorDataInterface
                EditorDataInterface.populate_ui_from_data(parent, data, "ability")
                return

            # Log any warnings
            for warning in warnings:
                logger.warning(f"Ability data warning: {warning}")

            # Convert back to dictionary and set to UI using standardized interface
            validated_data = ability.to_legacy_dict()
            from utils.editor_data_interface import EditorDataInterface
            EditorDataInterface.populate_ui_from_data(parent, validated_data, "ability")

        except Exception as e:
            logger.error(f"Error setting ability data to UI: {e}")
            # Fallback to EditorDataInterface
            try:
                from utils.editor_data_interface import EditorDataInterface
                EditorDataInterface.populate_ui_from_data(parent, data, "ability")
            except Exception as fallback_error:
                logger.error(f"Fallback also failed: {fallback_error}")
    
    @staticmethod
    def load_ability_for_ui(filename: str) -> Tuple[Optional[Dict[str, Any]], Optional[str]]:
        """
        Load ability for UI consumption (returns dictionary)
        This replaces direct ability manager calls
        """
        return CompatibilityLayer.load_ability_as_dict(filename)
    
    @staticmethod
    def save_ability_from_ui(parent, filename: Optional[str] = None) -> Tuple[bool, Optional[str]]:
        """
        Save ability from UI data with Pydantic validation
        This replaces direct ability manager calls
        """
        try:
            # Get data from UI
            ability_data = PydanticBridge.get_ability_data_from_ui(parent)
            
            # Save using Pydantic system
            return CompatibilityLayer.save_ability_from_dict(ability_data, filename)
            
        except Exception as e:
            logger.error(f"Error saving ability from UI: {e}")
            return False, str(e)
    
    # ========== VALIDATION OPERATIONS ==========
    
    @staticmethod
    def validate_piece_data(data: Dict[str, Any]) -> Tuple[bool, List[str], List[str]]:
        """
        Validate piece data using Pydantic models
        Returns: (is_valid, errors, warnings)
        """
        try:
            piece, warnings = DataMigrationManager.migrate_piece_dict_to_model(data)
            
            if piece is None:
                return False, ["Failed to create piece model"], []
            
            # Additional validation can be added here
            errors = []
            
            # Check for required fields
            if not piece.name or not piece.name.strip():
                errors.append("Piece name is required")
            
            # Validate movement configuration
            if piece.movement.type == "custom":
                if piece.movement.pattern is None:
                    errors.append("Custom movement requires a pattern")
                if piece.movement.piece_position is None:
                    errors.append("Custom movement requires piece position")
            
            is_valid = len(errors) == 0
            return is_valid, errors, warnings
            
        except Exception as e:
            return False, [f"Validation error: {e}"], []
    
    @staticmethod
    def validate_ability_data(data: Dict[str, Any]) -> Tuple[bool, List[str], List[str]]:
        """
        Validate ability data using Pydantic models
        Returns: (is_valid, errors, warnings)
        """
        try:
            ability, warnings = DataMigrationManager.migrate_ability_dict_to_model(data)
            
            if ability is None:
                return False, ["Failed to create ability model"], []
            
            # Validate all tag data
            tag_errors = ability.validate_all_tags()
            errors = []
            
            # Check for required fields
            if not ability.name or not ability.name.strip():
                errors.append("Ability name is required")
            
            # Add tag validation errors
            for tag, error in tag_errors.items():
                errors.append(f"Tag '{tag}': {error}")
            
            is_valid = len(errors) == 0
            return is_valid, errors, warnings
            
        except Exception as e:
            return False, [f"Validation error: {e}"], []
    
    # ========== UTILITY METHODS ==========
    
    @staticmethod
    def get_piece_list() -> List[str]:
        """Get list of available pieces"""
        return pydantic_data_manager.list_pieces()
    
    @staticmethod
    def get_ability_list() -> List[str]:
        """Get list of available abilities"""
        return pydantic_data_manager.list_abilities()
    
    @staticmethod
    def clear_cache():
        """Clear data cache"""
        pydantic_data_manager.clear_cache()
    
    @staticmethod
    def get_validation_errors() -> List[str]:
        """Get any validation errors that occurred"""
        return pydantic_data_manager.get_error_log()


# Create a global instance for easy access
pydantic_bridge = PydanticBridge()
