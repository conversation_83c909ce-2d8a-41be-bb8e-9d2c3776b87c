#!/usr/bin/env python3
"""
Summon Tag Resolver for Adventure Chess Rule Engine
Handles piece summoning with various summoning modes and restrictions
"""

from typing import List, Dict, Any
import logging
import uuid

from schemas import Ability
from schemas.base import Coordinate
from core.tester_engine import PieceColor
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class SummonTagResolver(BaseTagResolver):
    """
    Resolver for the 'summon' tag
    Handles piece summoning with support for different piece types and restrictions
    """
    
    def _get_tag_name(self) -> str:
        return "summon"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.ACTION
    
    def _get_conflicts(self) -> List[str]:
        return ["banish"]  # Summon conflicts with banishment
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        """
        Process summon tag and return summoning effects
        
        Args:
            ability: The ability containing the summon tag
            context: ActionContext with game state and action details
            
        Returns:
            List of TagEffect objects for summoning
        """
        effects = []
        
        # Extract summon-specific data
        summon_data = extract_tag_data(ability, "summon")
        
        # Get summon parameters
        summon_list = summon_data.get("list", [])
        summon_max = summon_data.get("max", 1)
        summon_mode = summon_data.get("mode", "create")  # create, recall, duplicate
        summon_duration = summon_data.get("duration", -1)  # -1 for permanent
        
        # Validate summon limits
        if not self._can_summon(context, summon_max):
            return effects
        
        # Determine what to summon
        pieces_to_summon = self._determine_summon_targets(context, summon_list, summon_max, summon_mode)
        
        # Create summon effects for each piece
        for summon_info in pieces_to_summon:
            summon_effect = TagEffect(
                effect_type="summon",
                priority=80,  # High priority for summoning
                immediate=True,
                parameters={
                    "action": "summon",
                    "mode": summon_mode,
                    "piece_type": summon_info["piece_type"],
                    "position": summon_info["position"],
                    "owner": context.current_player,
                    "summoner_id": context.source_piece_id,
                    "duration": summon_duration,
                    "abilities": summon_info.get("abilities", []),
                    "stats": summon_info.get("stats", {}),
                    "temporary": summon_duration > 0
                }
            )
            effects.append(summon_effect)
        
        # Add post-summon effects
        if summon_data.get("trigger_on_summon"):
            trigger_effect = TagEffect(
                effect_type="trigger",
                priority=30,
                immediate=False,
                parameters={
                    "trigger_type": "on_summon",
                    "summoned_pieces": [info["piece_id"] for info in pieces_to_summon],
                    "abilities": summon_data.get("trigger_abilities", [])
                }
            )
            effects.append(trigger_effect)
        
        # Add duration tracking for temporary summons
        if summon_duration > 0:
            for summon_info in pieces_to_summon:
                duration_effect = TagEffect(
                    effect_type="duration_tracker",
                    priority=10,
                    immediate=False,
                    parameters={
                        "target_piece_id": summon_info["piece_id"],
                        "duration": summon_duration,
                        "effect_type": "summon_expiry",
                        "on_expire": "banish"
                    }
                )
                effects.append(duration_effect)
        
        return effects
    
    def _can_summon(self, context: Any, max_summons: int) -> bool:
        """
        Check if summoning is possible
        
        Args:
            context: ActionContext
            max_summons: Maximum number of pieces that can be summoned
            
        Returns:
            True if summoning is allowed
        """
        # Check if there are valid summon positions
        valid_positions = self._get_valid_summon_positions(context)
        if len(valid_positions) < max_summons:
            return False
        
        # Check summoner's current summon count
        summoner = context.game_state.get_piece(context.source_piece_id)
        if summoner and hasattr(summoner, 'summoned_pieces'):
            current_summons = len(summoner.summoned_pieces)
            max_total_summons = getattr(summoner, 'max_summons', 5)  # Default limit
            if current_summons + max_summons > max_total_summons:
                return False
        
        return True
    
    def _determine_summon_targets(self, context: Any, summon_list: List[str], 
                                max_summons: int, summon_mode: str) -> List[Dict[str, Any]]:
        """
        Determine what pieces to summon
        
        Args:
            context: ActionContext
            summon_list: List of piece types that can be summoned
            max_summons: Maximum number to summon
            summon_mode: Mode of summoning (create, recall, duplicate)
            
        Returns:
            List of dictionaries with summon information
        """
        pieces_to_summon = []
        valid_positions = self._get_valid_summon_positions(context)
        
        if summon_mode == "create":
            # Create new pieces from summon list
            for i in range(min(max_summons, len(valid_positions), len(summon_list))):
                piece_type = summon_list[i % len(summon_list)]
                piece_info = {
                    "piece_id": str(uuid.uuid4()),
                    "piece_type": piece_type,
                    "position": valid_positions[i],
                    "abilities": self._get_piece_abilities(piece_type),
                    "stats": self._get_piece_stats(piece_type)
                }
                pieces_to_summon.append(piece_info)
        
        elif summon_mode == "recall":
            # Recall previously summoned pieces
            summoner = context.game_state.get_piece(context.source_piece_id)
            if summoner and hasattr(summoner, 'banished_pieces'):
                for i, piece_id in enumerate(summoner.banished_pieces[:max_summons]):
                    if i < len(valid_positions):
                        piece_info = {
                            "piece_id": piece_id,
                            "piece_type": "recalled",
                            "position": valid_positions[i],
                            "recall": True
                        }
                        pieces_to_summon.append(piece_info)
        
        elif summon_mode == "duplicate":
            # Duplicate existing pieces
            source_piece = context.game_state.get_piece(context.source_piece_id)
            if source_piece:
                for i in range(min(max_summons, len(valid_positions))):
                    piece_info = {
                        "piece_id": str(uuid.uuid4()),
                        "piece_type": source_piece.piece_type,
                        "position": valid_positions[i],
                        "abilities": source_piece.abilities.copy(),
                        "stats": {
                            "max_points": source_piece.max_points,
                            "current_points": source_piece.current_points
                        },
                        "duplicate_of": source_piece.id
                    }
                    pieces_to_summon.append(piece_info)
        
        return pieces_to_summon
    
    def _get_valid_summon_positions(self, context: Any) -> List[Coordinate]:
        """
        Get valid positions for summoning
        
        Args:
            context: ActionContext
            
        Returns:
            List of valid summon positions
        """
        valid_positions = []
        
        # Check all target positions
        target_positions = context.targeting.all_targets
        
        for position in target_positions:
            if self._is_valid_summon_position(context, position):
                valid_positions.append(position)
        
        return valid_positions
    
    def _is_valid_summon_position(self, context: Any, position: Coordinate) -> bool:
        """
        Check if a position is valid for summoning
        
        Args:
            context: ActionContext
            position: Position to check
            
        Returns:
            True if position is valid for summoning
        """
        # Check if position is empty
        pieces_at_position = context.game_state.get_piece_at(position)
        if pieces_at_position:
            return False
        
        # Check if position is within board bounds
        if not (0 <= position.row < 8 and 0 <= position.col < 8):
            return False
        
        # Check for obstacles
        if hasattr(context.game_state, 'get_cell'):
            cell = context.game_state.get_cell(position)
            if hasattr(cell, 'obstacles') and cell.obstacles:
                return False
        
        return True
    
    def _get_piece_abilities(self, piece_type: str) -> List[str]:
        """
        Get default abilities for a piece type
        
        Args:
            piece_type: Type of piece being summoned
            
        Returns:
            List of ability names
        """
        # This would load from piece definitions
        # For now, return basic abilities based on type
        ability_map = {
            "pawn": ["move", "capture"],
            "knight": ["move", "capture", "lShape"],
            "bishop": ["move", "capture", "diagonal"],
            "rook": ["move", "capture", "orthogonal"],
            "queen": ["move", "capture", "any"],
            "king": ["move", "capture", "adjacency"]
        }
        
        return ability_map.get(piece_type, ["move"])
    
    def _get_piece_stats(self, piece_type: str) -> Dict[str, int]:
        """
        Get default stats for a piece type
        
        Args:
            piece_type: Type of piece being summoned
            
        Returns:
            Dictionary with piece stats
        """
        # This would load from piece definitions
        # For now, return basic stats based on type
        stats_map = {
            "pawn": {"max_points": 1, "current_points": 1},
            "knight": {"max_points": 3, "current_points": 3},
            "bishop": {"max_points": 3, "current_points": 3},
            "rook": {"max_points": 5, "current_points": 5},
            "queen": {"max_points": 9, "current_points": 9},
            "king": {"max_points": 10, "current_points": 10}
        }
        
        return stats_map.get(piece_type, {"max_points": 1, "current_points": 1})
    
    def calculate_cost_modifier(self, base_cost: int, ability: Ability, context: Any) -> int:
        """
        Calculate cost modifications for summoning
        
        Args:
            base_cost: Base ability cost
            ability: The ability being used
            context: ActionContext
            
        Returns:
            Modified cost
        """
        summon_data = extract_tag_data(ability, "summon")
        
        # Cost per piece summoned
        cost_per_summon = summon_data.get("cost_per_summon", 1)
        summon_count = min(
            summon_data.get("max", 1),
            len(context.targeting.all_targets)
        )
        
        return base_cost + (summon_count * cost_per_summon)
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        """
        Get targeting requirements for summon abilities
        
        Args:
            ability: The ability being analyzed
            
        Returns:
            Dictionary with targeting requirements
        """
        summon_data = extract_tag_data(ability, "summon")
        
        return {
            "requires_target": True,
            "target_type": "position",
            "min_targets": 1,
            "max_targets": summon_data.get("max", 1),
            "target_must_be_empty": True,
            "range_required": True,
            "line_of_sight": summon_data.get("requires_los", False)
        }
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> tuple[bool, str]:
        """
        Validate prerequisites for summon tag
        
        Args:
            ability: The ability being validated
            context: ActionContext
            
        Returns:
            (is_valid, error_message)
        """
        # Call parent validation first
        is_valid, message = super().validate_prerequisites(ability, context)
        if not is_valid:
            return is_valid, message
        
        # Check summon-specific prerequisites
        summon_data = extract_tag_data(ability, "summon")
        
        # Check if summon list is available
        summon_list = summon_data.get("list", [])
        if not summon_list and summon_data.get("mode", "create") == "create":
            return False, "No pieces available to summon"
        
        # Check summon limits
        if not self._can_summon(context, summon_data.get("max", 1)):
            return False, "Cannot summon: no valid positions or summon limit exceeded"
        
        return True, "Prerequisites satisfied"
