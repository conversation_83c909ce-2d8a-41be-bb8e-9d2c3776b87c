# 🎯 Adventure Chess Logic Engine Implementation Plan

## 📊 Current System Analysis

### ✅ **Strengths of Current System**
- **Excellent Pydantic Schema Foundation**: Your schemas are well-designed with 28+ canonical ability tags
- **Comprehensive UI System**: PyQt6 editors are feature-complete and working
- **Solid Data Structures**: Good separation between pieces, abilities, and game state
- **Working Prototype**: Current logic engine has basic functionality implemented

### 🔧 **Areas for Enhancement**
- **Tag Processing**: Current system has basic tag handling but needs modular processors
- **Complex Interactions**: Limited support for multi-ability chains and conflicts
- **Performance**: Can be optimized with caching and batch processing
- **Validation**: Needs more sophisticated rule validation and conflict detection

## 🏗️ **New Architecture Design**

### **Core Principles**
1. **Schema-Driven**: Everything driven by validated Pydantic models
2. **Modular Tag System**: One processor per ability tag
3. **Conflict Resolution**: Sophisticated validation and conflict handling
4. **UI Preservation**: Zero changes to existing editors
5. **Performance First**: Optimized for real-time gameplay

### **Suggested File Structure**
```
rule_engine/
├── __init__.py
├── engine.py               # Core RuleEngine class
├── game_state.py           # Enhanced GameState with validation
├── context.py              # ActionContext for per-action state
├── validator.py            # Comprehensive validation system
├── events.py               # Event system for reactions
├── resolver/               # Tag-specific effect resolvers
│   ├── __init__.py
│   ├── base.py             # BaseTagResolver
│   ├── action/             # Action tag resolvers
│   │   ├── move.py
│   │   ├── capture.py
│   │   ├── summon.py
│   │   ├── revival.py
│   │   ├── carry_piece.py
│   │   ├── swap_places.py
│   │   ├── displace_piece.py
│   │   ├── immobilize.py
│   │   ├── convert_piece.py
│   │   ├── duplicate.py
│   │   ├── buff_piece.py
│   │   ├── debuff_piece.py
│   │   ├── add_obstacle.py
│   │   ├── remove_obstacle.py
│   │   └── trap_tile.py
│   ├── targeting/          # Targeting tag resolvers
│   │   ├── range.py
│   │   └── area_effect.py
│   ├── condition/          # Condition tag resolvers
│   │   ├── adjacency_required.py
│   │   ├── los_required.py
│   │   ├── no_turn_cost.py
│   │   ├── share_space.py
│   │   ├── delay.py
│   │   ├── pass_through.py
│   │   └── requires_starting_position.py
│   └── special/            # Special tag resolvers
│       ├── reaction.py
│       ├── pulse_effect.py
│       ├── fog_of_war.py
│       └── invisible.py
├── movement/               # Movement system
│   ├── __init__.py
│   ├── patterns.py         # Movement pattern processors
│   ├── validator.py        # Movement validation
│   └── pathfinding.py      # Line-of-sight and pathfinding
└── utils/                  # Utilities
    ├── __init__.py
    ├── cost_calculator.py  # Cost and resource management
    ├── targeting.py        # Target selection and validation
    └── state_manager.py    # State snapshots and history
```

## 🎮 **Key Features to Implement**

### **1. Enhanced Game State Management**
- **Immutable State**: Copy-on-write for undo/replay functionality
- **Validation Engine**: Real-time state consistency checking
- **Effect Queue**: Priority-based effect resolution
- **Event System**: Trigger-based reactions and cascading effects

### **2. Modular Tag Processing System**
- **BaseTagResolver**: Common interface for all tag processors
- **Tag Categories**: Action, Targeting, Condition, Special
- **Conflict Detection**: Automatic validation of tag combinations
- **Effect Chaining**: Support for multi-step ability interactions

### **3. Advanced Movement System**
- **Pattern Support**: All movement types (orthogonal, diagonal, custom, L-shape)
- **Pathfinding**: Line-of-sight with obstacle detection
- **Custom Patterns**: 8x8 grid-based movement definitions
- **Movement Modifiers**: Abilities that affect movement behavior

### **4. Comprehensive Ability Framework**
- **28+ Canonical Tags**: All ability types implemented
- **Cost System**: Point-based resource management
- **Targeting System**: Range, area-of-effect, line-of-sight
- **Activation Modes**: Auto, manual, reaction support

## 🔧 **Critical Conflict Resolution Matrix**

Based on field_ref_completed.md analysis:

| **Conflict Type** | **Tags** | **Resolution Strategy** |
|-------------------|----------|------------------------|
| **Movement vs Immobilization** | `immobilize` vs `move`, `teleport`, `carryPiece` | Block movement when immobilized, exceptions for forced movement |
| **Passive vs Manual Activation** | `activation_mode: auto` vs `cost > 0`, `manual` | Auto abilities with costs become manual activation |
| **Visibility Logic** | `invisible` vs `alwaysVisible`, `revealOnMove` | Only one visibility state active at a time |
| **Mortality Logic** | `selfSacrifice` vs `cannotDie`, `revival.selfRevive` | Immortal pieces cannot be sacrifice targets |
| **Shared Capture Logic** | `trapTile.capture` + `shareSpace` + `carryPiece` | All units destroyed unless selective survival flag |
| **Execution Timing** | `reaction`, `delay`, `pulseEffect` | Explicit execution order: delay → reaction → pulse |
| **Repeatable vs One-Off** | `pulseEffect` vs `cost > 0`, `cooldown` | Cancel ability if insufficient points, check once per turn |
| **Position Anchoring** | `requiresStartingPosition` vs `teleport` | Break condition when piece leaves spawn square |

## 🚀 **Implementation Phases**

### **Phase 1: Core Engine Foundation** (Week 1-2)
**Deliverables:**
- Enhanced GameState with comprehensive validation
- ActionContext for per-action state tracking
- BaseTagResolver architecture
- Integration with existing Pydantic schemas
- Basic event system for reactions

**Key Components:**
- `rule_engine/engine.py` - Core RuleEngine class
- `rule_engine/game_state.py` - Enhanced GameState
- `rule_engine/context.py` - ActionContext
- `rule_engine/resolver/base.py` - BaseTagResolver

### **Phase 2: Movement & Validation Systems** (Week 3-4)
**Deliverables:**
- Comprehensive movement pattern validation
- Line-of-sight and pathfinding algorithms
- Collision detection and obstacle handling
- Custom pattern processing (8x8 grids)
- Movement modifier system

**Key Components:**
- `rule_engine/movement/` - Complete movement system
- `rule_engine/validator.py` - Validation engine
- Pattern processors for all movement types
- Pathfinding with obstacle detection

### **Phase 3: Ability Tag Processing Engine** (Week 5-6)
**Deliverables:**
- All 28+ tag processors implemented
- Cost calculation and validation system
- Target selection and validation
- Effect application and state mutation
- Tag conflict detection and resolution

**Key Components:**
- `rule_engine/resolver/action/` - All action tag processors
- `rule_engine/resolver/targeting/` - Targeting processors
- `rule_engine/resolver/condition/` - Condition processors
- `rule_engine/resolver/special/` - Special processors
- `rule_engine/utils/cost_calculator.py` - Cost management

### **Phase 4: Advanced Game Mechanics** (Week 7-8)
**Deliverables:**
- Complex ability interaction matrix
- Status effect stacking and management
- Comprehensive reaction system
- Turn management and recharge systems
- Environmental effects (obstacles, traps)

**Key Components:**
- Advanced effect chaining
- Buff/debuff stacking system
- Reaction trigger system
- Turn-based mechanics
- Environmental gameplay elements

### **Phase 5: Integration & Testing** (Week 9-10)
**Deliverables:**
- Seamless UI integration (zero editor changes)
- Comprehensive testing suite
- Performance optimization
- Complete documentation
- Example scenarios and tutorials

**Key Components:**
- UI integration layer
- Test suite with edge cases
- Performance benchmarks
- API documentation
- User guides and examples

## 🎯 **Special Features to Support**

### **Manual Abilities with Cost**
```python
# Support for clickable abilities with cost validation
if ability.activation_mode == "manual" and ability.cost > 0:
    # UI integration for manual triggering
    # Cost validation before execution
    # Per-target cost calculation support
    # Integration with existing UI without changes
```

### **Complex Tag Interactions**
```python
# Example: trapTile with embedded effects
trap_tile_effect = {
    "trapTile": {
        "capture": True,
        "immobilize": {"duration": 3},
        "teleport": {"pattern": custom_pattern},
        "addAbility": {"ability": "temporary_buff"}
    }
}
```

### **Reaction System**
```python
# Event-driven reactions
reaction_triggers = {
    "onMove": lambda piece, target: trigger_reaction(),
    "onTargeted": lambda piece, attacker: trigger_reaction(),
    "onPieceDeath": lambda piece: trigger_reaction(),
    "onAction": lambda piece, ability: trigger_reaction()
}
```

### **Pulse Effects**
```python
# Repeating abilities with interval tracking
pulse_effect = {
    "pulseEffect": {
        "interval": 3,  # Every 3 turns
        "ability": "healing_aura",
        "cost_per_pulse": 1
    }
}
```

## 📋 **Core Engine Interface**

### **Primary Methods**
```python
class RuleEngine:
    def simulate_ability(piece, ability, targets) -> GameState
    def validate_action(piece, action, targets) -> ValidationResult
    def resolve_tags(ability, context) -> List[Effect]
    def run_turn_cycle(game_state) -> GameState
    def recharge_phase(game_state) -> GameState
    def preview(action, game_state) -> GameState
```

### **Input/Output Specification**
**Inputs:**
- GameState (pieces, board, effects, points)
- Action request (piece_id, ability_id, target square)
- Player context (current player, turn state)

**Outputs:**
- Updated GameState
- Action log with detailed changes
- Validation result (success/failure with reasons)
- Triggered effects and reactions

## 🔧 **Key Design Decisions**

1. **Architecture**: Hybrid system with optimized standard types + custom patterns
2. **Execution Model**: Validation-first approach with pre-execution checks
3. **State Management**: Immutable state with copy-on-write for undo/replay
4. **UI Integration**: Zero changes to existing editors - engine adapts to current data
5. **Performance**: Caching and lazy evaluation for expensive operations
6. **Extensibility**: Plugin-based tag system for easy addition of new abilities

## 📈 **Success Metrics**

### **Technical Metrics**
- **Performance**: < 100ms response time for complex ability chains
- **Memory**: < 50MB memory usage for standard game states
- **Reliability**: 99.9% uptime with comprehensive error handling
- **Coverage**: 100% test coverage for all tag processors

### **Functional Metrics**
- **Compatibility**: 100% compatibility with existing piece/ability definitions
- **Completeness**: All 28+ canonical tags fully implemented
- **Accuracy**: Perfect rule compliance with field reference specifications
- **Usability**: Zero learning curve for existing UI users

## 🎯 **Next Immediate Steps**

1. **Review and approve this architecture plan**
2. **Start with Phase 1: Core Engine Foundation**
3. **Create the base rule engine structure**
4. **Implement enhanced GameState with validation**
5. **Build the tag resolver base classes**
6. **Set up integration with existing Pydantic schemas**

---

**Note**: This plan ensures we build a high-quality, modular engine that supports all complex gameplay requirements while preserving your excellent UI work. The modular design makes it easy to extend with new abilities and maintain existing functionality.
