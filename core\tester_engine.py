"""
Tester Engine for Adventure Chess
Handles board state, turn simulation, ability triggers, cooldowns, and state tracking
Decoupled from UI for testing and modularity
"""
import logging
from typing import Dict, List, Optional, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime

from utils.simple_bridge import simple_bridge

logger = logging.getLogger(__name__)

class PieceColor(Enum):
    WHITE = "white"
    BLACK = "black"

class InteractionMode(Enum):
    PLACE_PIECE = "place"
    MOVE_PIECE = "move"
    USE_ABILITY = "ability"
    ERASE_PIECE = "erase"

@dataclass
class BoardPosition:
    """Represents a position on the 8x8 board"""
    row: int
    col: int
    
    def __post_init__(self):
        if not (0 <= self.row < 8 and 0 <= self.col < 8):
            raise ValueError(f"Invalid board position: ({self.row}, {self.col})")
    
    def __str__(self):
        return f"({self.row}, {self.col})"
    
    def to_tuple(self) -> Tuple[int, int]:
        return (self.row, self.col)

@dataclass
class PieceInstance:
    """Represents a piece instance on the board"""
    piece_name: str
    color: PieceColor
    position: BoardPosition
    current_points: int = 0
    max_points: int = 0
    cooldowns: Dict[str, int] = field(default_factory=dict)  # ability_name -> turns_remaining
    abilities_used_this_turn: Set[str] = field(default_factory=set)
    piece_data: Dict = field(default_factory=dict)  # Full piece definition
    instance_id: str = ""  # Unique identifier
    
    def __post_init__(self):
        if not self.instance_id:
            # Generate unique ID based on position and timestamp
            timestamp = datetime.now().strftime("%H%M%S%f")
            self.instance_id = f"{self.piece_name}_{self.position.row}{self.position.col}_{timestamp}"

@dataclass
class ActionLog:
    """Represents an action in the game log"""
    turn: int
    timestamp: datetime
    action_type: str
    description: str
    piece_id: Optional[str] = None
    position: Optional[BoardPosition] = None
    details: Dict = field(default_factory=dict)

class TesterEngine:
    """Core engine for piece testing and board simulation"""
    
    def __init__(self):
        self.board: Dict[Tuple[int, int], PieceInstance] = {}  # position -> piece
        self.turn_number: int = 1
        self.action_log: List[ActionLog] = []
        self.selected_piece_id: Optional[str] = None
        self.interaction_mode: InteractionMode = InteractionMode.PLACE_PIECE
        self.auto_turn_enabled: bool = False
        
        # State tracking
        self.piece_instances: Dict[str, PieceInstance] = {}  # instance_id -> piece
        self.graveyard: List[PieceInstance] = []  # Destroyed pieces
        
        logger.info("Tester engine initialized")
    
    def reset_board(self):
        """Reset the board to initial state"""
        self.board.clear()
        self.piece_instances.clear()
        self.graveyard.clear()
        self.turn_number = 1
        self.action_log.clear()
        self.selected_piece_id = None
        
        self._log_action("system", "Board reset to initial state")
        logger.info("Board reset")
    
    def place_piece(self, piece_name: str, color: PieceColor, position: BoardPosition) -> Tuple[bool, str]:
        """
        Place a piece on the board
        Returns: (success, message)
        """
        pos_tuple = position.to_tuple()
        
        # Check if position is occupied
        if pos_tuple in self.board:
            return False, f"Position {position} is already occupied"
        
        # Load piece data
        piece_data, error = simple_bridge.load_piece_for_ui(piece_name)
        if error or piece_data is None:
            return False, f"Cannot load piece '{piece_name}': {error}"
        
        # Create piece instance
        max_points = piece_data.get('maxPoints', 0)
        starting_points = piece_data.get('startingPoints', max_points)
        
        piece_instance = PieceInstance(
            piece_name=piece_name,
            color=color,
            position=position,
            current_points=starting_points,
            max_points=max_points,
            piece_data=piece_data
        )
        
        # Place on board
        self.board[pos_tuple] = piece_instance
        self.piece_instances[piece_instance.instance_id] = piece_instance
        
        self._log_action(
            "place",
            f"Placed {color.value} {piece_name} at {position}",
            piece_instance.instance_id,
            position,
            {"piece_name": piece_name, "color": color.value}
        )
        
        logger.debug(f"Placed piece: {piece_name} ({color.value}) at {position}")
        return True, f"Placed {piece_name} at {position}"
    
    def move_piece(self, from_pos: BoardPosition, to_pos: BoardPosition) -> Tuple[bool, str]:
        """
        Move a piece from one position to another
        Returns: (success, message)
        """
        from_tuple = from_pos.to_tuple()
        to_tuple = to_pos.to_tuple()
        
        # Check if source position has a piece
        if from_tuple not in self.board:
            return False, f"No piece at {from_pos}"
        
        # Check if destination is occupied
        if to_tuple in self.board:
            return False, f"Position {to_pos} is already occupied"
        
        piece = self.board[from_tuple]
        
        # Validate movement (basic check - can be extended)
        if not self._is_valid_move(piece, from_pos, to_pos):
            return False, f"Invalid move for {piece.piece_name}"
        
        # Perform the move
        del self.board[from_tuple]
        piece.position = to_pos
        self.board[to_tuple] = piece
        
        self._log_action(
            "move",
            f"Moved {piece.color.value} {piece.piece_name} from {from_pos} to {to_pos}",
            piece.instance_id,
            to_pos,
            {"from": from_pos.to_tuple(), "to": to_pos.to_tuple()}
        )
        
        logger.debug(f"Moved piece: {piece.piece_name} from {from_pos} to {to_pos}")
        return True, f"Moved {piece.piece_name} to {to_pos}"
    
    def remove_piece(self, position: BoardPosition) -> Tuple[bool, str]:
        """
        Remove a piece from the board
        Returns: (success, message)
        """
        pos_tuple = position.to_tuple()
        
        if pos_tuple not in self.board:
            return False, f"No piece at {position}"
        
        piece = self.board[pos_tuple]
        del self.board[pos_tuple]
        del self.piece_instances[piece.instance_id]
        
        self._log_action(
            "remove",
            f"Removed {piece.color.value} {piece.piece_name} from {position}",
            piece.instance_id,
            position,
            {"piece_name": piece.piece_name, "color": piece.color.value}
        )
        
        logger.debug(f"Removed piece: {piece.piece_name} from {position}")
        return True, f"Removed {piece.piece_name} from {position}"
    
    def use_ability(self, piece_id: str, ability_name: str, target_pos: Optional[BoardPosition] = None) -> Tuple[bool, str]:
        """
        Use a piece's ability
        Returns: (success, message)
        """
        if piece_id not in self.piece_instances:
            return False, f"Piece not found: {piece_id}"
        
        piece = self.piece_instances[piece_id]
        
        # Load piece abilities
        abilities = []
        ability_refs = piece.piece_data.get('abilities', [])
        for ability_ref in ability_refs:
            if isinstance(ability_ref, str):
                ability_data, error = simple_bridge.load_ability_for_ui(ability_ref)
                if ability_data and not error:
                    abilities.append(ability_data)
                else:
                    logger.warning(f"Could not load ability {ability_ref}: {error}")
        
        # Find the requested ability
        ability_data = None
        for ability in abilities:
            if ability.get('name') == ability_name:
                ability_data = ability
                break
        
        if not ability_data:
            return False, f"Ability '{ability_name}' not found for {piece.piece_name}"
        
        # Check if ability can be used
        can_use, reason = self._can_use_ability(piece, ability_data)
        if not can_use:
            return False, reason
        
        # Execute the ability
        success, result_message = self._execute_ability(piece, ability_data, target_pos)
        
        if success:
            # Deduct cost and apply cooldown
            cost = ability_data.get('cost', 0)
            piece.current_points = max(0, piece.current_points - cost)
            piece.abilities_used_this_turn.add(ability_name)
            
            # Apply delay as cooldown if specified
            delay = ability_data.get('delay', 0)
            if delay > 0:
                piece.cooldowns[ability_name] = delay
            
            self._log_action(
                "ability",
                f"{piece.color.value} {piece.piece_name} used {ability_name}: {result_message}",
                piece.instance_id,
                piece.position,
                {
                    "ability_name": ability_name,
                    "cost": cost,
                    "target": target_pos.to_tuple() if target_pos else None
                }
            )
            
            logger.info(f"Ability used: {piece.piece_name} -> {ability_name}")
        
        return success, result_message
    
    def next_turn(self) -> str:
        """
        Advance to the next turn and update all piece states
        Returns: summary message
        """
        self.turn_number += 1
        
        # Process all pieces for turn-based effects
        recharge_summary = []
        cooldown_summary = []
        
        for piece in self.piece_instances.values():
            # Clear abilities used this turn
            piece.abilities_used_this_turn.clear()
            
            # Process recharge
            recharge_gained = self._process_piece_recharge(piece)
            if recharge_gained > 0:
                recharge_summary.append(f"{piece.piece_name}: +{recharge_gained}")
            
            # Process cooldowns
            expired_cooldowns = []
            for ability_name, turns_remaining in list(piece.cooldowns.items()):
                new_remaining = turns_remaining - 1
                if new_remaining <= 0:
                    del piece.cooldowns[ability_name]
                    expired_cooldowns.append(ability_name)
                else:
                    piece.cooldowns[ability_name] = new_remaining
            
            if expired_cooldowns:
                cooldown_summary.append(f"{piece.piece_name}: {', '.join(expired_cooldowns)} ready")
        
        # Create turn summary
        summary_parts = [f"Turn {self.turn_number}"]
        if recharge_summary:
            summary_parts.append(f"Recharge: {'; '.join(recharge_summary)}")
        if cooldown_summary:
            summary_parts.append(f"Cooldowns expired: {'; '.join(cooldown_summary)}")
        
        summary = " | ".join(summary_parts)
        
        self._log_action("turn", summary)
        logger.info(f"Advanced to turn {self.turn_number}")
        
        return summary
    
    def get_piece_at(self, position: BoardPosition) -> Optional[PieceInstance]:
        """Get the piece at a specific position"""
        return self.board.get(position.to_tuple())
    
    def get_valid_moves(self, piece: PieceInstance) -> List[BoardPosition]:
        """Get all valid moves for a piece"""
        valid_moves = []
        
        for row in range(8):
            for col in range(8):
                target_pos = BoardPosition(row, col)
                if target_pos.to_tuple() != piece.position.to_tuple():
                    if self._is_valid_move(piece, piece.position, target_pos):
                        valid_moves.append(target_pos)
        
        return valid_moves
    
    def get_ability_targets(self, piece: PieceInstance, ability_data: Dict) -> List[BoardPosition]:
        """Get all valid targets for an ability"""
        targets = []
        
        # Check if ability has range mask
        if 'rangeMask' in ability_data and 'piecePosition' in ability_data:
            range_mask = ability_data['rangeMask']
            piece_pos = ability_data['piecePosition']
            
            # Calculate offset from piece position in ability to actual piece position
            offset_row = piece.position.row - piece_pos[0]
            offset_col = piece.position.col - piece_pos[1]
            
            for row in range(8):
                for col in range(8):
                    if range_mask[row][col]:
                        # Apply offset to get actual board position
                        actual_row = row + offset_row
                        actual_col = col + offset_col
                        
                        if 0 <= actual_row < 8 and 0 <= actual_col < 8:
                            targets.append(BoardPosition(actual_row, actual_col))
        
        return targets
    
    def get_board_state(self) -> Dict:
        """Get complete board state for saving/loading"""
        state = {
            'version': '1.0.0',
            'turn_number': self.turn_number,
            'pieces': [],
            'action_log': []
        }
        
        # Serialize pieces
        for piece in self.piece_instances.values():
            piece_state = {
                'instance_id': piece.instance_id,
                'piece_name': piece.piece_name,
                'color': piece.color.value,
                'position': piece.position.to_tuple(),
                'current_points': piece.current_points,
                'max_points': piece.max_points,
                'cooldowns': piece.cooldowns,
                'abilities_used_this_turn': list(piece.abilities_used_this_turn)
            }
            state['pieces'].append(piece_state)
        
        # Serialize action log (last 100 entries)
        for action in self.action_log[-100:]:
            action_state = {
                'turn': action.turn,
                'timestamp': action.timestamp.isoformat(),
                'action_type': action.action_type,
                'description': action.description,
                'piece_id': action.piece_id,
                'position': action.position.to_tuple() if action.position else None,
                'details': action.details
            }
            state['action_log'].append(action_state)
        
        return state
    
    def load_board_state(self, state: Dict) -> Tuple[bool, str]:
        """
        Load board state from saved data
        Returns: (success, message)
        """
        try:
            # Clear current state
            self.reset_board()
            
            # Load basic state
            self.turn_number = state.get('turn_number', 1)
            
            # Load pieces
            for piece_state in state.get('pieces', []):
                # Load piece data
                piece_data, error = simple_bridge.load_piece_for_ui(piece_state['piece_name'])
                if error or piece_data is None:
                    logger.warning(f"Could not load piece data for {piece_state['piece_name']}: {error}")
                    continue
                
                # Create piece instance
                position = BoardPosition(piece_state['position'][0], piece_state['position'][1])
                color = PieceColor(piece_state['color'])
                
                piece = PieceInstance(
                    piece_name=piece_state['piece_name'],
                    color=color,
                    position=position,
                    current_points=piece_state['current_points'],
                    max_points=piece_state['max_points'],
                    cooldowns=piece_state.get('cooldowns', {}),
                    abilities_used_this_turn=set(piece_state.get('abilities_used_this_turn', [])),
                    piece_data=piece_data,
                    instance_id=piece_state['instance_id']
                )
                
                self.board[position.to_tuple()] = piece
                self.piece_instances[piece.instance_id] = piece
            
            # Load action log
            for action_state in state.get('action_log', []):
                position = None
                if action_state['position']:
                    position = BoardPosition(action_state['position'][0], action_state['position'][1])
                
                action = ActionLog(
                    turn=action_state['turn'],
                    timestamp=datetime.fromisoformat(action_state['timestamp']),
                    action_type=action_state['action_type'],
                    description=action_state['description'],
                    piece_id=action_state.get('piece_id'),
                    position=position,
                    details=action_state.get('details', {})
                )
                self.action_log.append(action)
            
            logger.info(f"Loaded board state: {len(self.piece_instances)} pieces, turn {self.turn_number}")
            return True, f"Loaded board state with {len(self.piece_instances)} pieces"
            
        except Exception as e:
            error_msg = f"Error loading board state: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def get_statistics(self) -> Dict:
        """Get board statistics"""
        stats = {
            'turn_number': self.turn_number,
            'total_pieces': len(self.piece_instances),
            'white_pieces': len([p for p in self.piece_instances.values() if p.color == PieceColor.WHITE]),
            'black_pieces': len([p for p in self.piece_instances.values() if p.color == PieceColor.BLACK]),
            'pieces_in_graveyard': len(self.graveyard),
            'total_actions': len(self.action_log)
        }
        return stats
    
    # Private helper methods
    
    def _is_valid_move(self, piece: PieceInstance, from_pos: BoardPosition, to_pos: BoardPosition) -> bool:
        """Check if a move is valid for a piece"""
        # Basic validation - can be extended with proper movement rules
        piece_data = piece.piece_data
        movement = piece_data.get('movement', {})
        
        if isinstance(movement, str):
            # Legacy string format
            movement_type = movement
        elif isinstance(movement, dict):
            # New dict format
            movement_type = movement.get('type', 'orthogonal')
        else:
            return False
        
        # Calculate movement delta
        delta_row = abs(to_pos.row - from_pos.row)
        delta_col = abs(to_pos.col - from_pos.col)
        
        # Basic movement validation (simplified)
        if movement_type in ['orthogonal', 'Orthogonal']:
            return (delta_row == 0) != (delta_col == 0)  # XOR - either row or col changes, not both
        elif movement_type in ['diagonal', 'Diagonal']:
            return delta_row == delta_col and delta_row > 0
        elif movement_type in ['any', 'Any']:
            return delta_row > 0 or delta_col > 0
        elif movement_type in ['lShape', 'L-shape']:
            return (delta_row == 2 and delta_col == 1) or (delta_row == 1 and delta_col == 2)
        elif movement_type in ['custom', 'Custom']:
            # Custom pattern validation would go here
            return True  # Simplified for now
        
        return False
    
    def _can_use_ability(self, piece: PieceInstance, ability_data: Dict) -> Tuple[bool, str]:
        """Check if a piece can use an ability"""
        ability_name = ability_data.get('name', 'Unknown')
        
        # Check cost
        cost = ability_data.get('cost', 0)
        if piece.current_points < cost:
            return False, f"Insufficient points: need {cost}, have {piece.current_points}"
        
        # Check cooldown
        if ability_name in piece.cooldowns:
            remaining = piece.cooldowns[ability_name]
            return False, f"Ability on cooldown: {remaining} turns remaining"
        
        # Check if already used this turn (for certain activation modes)
        activation_mode = ability_data.get('activationMode', 'auto')
        if activation_mode == 'click' and ability_name in piece.abilities_used_this_turn:
            return False, "Ability already used this turn"
        
        return True, "Can use ability"
    
    def _execute_ability(self, piece: PieceInstance, ability_data: Dict, target_pos: Optional[BoardPosition]) -> Tuple[bool, str]:
        """Execute an ability (simplified implementation)"""
        ability_name = ability_data.get('name', 'Unknown')
        
        # This is a simplified implementation - real abilities would have complex logic
        tags = ability_data.get('tags', [])
        
        if 'move' in tags:
            if target_pos and target_pos.to_tuple() not in self.board:
                # Move piece to target position (canonical move ability)
                old_pos = piece.position.to_tuple()
                del self.board[old_pos]
                piece.position = target_pos
                self.board[target_pos.to_tuple()] = piece
                return True, f"Moved to {target_pos}"
            else:
                return False, "Invalid move target"
        
        elif 'summon' in tags:
            # Simplified summon - would need more complex logic
            return True, "Summoned (placeholder)"
        
        elif 'capture' in tags:
            if target_pos and target_pos.to_tuple() in self.board:
                target_piece = self.board[target_pos.to_tuple()]
                del self.board[target_pos.to_tuple()]
                del self.piece_instances[target_piece.instance_id]
                self.graveyard.append(target_piece)
                return True, f"Captured {target_piece.piece_name}"
            else:
                return False, "No target to capture"
        
        else:
            # Generic ability execution
            return True, f"Used {ability_name}"
    
    def _process_piece_recharge(self, piece: PieceInstance) -> int:
        """Process recharge for a piece, returns points gained"""
        piece_data = piece.piece_data
        recharge_type = piece_data.get('rechargeType', 'turnRecharge')
        
        if recharge_type == 'turnRecharge':
            turn_points = piece_data.get('turnPoints', 1)
            old_points = piece.current_points
            piece.current_points = min(piece.max_points, piece.current_points + turn_points)
            return piece.current_points - old_points
        
        # Other recharge types would be implemented here
        return 0
    
    def _log_action(self, action_type: str, description: str, piece_id: str = None, 
                   position: BoardPosition = None, details: Dict = None):
        """Log an action to the action log"""
        action = ActionLog(
            turn=self.turn_number,
            timestamp=datetime.now(),
            action_type=action_type,
            description=description,
            piece_id=piece_id,
            position=position,
            details=details or {}
        )
        self.action_log.append(action)
        
        # Keep log size manageable
        if len(self.action_log) > 1000:
            self.action_log = self.action_log[-500:]  # Keep last 500 entries

# Global instance
tester_engine = TesterEngine()