#!/usr/bin/env python3
"""
Capture Tag Resolver for Adventure Chess Rule Engine
Handles piece capture with various capture modes and conditions
"""

from typing import List, Dict, Any
import logging

from schemas import Ability
from schemas.base import Coordinate
from core.tester_engine import PieceColor
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class CaptureTagResolver(BaseTagResolver):
    """
    Resolver for the 'capture' tag
    Handles piece capture with support for different capture modes and conditions
    """
    
    def _get_tag_name(self) -> str:
        return "capture"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.ACTION
    
    def _get_conflicts(self) -> List[str]:
        return ["protect", "cannotDie"]  # Capture conflicts with protection
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        """
        Process capture tag and return capture effects
        
        Args:
            ability: The ability containing the capture tag
            context: ActionContext with game state and action details
            
        Returns:
            List of TagEffect objects for capture
        """
        effects = []
        
        # Extract capture-specific data
        capture_data = extract_tag_data(ability, "capture")
        
        # Get capture parameters
        capture_mode = capture_data.get("mode", "destroy")  # destroy, convert, banish
        capture_target = capture_data.get("target", "enemy")  # enemy, friendly, any
        selective_capture = capture_data.get("selective", False)
        
        # Find valid capture targets
        capture_targets = self._find_capture_targets(context, capture_target, selective_capture)
        
        if not capture_targets:
            return effects
        
        # Create capture effects for each target
        for target_piece_id in capture_targets:
            capture_effect = TagEffect(
                effect_type="capture",
                priority=90,  # High priority, but after movement
                immediate=True,
                parameters={
                    "action": "capture",
                    "mode": capture_mode,
                    "target_piece_id": target_piece_id,
                    "source_piece_id": context.source_piece_id,
                    "capture_position": self._get_capture_position(context, target_piece_id),
                    "selective": selective_capture
                }
            )
            effects.append(capture_effect)
        
        # Add post-capture effects
        if capture_data.get("trigger_on_capture"):
            trigger_effect = TagEffect(
                effect_type="trigger",
                priority=40,
                immediate=False,
                parameters={
                    "trigger_type": "on_capture",
                    "captured_pieces": capture_targets,
                    "abilities": capture_data.get("trigger_abilities", [])
                }
            )
            effects.append(trigger_effect)
        
        # Add revival effects if capture mode supports it
        if capture_mode == "convert":
            for target_piece_id in capture_targets:
                convert_effect = TagEffect(
                    effect_type="convert",
                    priority=30,
                    immediate=True,
                    parameters={
                        "target_piece_id": target_piece_id,
                        "new_owner": context.current_player,
                        "preserve_abilities": capture_data.get("preserve_abilities", True)
                    }
                )
                effects.append(convert_effect)
        
        return effects
    
    def _find_capture_targets(self, context: Any, capture_target: str, selective: bool) -> List[str]:
        """
        Find valid pieces to capture
        
        Args:
            context: ActionContext
            capture_target: Type of pieces to capture (enemy, friendly, any)
            selective: Whether capture is selective
            
        Returns:
            List of piece IDs that can be captured
        """
        targets = []
        
        # Get all target positions
        target_positions = []
        if context.targeting.primary_target:
            target_positions.append(context.targeting.primary_target)
        target_positions.extend(context.targeting.secondary_targets)
        
        # Find pieces at target positions
        for position in target_positions:
            pieces_at_position = context.game_state.get_piece_at(position)
            
            for piece in pieces_at_position:
                if self._can_capture_piece(context, piece, capture_target):
                    targets.append(piece.id)
        
        # If selective capture, only capture specific pieces
        if selective and hasattr(context, 'selected_targets'):
            targets = [pid for pid in targets if pid in context.selected_targets]
        
        return targets
    
    def _can_capture_piece(self, context: Any, target_piece: Any, capture_target: str) -> bool:
        """
        Check if a piece can be captured
        
        Args:
            context: ActionContext
            target_piece: The piece to potentially capture
            capture_target: Type of capture (enemy, friendly, any)
            
        Returns:
            True if piece can be captured
        """
        # Check capture target type
        if capture_target == "enemy" and target_piece.owner == context.current_player:
            return False
        elif capture_target == "friendly" and target_piece.owner != context.current_player:
            return False
        
        # Check if piece is protected
        if hasattr(target_piece, 'status_effects'):
            for effect in target_piece.status_effects:
                if effect.name in ["protected", "cannotDie", "immortal"]:
                    return False
        
        # Check if piece has capture immunity
        if hasattr(target_piece, 'abilities'):
            for ability in target_piece.abilities:
                if isinstance(ability, dict) and "captureImmune" in ability.get('tags', []):
                    return False
        
        # Check if piece is invisible and capture requires visibility
        if hasattr(target_piece, 'is_invisible') and target_piece.is_invisible:
            # Check if capturing piece can see invisible pieces
            source_piece = context.game_state.get_piece(context.source_piece_id)
            if source_piece and not self._can_see_invisible(source_piece):
                return False
        
        return True
    
    def _can_see_invisible(self, piece: Any) -> bool:
        """
        Check if a piece can see invisible pieces
        
        Args:
            piece: The piece checking for visibility
            
        Returns:
            True if piece can see invisible pieces
        """
        if hasattr(piece, 'abilities'):
            for ability in piece.abilities:
                if isinstance(ability, dict) and "truesight" in ability.get('tags', []):
                    return True
        return False
    
    def _get_capture_position(self, context: Any, target_piece_id: str) -> Coordinate:
        """
        Get the position where capture occurs
        
        Args:
            context: ActionContext
            target_piece_id: ID of piece being captured
            
        Returns:
            Coordinate where capture occurs
        """
        target_piece = context.game_state.get_piece(target_piece_id)
        if target_piece and target_piece.position:
            return target_piece.position
        
        # Fallback to primary target position
        return context.targeting.primary_target
    
    def calculate_cost_modifier(self, base_cost: int, ability: Ability, context: Any) -> int:
        """
        Calculate cost modifications for capture
        
        Args:
            base_cost: Base ability cost
            ability: The ability being used
            context: ActionContext
            
        Returns:
            Modified cost
        """
        capture_data = extract_tag_data(ability, "capture")
        
        # Cost per target captured
        cost_per_target = capture_data.get("cost_per_target", 0)
        if cost_per_target > 0:
            target_count = len(context.targeting.all_targets)
            return base_cost + (target_count * cost_per_target)
        
        # Higher cost for friendly capture
        if capture_data.get("target") == "friendly":
            friendly_penalty = capture_data.get("friendly_cost_multiplier", 2.0)
            return int(base_cost * friendly_penalty)
        
        return base_cost
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        """
        Get targeting requirements for capture abilities
        
        Args:
            ability: The ability being analyzed
            
        Returns:
            Dictionary with targeting requirements
        """
        capture_data = extract_tag_data(ability, "capture")
        
        return {
            "requires_target": True,
            "target_type": "piece",
            "min_targets": 1,
            "max_targets": capture_data.get("max_targets", 1),
            "target_must_be_occupied": True,
            "range_required": True,
            "line_of_sight": capture_data.get("requires_los", True),
            "target_filter": capture_data.get("target", "enemy")
        }
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> tuple[bool, str]:
        """
        Validate prerequisites for capture tag
        
        Args:
            ability: The ability being validated
            context: ActionContext
            
        Returns:
            (is_valid, error_message)
        """
        # Call parent validation first
        is_valid, message = super().validate_prerequisites(ability, context)
        if not is_valid:
            return is_valid, message
        
        # Check capture-specific prerequisites
        capture_data = extract_tag_data(ability, "capture")
        
        # Check if there are valid targets
        capture_targets = self._find_capture_targets(
            context, 
            capture_data.get("target", "enemy"), 
            capture_data.get("selective", False)
        )
        
        if not capture_targets:
            return False, "No valid capture targets found"
        
        # Check adjacency requirement for capture
        if capture_data.get("requires_adjacency", False):
            source_piece = context.game_state.get_piece(context.source_piece_id)
            if source_piece:
                for target_id in capture_targets:
                    target_piece = context.game_state.get_piece(target_id)
                    if target_piece:
                        distance = max(
                            abs(source_piece.position.row - target_piece.position.row),
                            abs(source_piece.position.col - target_piece.position.col)
                        )
                        if distance > 1:
                            return False, "Capture requires adjacency"
        
        return True, "Prerequisites satisfied"
