#!/usr/bin/env python3
"""
Range and Pattern Tag Resolvers for Adventure Chess Rule Engine
Handles range modifications and custom patterns
"""

from typing import List, Dict, Any
from schemas import Ability
from schemas.base import Coordinate
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class RangeTagResolver(BaseTagResolver):
    """Resolver for range modifications"""
    
    def _get_tag_name(self) -> str:
        return "range"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.TARGETING
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        range_data = extract_tag_data(ability, "range")
        
        return [TagEffect(
            effect_type="range_modifier",
            priority=175,
            immediate=True,
            parameters={
                "modifier_type": "range",
                "base_range": range_data.get("base", 1),
                "min_range": range_data.get("min", 0),
                "max_range": range_data.get("max", 8),
                "range_bonus": range_data.get("bonus", 0),
                "range_multiplier": range_data.get("multiplier", 1.0)
            }
        )]
    
    def get_range_modifier(self, base_range: int, ability: Ability, context: Any) -> int:
        range_data = extract_tag_data(ability, "range")
        
        # Apply range modifications
        modified_range = base_range + range_data.get("bonus", 0)
        modified_range = int(modified_range * range_data.get("multiplier", 1.0))
        
        # Apply min/max constraints
        min_range = range_data.get("min", 0)
        max_range = range_data.get("max", 8)
        
        return max(min_range, min(max_range, modified_range))
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        range_data = extract_tag_data(ability, "range")
        
        return {
            "range_required": True,
            "min_range": range_data.get("min", 0),
            "max_range": range_data.get("max", 8),
            "base_range": range_data.get("base", 1)
        }


class CustomPatternTagResolver(BaseTagResolver):
    """Resolver for custom movement/targeting patterns"""
    
    def _get_tag_name(self) -> str:
        return "customPattern"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.TARGETING
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        pattern_data = extract_tag_data(ability, "customPattern")
        
        return [TagEffect(
            effect_type="targeting_constraint",
            priority=170,
            immediate=True,
            parameters={
                "constraint_type": "custom_pattern",
                "pattern": pattern_data.get("pattern", []),
                "piece_position": pattern_data.get("piece_position", [3, 3]),
                "pattern_type": pattern_data.get("type", "movement"),
                "relative_positioning": pattern_data.get("relative", True)
            }
        )]
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        pattern_data = extract_tag_data(ability, "customPattern")
        
        return {
            "requires_target": True,
            "custom_pattern": True,
            "pattern": pattern_data.get("pattern", []),
            "piece_position": pattern_data.get("piece_position", [3, 3]),
            "relative_positioning": pattern_data.get("relative", True)
        }
