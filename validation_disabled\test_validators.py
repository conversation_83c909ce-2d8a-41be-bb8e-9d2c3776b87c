"""
Unit tests for validators module
"""
import unittest
from validators import (
    PieceValidator, AbilityValidator, validate_piece_data, validate_ability_data,
    CURRENT_PIECE_VERSION, CURRENT_ABILITY_VERSION
)

class TestPieceValidator(unittest.TestCase):
    """Test piece validation functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.valid_piece = {
            "version": CURRENT_PIECE_VERSION,
            "name": "Test Piece",
            "description": "A test piece",
            "movement": {"type": "orthogonal", "distance": 1},
            "range": 2,
            "maxPoints": 5,
            "startingPoints": 3,
            "turnPoints": 1,
            "rechargeType": "turnRecharge",
            "abilities": []
        }
    
    def test_valid_piece(self):
        """Test validation of a valid piece"""
        is_valid, errors, warnings = validate_piece_data(self.valid_piece)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_missing_name(self):
        """Test validation fails when name is missing"""
        piece = self.valid_piece.copy()
        del piece["name"]
        
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertFalse(is_valid)
        self.assertIn("Piece must have a 'name' field", errors)
    
    def test_empty_name(self):
        """Test validation fails when name is empty"""
        piece = self.valid_piece.copy()
        piece["name"] = ""
        
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertFalse(is_valid)
        self.assertIn("Piece must have a 'name' field", errors)
    
    def test_invalid_movement_type(self):
        """Test validation fails with invalid movement type"""
        piece = self.valid_piece.copy()
        piece["movement"] = {"type": "invalid_type", "distance": 1}
        
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertFalse(is_valid)
        self.assertTrue(any("Invalid movement type" in error for error in errors))
    
    def test_negative_range(self):
        """Test validation fails with negative range"""
        piece = self.valid_piece.copy()
        piece["range"] = -1
        
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertFalse(is_valid)
        self.assertIn("Range must be a non-negative integer", errors)
    
    def test_invalid_recharge_type(self):
        """Test validation fails with invalid recharge type"""
        piece = self.valid_piece.copy()
        piece["rechargeType"] = "invalid_recharge"
        
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertFalse(is_valid)
        self.assertTrue(any("Invalid recharge type" in error for error in errors))
    
    def test_custom_pattern_validation(self):
        """Test custom pattern validation"""
        piece = self.valid_piece.copy()
        piece["movement"] = {"type": "custom"}
        
        # Valid 8x8 pattern
        valid_pattern = [[0 for _ in range(8)] for _ in range(8)]
        piece["customPattern"] = valid_pattern
        piece["customPatternPiecePos"] = [3, 3]
        
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertTrue(is_valid)
        
        # Invalid pattern (wrong size)
        invalid_pattern = [[0 for _ in range(7)] for _ in range(7)]
        piece["customPattern"] = invalid_pattern
        
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertFalse(is_valid)
        self.assertTrue(any("Custom pattern must be 8x8" in error for error in errors))
    
    def test_ability_references_validation(self):
        """Test ability references validation"""
        piece = self.valid_piece.copy()
        
        # Valid string reference
        piece["abilities"] = ["test_ability"]
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertTrue(is_valid)
        
        # Valid dict reference
        piece["abilities"] = [{"abilityFile": "test_ability", "overrides": {"cost": 2}}]
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertTrue(is_valid)
        
        # Invalid reference (empty string)
        piece["abilities"] = [""]
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertFalse(is_valid)
        self.assertTrue(any("cannot be empty" in error for error in errors))
    
    def test_version_handling(self):
        """Test version field handling"""
        # Missing version should generate warning
        piece = self.valid_piece.copy()
        del piece["version"]
        
        is_valid, errors, warnings = validate_piece_data(piece)
        self.assertTrue(is_valid)  # Should still be valid
        self.assertTrue(any("missing version field" in warning for warning in warnings))

class TestAbilityValidator(unittest.TestCase):
    """Test ability validation functionality"""
    
    def setUp(self):
        """Set up test data"""
        self.valid_ability = {
            "version": CURRENT_ABILITY_VERSION,
            "name": "Test Ability",
            "description": "A test ability",
            "cost": 2,
            "delay": 1,
            "activationMode": "click",
            "tags": ["teleport", "range"]
        }
    
    def test_valid_ability(self):
        """Test validation of a valid ability"""
        is_valid, errors, warnings = validate_ability_data(self.valid_ability)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
    
    def test_missing_name(self):
        """Test validation fails when name is missing"""
        ability = self.valid_ability.copy()
        del ability["name"]
        
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertFalse(is_valid)
        self.assertIn("Ability must have a name", errors)
    
    def test_invalid_activation_mode(self):
        """Test validation fails with invalid activation mode"""
        ability = self.valid_ability.copy()
        ability["activationMode"] = "invalid_mode"
        
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertFalse(is_valid)
        self.assertTrue(any("Invalid activation mode" in error for error in errors))
    
    def test_negative_cost(self):
        """Test validation fails with negative cost"""
        ability = self.valid_ability.copy()
        ability["cost"] = -1
        
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertFalse(is_valid)
        self.assertIn("Cost must be a non-negative number", errors)
    
    def test_range_mask_validation(self):
        """Test range mask validation"""
        ability = self.valid_ability.copy()
        
        # Valid 8x8 boolean mask
        valid_mask = [[True if i == j else False for j in range(8)] for i in range(8)]
        ability["rangeMask"] = valid_mask
        
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertTrue(is_valid)
        
        # Invalid mask (wrong size)
        invalid_mask = [[True for _ in range(7)] for _ in range(7)]
        ability["rangeMask"] = invalid_mask
        
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertFalse(is_valid)
        self.assertTrue(any("Range mask must be 8x8" in error for error in errors))
        
        # Invalid mask (non-boolean values)
        invalid_mask = [[1 if i == j else 0 for j in range(8)] for i in range(8)]
        ability["rangeMask"] = invalid_mask
        
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertFalse(is_valid)
        self.assertTrue(any("must be boolean" in error for error in errors))
    
    def test_piece_position_validation(self):
        """Test piece position validation"""
        ability = self.valid_ability.copy()
        
        # Valid position
        ability["piecePosition"] = [3, 4]
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertTrue(is_valid)
        
        # Invalid position (out of bounds)
        ability["piecePosition"] = [8, 8]
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertFalse(is_valid)
        self.assertTrue(any("must be within 8x8 grid" in error for error in errors))
        
        # Invalid position format
        ability["piecePosition"] = [3]
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertFalse(is_valid)
        self.assertTrue(any("must be [row, col]" in error for error in errors))
    
    def test_summon_list_validation(self):
        """Test summon list validation"""
        ability = self.valid_ability.copy()
        
        # Valid summon list
        ability["summonList"] = [
            {"piece": "Pawn", "cost": 1},
            {"piece": "Knight"}
        ]
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertTrue(is_valid)
        
        # Invalid summon list (missing piece field)
        ability["summonList"] = [{"cost": 1}]
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertFalse(is_valid)
        self.assertTrue(any("must have 'piece' field" in error for error in errors))
        
        # Invalid cost
        ability["summonList"] = [{"piece": "Pawn", "cost": -1}]
        is_valid, errors, warnings = validate_ability_data(ability)
        self.assertFalse(is_valid)
        self.assertTrue(any("must be non-negative" in error for error in errors))

if __name__ == '__main__':
    unittest.main()