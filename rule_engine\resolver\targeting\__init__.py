"""
Targeting Tag Resolvers for Adventure Chess Rule Engine
Processors for targeting and range-related tags

Note: Individual resolvers are imported directly by the base registry
to avoid circular import issues.
"""

# No bulk imports to avoid circular dependencies
# Individual resolvers are imported directly by base.py

__all__ = [
    "AdjacencyTagResolver",
    "OrthogonalTagResolver",
    "DiagonalTagResolver",
    "AnyDirectionTagResolver",
    "LShapeTagResolver",
    "TeleportTagResolver",
    "AreaOfEffectTagResolver",
    "LineOfSightTagResolver",
    "RangeTagResolver",
    "CustomPatternTagResolver"
]
