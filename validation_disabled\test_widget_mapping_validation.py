"""
Widget Mapping Validation Test
Validates that all UI widgets in editors have corresponding field mappings
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.editor_data_interface import EditorDataInterface

def test_widget_mappings():
    """Test that all widgets have proper field mappings"""
    
    # Extract widget names from field mappings
    ability_widgets = set()
    piece_widgets = set()
    
    for mapping in EditorDataInterface.ABILITY_FIELD_MAPPINGS:
        widget_name = mapping[0]
        ability_widgets.add(widget_name)
    
    for mapping in EditorDataInterface.PIECE_FIELD_MAPPINGS:
        widget_name = mapping[0]
        piece_widgets.add(widget_name)
    
    print("=== ABILITY EDITOR WIDGET MAPPINGS ===")
    print(f"Total mapped widgets: {len(ability_widgets)}")
    
    # Known ability editor widgets from analysis
    ability_editor_widgets = {
        'name_edit', 'description_edit', 'cost_spin', 'auto_cost_check', 'activation_combo',
        'range_friendly_only_check', 'range_enemy_only_check', 'range_include_start_check', 
        'range_include_self_check', 'area_size_spin', 'area_shape_combo',
        'summon_max_spin', 'capture_target_combo', 'adjacency_distance_spin',
        'no_turn_cost_limit_spin', 'share_space_max_spin', 'share_space_same_type_check',
        'share_space_friendly_check', 'share_space_enemy_check', 'share_space_any_check',
        'displace_direction_combo', 'displace_distance_spin', 'displace_custom_check',
        'immobilize_duration_check', 'immobilize_duration_spin', 'obstacle_type_combo',
        'remove_obstacle_type_combo', 'duplicate_limit_check', 'duplicate_limit_spin',
        'reaction_uses_action_check', 'buff_duration_spin', 'buff_add_ability_check',
        'buff_movement_pattern_check', 'debuff_duration_spin', 'debuff_prevent_ability_check',
        'debuff_prevent_los_check', 'debuff_movement_pattern_check',
        'invisible_reveal_move_check', 'invisible_reveal_move_spin',
        'invisible_reveal_capture_check', 'invisible_reveal_capture_spin',
        'invisible_reveal_action_check', 'invisible_reveal_action_spin',
        'invisible_reveal_los_check', 'trap_capture_check', 'trap_immobilize_check',
        'trap_immobilize_spin', 'trap_teleport_check', 'trap_add_ability_check',
        'revival_max_pieces_spin', 'revival_sacrifice_check', 'revival_max_cost_spin',
        'revival_with_points_check', 'revival_points_spin', 'revival_starting_check',
        'revival_within_turn_spin', 'los_ignore_enemy_check', 'los_ignore_all_check',
        'delay_turn_check', 'delay_turn_spin', 'delay_action_check', 'delay_action_spin',
        'pulse_interval_spin', 'fog_vision_combo', 'fog_radius_spin', 'fog_duration_spin',
        'fog_cost_spin', 'carry_range_spin', 'carry_drop_on_death_check', 'carry_drop_mode_combo',
        'carry_drop_range_spin', 'carry_drop_can_capture_check', 'carry_share_abilities_check',
        'carry_starting_piece_check'
    }
    
    # Check for missing mappings
    missing_ability_mappings = ability_editor_widgets - ability_widgets
    extra_ability_mappings = ability_widgets - ability_editor_widgets
    
    print(f"Missing mappings: {len(missing_ability_mappings)}")
    if missing_ability_mappings:
        for widget in sorted(missing_ability_mappings):
            print(f"  - {widget}")
    
    print(f"Extra mappings: {len(extra_ability_mappings)}")
    if extra_ability_mappings:
        for widget in sorted(extra_ability_mappings):
            print(f"  + {widget}")
    
    print("\n=== PIECE EDITOR WIDGET MAPPINGS ===")
    print(f"Total mapped widgets: {len(piece_widgets)}")
    
    # Known piece editor widgets from analysis
    piece_editor_widgets = {
        'name_edit', 'desc_edit', 'role_combo', 'can_castle_check', 
        'track_starting_position_check', 'color_directional_check',
        'black_icon_combo', 'white_icon_combo', 'enable_recharge_check',
        'max_points_spin', 'starting_points_spin', 'recharge_combo',
        'turn_points_spin', 'committed_recharge_spin'
    }
    
    # Check for missing mappings
    missing_piece_mappings = piece_editor_widgets - piece_widgets
    extra_piece_mappings = piece_widgets - piece_editor_widgets
    
    print(f"Missing mappings: {len(missing_piece_mappings)}")
    if missing_piece_mappings:
        for widget in sorted(missing_piece_mappings):
            print(f"  - {widget}")
    
    print(f"Extra mappings: {len(extra_piece_mappings)}")
    if extra_piece_mappings:
        for widget in sorted(extra_piece_mappings):
            print(f"  + {widget}")
    
    return len(missing_ability_mappings) == 0 and len(missing_piece_mappings) == 0

if __name__ == "__main__":
    success = test_widget_mappings()
    if success:
        print("\n✅ All widgets have proper field mappings!")
    else:
        print("\n❌ Some widgets are missing field mappings!")
    sys.exit(0 if success else 1)
