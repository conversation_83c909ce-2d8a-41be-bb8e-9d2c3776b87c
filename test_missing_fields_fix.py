#!/usr/bin/env python3
"""
Test Missing Fields Fix in SimpleBridge
Specifically tests the can capture radio buttons and other previously missing fields
"""

import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_missing_fields_fix():
    """Test that previously missing fields are now properly saved and loaded"""
    
    print("=== MISSING FIELDS FIX TEST ===")
    
    # Test 1: Test can capture checkbox
    print("\n1. Testing Can Capture Checkbox...")

    try:
        from PyQt6.QtWidgets import QApplication
        import sys

        # Create minimal QApplication if needed
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)

        from editors.piece_editor import PieceEditorWindow
        from utils.simple_bridge import simple_bridge

        # Create piece editor instance
        editor = PieceEditorWindow()

        # Test setting can capture to False (unchecked)
        editor.can_capture_check.setChecked(False)

        # Collect data
        piece_data = simple_bridge.get_piece_data_from_ui(editor)

        # Verify can capture is False
        if piece_data.get('canCapture') == False:
            print("✓ Can capture unchecked (False) properly collected")
        else:
            print(f"❌ Can capture unchecked failed: got {piece_data.get('canCapture')}")
            return False

        # Test setting can capture to True (checked)
        editor.can_capture_check.setChecked(True)

        # Collect data again
        piece_data = simple_bridge.get_piece_data_from_ui(editor)

        # Verify can capture is True
        if piece_data.get('canCapture') == True:
            print("✓ Can capture checked (True) properly collected")
        else:
            print(f"❌ Can capture checked failed: got {piece_data.get('canCapture')}")
            return False

    except Exception as e:
        print(f"❌ Can capture test failed: {e}")
        return False
    
    # Test 2: Test color directional checkbox
    print("\n2. Testing Color Directional Checkbox...")
    
    try:
        # Test setting color directional to True
        editor.color_directional_check.setChecked(True)
        
        # Collect data
        piece_data = simple_bridge.get_piece_data_from_ui(editor)
        
        # Verify color directional is True
        if piece_data.get('colorDirectional') == True:
            print("✓ Color directional True properly collected")
        else:
            print(f"❌ Color directional True failed: got {piece_data.get('colorDirectional')}")
            return False
        
        # Test setting color directional to False
        editor.color_directional_check.setChecked(False)
        
        # Collect data again
        piece_data = simple_bridge.get_piece_data_from_ui(editor)
        
        # Verify color directional is False
        if piece_data.get('colorDirectional') == False:
            print("✓ Color directional False properly collected")
        else:
            print(f"❌ Color directional False failed: got {piece_data.get('colorDirectional')}")
            return False
            
    except Exception as e:
        print(f"❌ Color directional test failed: {e}")
        return False
    
    # Test 3: Test ability checkboxes
    print("\n3. Testing Ability Checkboxes...")
    
    try:
        # Setup ability selector if not already done
        if hasattr(editor, 'setup_inline_ability_selector'):
            editor.setup_inline_ability_selector()
        
        # Check if we have any ability checkboxes
        if hasattr(editor, 'ability_checkboxes') and editor.ability_checkboxes:
            # Select first few abilities
            ability_names = list(editor.ability_checkboxes.keys())[:3]
            for ability_name in ability_names:
                editor.ability_checkboxes[ability_name].setChecked(True)
            
            # Trigger the change handler
            editor.on_ability_checkbox_changed()
            
            # Collect data
            piece_data = simple_bridge.get_piece_data_from_ui(editor)
            
            # Verify abilities are collected
            collected_abilities = piece_data.get('abilities', [])
            if len(collected_abilities) >= 3:
                print(f"✓ Ability checkboxes properly collected: {len(collected_abilities)} abilities")
            else:
                print(f"❌ Ability checkboxes failed: got {len(collected_abilities)} abilities")
                return False
        else:
            print("⚠️ No ability checkboxes found to test")
            
    except Exception as e:
        print(f"❌ Ability checkboxes test failed: {e}")
        return False
    
    # Test 4: Test data collection consistency
    print("\n4. Testing Data Collection Consistency...")

    try:
        # Set up test data
        editor.name_edit.setText("TestPiece")
        editor.desc_edit.setPlainText("Test piece for missing fields")
        editor.can_capture_check.setChecked(False)  # Set to False
        editor.color_directional_check.setChecked(True)  # Set to True

        # Collect data multiple times to ensure consistency
        piece_data1 = simple_bridge.get_piece_data_from_ui(editor)
        piece_data2 = simple_bridge.get_piece_data_from_ui(editor)

        # Verify consistency
        if (piece_data1.get('canCapture') == piece_data2.get('canCapture') == False and
            piece_data1.get('colorDirectional') == piece_data2.get('colorDirectional') == True and
            piece_data1.get('name') == piece_data2.get('name') == "TestPiece"):
            print("✓ Data collection consistency verified")
        else:
            print(f"❌ Data collection inconsistency:")
            print(f"  canCapture: {piece_data1.get('canCapture')} vs {piece_data2.get('canCapture')}")
            print(f"  colorDirectional: {piece_data1.get('colorDirectional')} vs {piece_data2.get('colorDirectional')}")
            print(f"  name: {piece_data1.get('name')} vs {piece_data2.get('name')}")
            return False

    except Exception as e:
        print(f"❌ Data collection consistency test failed: {e}")
        return False
    
    # Test 5: Test UI population from loaded data
    print("\n5. Testing UI Population from Loaded Data...")
    
    try:
        # Create test data
        test_data = {
            'name': 'LoadTestPiece',
            'description': 'Test loading data into UI',
            'canCapture': False,
            'colorDirectional': True,
            'role': 'Supporter',
            'canCastle': True,
            'movement': {'type': 'diagonal', 'pattern': None, 'piecePosition': [3, 3]},
            'abilities': []
        }
        
        # Load data into UI
        editor.set_widget_values_from_data(test_data)
        
        # Verify UI state
        if (not editor.can_capture_check.isChecked() and
            editor.color_directional_check.isChecked() and
            editor.name_edit.text() == 'LoadTestPiece'):
            print("✓ UI population from loaded data successful")
        else:
            print(f"❌ UI population failed:")
            print(f"  can_capture: {editor.can_capture_check.isChecked()} (expected False)")
            print(f"  color_directional: {editor.color_directional_check.isChecked()} (expected True)")
            print(f"  name: {editor.name_edit.text()} (expected LoadTestPiece)")
            return False
            
    except Exception as e:
        print(f"❌ UI population test failed: {e}")
        return False
    
    print("\n✅ ALL MISSING FIELDS TESTS PASSED!")
    print("\nFixed Fields Summary:")
    print("  ✓ Can Capture checkbox (True/False)")
    print("  ✓ Color Directional checkbox")
    print("  ✓ Ability checkboxes collection")
    print("  ✓ Complete save/load cycle")
    print("  ✓ UI population from loaded data")
    print("\n🎯 SimpleBridge now captures all editor fields correctly!")
    print("📝 Can Capture is now a single checkbox for better UI consistency!")
    
    return True

if __name__ == "__main__":
    success = test_missing_fields_fix()
    sys.exit(0 if success else 1)
