"""
Action Tag Resolvers for Adventure Chess Rule Engine
Processors for direct game actions (move, capture, summon, etc.)

Note: Individual resolvers are imported directly by the base registry
to avoid circular import issues.
"""

# No bulk imports to avoid circular dependencies
# Individual resolvers are imported directly by base.py

__all__ = [
    "MoveTagResolver",
    "CaptureTagResolver",
    "SummonTagResolver",
    "RevivalTagResolver",
    "CarryPieceTagResolver",
    "SwapPlacesTagResolver",
    "DisplacePieceTagResolver",
    "ImmobilizeTagResolver",
    "ConvertPieceTagResolver",
    "DuplicateTagResolver",
    "BuffPieceTagResolver",
    "DebuffPieceTagResolver",
    "AddObstacleTagResolver",
    "RemoveObstacleTagResolver",
    "TrapTileTagResolver"
]
