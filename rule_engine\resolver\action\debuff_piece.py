#!/usr/bin/env python3
"""Debuff Piece Tag Resolver"""
from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect

class DebuffPieceTagResolver(BaseTagResolver):
    def _get_tag_name(self) -> str: return "debuffPiece"
    def _get_category(self) -> TagCategory: return TagCategory.ACTION
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(effect_type="debuff_piece", priority=45, immediate=True, parameters={"action": "debuff_piece"})]
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        return {"requires_target": True, "target_type": "piece", "min_targets": 1, "max_targets": 5}
