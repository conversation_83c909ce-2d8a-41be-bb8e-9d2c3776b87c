#!/usr/bin/env python3
"""
Core Rule Engine for Adventure Chess
Handles validation, processing, and execution of all game actions
"""

from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
import copy
import logging

# Import from schemas for data structures
from schemas.base import Coordinate
from schemas import Piece, Ability, pydantic_data_manager
from core.tester_engine import PieceColor

from .game_state import EnhancedGameState, EnhancedPieceState, ActionContext
from .validator import ValidationEngine, ValidationResult, ConflictResolver
from .events import EventSystem, GameEvent
from .resolver.base import BaseTagResolver, TagRegistry
from .movement import MovementValidator, MovementPatternProcessor, PathfindingEngine, MovementModifierEngine


@dataclass
class ActionRequest:
    """Represents a request to perform an action"""
    piece_id: str
    action_type: str  # "move", "ability", "end_turn"
    ability_name: Optional[str] = None
    target_position: Optional[Coordinate] = None
    target_pieces: Optional[List[str]] = None
    parameters: Optional[Dict[str, Any]] = None

    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}


@dataclass
class ActionResult:
    """Result of processing an action"""
    success: bool
    message: str
    state_changes: Optional[List[Dict[str, Any]]] = None
    triggered_effects: Optional[List[Dict[str, Any]]] = None
    validation_errors: Optional[List[str]] = None

    def __post_init__(self):
        if self.state_changes is None:
            self.state_changes = []
        if self.triggered_effects is None:
            self.triggered_effects = []
        if self.validation_errors is None:
            self.validation_errors = []


class RuleEngine:
    """
    Core rule engine for Adventure Chess
    Provides schema-driven, modular processing of all game mechanics
    """
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.validation_engine = ValidationEngine()
        self.conflict_resolver = ConflictResolver()
        self.event_system = EventSystem()
        self.tag_registry = TagRegistry()

        # Movement system components
        self.movement_validator = MovementValidator()
        self.movement_processor = MovementPatternProcessor()
        self.pathfinding_engine = PathfindingEngine()
        self.movement_modifier_engine = MovementModifierEngine()

        # Advanced game mechanics systems
        from .status_effects import StatusEffectManager
        from .turn_manager import TurnManager, create_turn_manager_with_handlers
        from .interaction_system import InteractionProcessor

        self.status_effect_manager = StatusEffectManager()
        self.turn_manager = create_turn_manager_with_handlers(
            self.status_effect_manager,
            self.event_system
        )
        self.interaction_processor = InteractionProcessor()

        # Initialize tag processors
        self._initialize_tag_processors()

        self.logger.info("RuleEngine initialized with advanced game mechanics systems")
    
    def _initialize_tag_processors(self):
        """Initialize all tag processors"""
        # This will be expanded as we implement tag processors
        # For now, just register the base structure
        self.tag_registry.initialize_processors()
        self.logger.info(f"Initialized {len(self.tag_registry.processors)} tag processors")
    
    def simulate_ability(self, game_state: EnhancedGameState, piece_id: str, 
                        ability_name: str, targets: List[Coordinate]) -> EnhancedGameState:
        """
        Simulate an ability execution and return the resulting game state
        This is non-destructive - original state is unchanged
        """
        # Create a deep copy for simulation
        simulated_state = game_state.create_snapshot()
        
        # Create action request
        action_request = ActionRequest(
            piece_id=piece_id,
            action_type="ability",
            ability_name=ability_name,
            target_position=targets[0] if targets else None,
            parameters={"all_targets": targets}
        )
        
        # Process the action on the simulated state
        result = self._process_action_internal(simulated_state, action_request, simulate=True)
        
        if result.success:
            return simulated_state
        else:
            # Return original state if simulation failed
            return game_state
    
    def validate_action(self, game_state: EnhancedGameState, piece_id: str, 
                       action_type: str, **kwargs) -> ValidationResult:
        """
        Validate if an action can be performed without executing it
        Supports flexible tag validation as requested by user
        """
        action_request = ActionRequest(
            piece_id=piece_id,
            action_type=action_type,
            ability_name=kwargs.get('ability_name'),
            target_position=kwargs.get('target_position'),
            target_pieces=kwargs.get('target_pieces'),
            parameters=kwargs.get('parameters', {})
        )
        
        return self.validation_engine.validate_action(game_state, action_request)
    
    def resolve_tags(self, ability: Ability, context: ActionContext) -> List[Dict[str, Any]]:
        """
        Resolve all tags in an ability and return list of effects
        Supports multiple tags as requested by user
        """
        effects = []
        
        # Process each tag in the ability
        for tag_name in ability.tags:
            processor = self.tag_registry.get_processor(tag_name)
            if processor:
                tag_effects = processor.process(ability, context)
                effects.extend(tag_effects)
            else:
                self.logger.warning(f"No processor found for tag: {tag_name}")
        
        # Resolve conflicts between effects
        resolved_effects = self.conflict_resolver.resolve_effect_conflicts(effects, context)
        
        return resolved_effects
    
    def run_turn_cycle(self, game_state: EnhancedGameState) -> EnhancedGameState:
        """
        Execute a complete turn cycle including recharge, effects, and cleanup
        """
        # End current turn if active
        if self.turn_manager.current_turn:
            self.turn_manager.end_turn()

        # Start new turn
        next_player = PieceColor.BLACK if game_state.current_player == PieceColor.WHITE else PieceColor.WHITE
        self.turn_manager.start_turn(next_player)

        # Process turn start effects
        self._process_turn_start_effects(game_state)

        # Process recharge systems
        self.recharge_phase(game_state)

        # Process pending effects and interactions
        self._process_pending_effects(game_state)
        self.interaction_processor.process_interactions(game_state)

        # Process pulse effects
        self._process_pulse_effects(game_state)

        # Clean up expired effects
        self._cleanup_expired_effects(game_state)

        # Advance turn counter
        game_state.next_turn()

        return game_state
    
    def recharge_phase(self, game_state: EnhancedGameState) -> EnhancedGameState:
        """
        Process recharge systems for all pieces
        """
        for piece in game_state.get_pieces_by_owner(game_state.current_player):
            self._process_piece_recharge(game_state, piece)
        
        return game_state
    
    def preview(self, game_state: EnhancedGameState, action_request: ActionRequest) -> EnhancedGameState:
        """
        Preview an action without applying it to the actual game state
        Returns a simulated state showing the results
        """
        return self.simulate_ability(
            game_state, 
            action_request.piece_id,
            action_request.ability_name or "move",
            [action_request.target_position] if action_request.target_position else []
        )
    
    def process_action(self, game_state: EnhancedGameState, action_request: ActionRequest) -> ActionResult:
        """
        Process a game action and return the result
        This is the main entry point for action processing
        """
        return self._process_action_internal(game_state, action_request, simulate=False)
    
    def _process_action_internal(self, game_state: EnhancedGameState, 
                               action_request: ActionRequest, simulate: bool = False) -> ActionResult:
        """
        Internal action processing with simulation support
        """
        try:
            # Validate the action
            validation_result = self.validation_engine.validate_action(game_state, action_request)
            if not validation_result.is_valid:
                return ActionResult(
                    success=False,
                    message="Action validation failed",
                    validation_errors=validation_result.errors
                )
            
            # Create action context
            context = ActionContext(
                game_state=game_state,
                action_request=action_request,
                current_player=game_state.current_player,
                turn_counter=game_state.turn_counter,
                is_simulation=simulate
            )
            
            # Process the action based on type
            if action_request.action_type == "ability":
                return self._process_ability_action(context)
            elif action_request.action_type == "move":
                return self._process_move_action(context)
            elif action_request.action_type == "end_turn":
                return self._process_end_turn_action(context)
            else:
                return ActionResult(False, f"Unknown action type: {action_request.action_type}")
                
        except Exception as e:
            self.logger.error(f"Error processing action: {str(e)}")
            return ActionResult(False, f"Internal error: {str(e)}")
    
    def _process_ability_action(self, context: ActionContext) -> ActionResult:
        """Process an ability action"""
        piece = context.game_state.get_piece(context.action_request.piece_id)
        if not piece:
            return ActionResult(False, "Piece not found")
        
        # Load ability data
        ability_data = self._load_ability_data(context.action_request.ability_name)
        if not ability_data:
            return ActionResult(False, "Ability not found")
        
        # Resolve all tags in the ability
        effects = self.resolve_tags(ability_data, context)
        
        # Apply effects to game state
        state_changes = []
        for effect in effects:
            change = self._apply_effect(context.game_state, effect, context)
            if change:
                state_changes.append(change)
        
        # Trigger reactions
        triggered_effects = self.event_system.trigger_reactions(context.game_state, state_changes)
        
        return ActionResult(
            success=True,
            message="Ability executed successfully",
            state_changes=state_changes,
            triggered_effects=triggered_effects
        )
    
    def _process_move_action(self, context: ActionContext) -> ActionResult:
        """Process a move action using the integrated movement system"""
        piece = context.game_state.get_piece(context.action_request.piece_id)
        if not piece:
            return ActionResult(False, "Piece not found")

        if not context.action_request.target_position:
            return ActionResult(False, "Move action requires target position")

        # Load piece data for movement validation
        piece_data = self._get_piece_movement_data(piece)

        # Apply movement modifiers
        modified_movement_data = self.movement_modifier_engine.apply_modifiers_to_movement(
            piece.id, piece_data, piece.position, context.game_state
        )

        # Validate movement
        validation_result = self.movement_validator.validate_movement(
            modified_movement_data, piece.position, context.action_request.target_position,
            context.game_state, context
        )

        if not validation_result.is_valid:
            return ActionResult(False, validation_result.error_message)

        # Execute movement
        old_position = piece.position
        context.game_state.move_piece(piece.id, context.action_request.target_position)
        piece.has_moved_this_turn = True

        # Create state change record
        state_change = {
            'type': 'move',
            'piece_id': piece.id,
            'from_position': old_position,
            'to_position': context.action_request.target_position,
            'path': validation_result.path,
            'cost': validation_result.movement_cost
        }

        return ActionResult(
            success=True,
            message="Move executed successfully",
            state_changes=[state_change]
        )
    
    def _process_end_turn_action(self, context: ActionContext) -> ActionResult:
        """Process end turn action"""
        self.run_turn_cycle(context.game_state)
        return ActionResult(True, "Turn ended successfully")
    
    def _load_ability_data(self, ability_name: str) -> Optional[Ability]:
        """Load ability data from the data manager"""
        try:
            return pydantic_data_manager.load_ability(ability_name)
        except Exception as e:
            self.logger.error(f"Failed to load ability {ability_name}: {str(e)}")
            return None

    def _get_piece_movement_data(self, piece: EnhancedPieceState) -> Dict[str, Any]:
        """Extract movement data from piece for validation"""
        # This would load from piece definition in a real implementation
        # For now, create basic movement data structure
        return {
            'id': piece.id,
            'owner': piece.owner,
            'movement': {
                'type': 'orthogonal',  # Default - would be loaded from piece definition
                'distance': 1,
                'pattern': None,
                'piece_position': None
            },
            'can_capture': True,
            'color_directional': False,
            'abilities': piece.get_all_abilities() if hasattr(piece, 'get_all_abilities') else piece.abilities
        }
    
    def _apply_effect(self, game_state: EnhancedGameState, effect: Dict[str, Any], 
                     context: ActionContext) -> Optional[Dict[str, Any]]:
        """Apply a single effect to the game state"""
        # Implementation will be expanded as tag processors are added
        effect_type = effect.get('type')
        self.logger.debug(f"Applying effect: {effect_type}")
        
        # Basic effect application - will be enhanced
        return {"type": effect_type, "applied": True}
    
    def _process_turn_start_effects(self, game_state: EnhancedGameState):
        """Process effects that trigger at turn start"""
        pass  # Implementation will be added
    
    def _process_pending_effects(self, game_state: EnhancedGameState):
        """Process delayed and pending effects"""
        pass  # Implementation will be added
    
    def _process_pulse_effects(self, game_state: EnhancedGameState):
        """Process pulse effects that trigger on intervals"""
        pass  # Implementation will be added
    
    def _cleanup_expired_effects(self, game_state: EnhancedGameState):
        """Clean up expired status effects and temporary modifiers"""
        # Use status effect manager to tick effects
        current_turn = self.turn_manager.current_turn.turn_number if self.turn_manager.current_turn else 0
        self.status_effect_manager.tick_effects(current_turn)

        # Also tick piece-specific effects if they exist
        for piece in game_state.pieces.values():
            if hasattr(piece, 'tick_effects'):
                piece.tick_effects()
    
    def _process_piece_recharge(self, game_state: EnhancedGameState, piece: EnhancedPieceState):
        """Process recharge for a single piece"""
        # Implementation will be added in recharge system phase
        pass
