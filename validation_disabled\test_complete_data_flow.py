"""
Complete Data Flow Test for Adventure Chess
Tests the entire data pipeline from UI → EditorDataInterface → PydanticBridge → JSON → Piece Tester
"""

import sys
import os
import tempfile
import json
from pathlib import Path

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.editor_data_interface import EditorDataInterface
from utils.pydantic_bridge import pydantic_bridge
from schemas import pydantic_data_manager

def test_complete_data_flow():
    """Test complete data flow from UI simulation to piece tester consumption"""
    
    print("=== COMPLETE DATA FLOW TEST ===")
    
    # Test 1: Simulate UI data collection for a piece
    print("\n1. Testing Piece Data Flow...")
    
    # Create mock piece editor data (simulating UI widget values)
    mock_piece_data = {
        'version': '1.0.0',
        'name': 'Test Knight',
        'description': 'A test piece for data flow validation',
        'role': 'Commander',
        'canCastle': False,
        'trackStartingPosition': True,
        'colorDirectional': False,
        'blackIcon': 'knight_black.png',
        'whiteIcon': 'knight_white.png',
        'movement': {
            'type': 'lShape',
            'distance': 1,
            'pattern': [[0, 0, 0, 0, 0, 0, 0, 0] for _ in range(8)]
        },
        'canCapture': True,
        'enableRecharge': True,
        'maxPoints': 5,
        'startingPoints': 3,
        'rechargeType': 'turnRecharge',
        'turnPoints': 1,
        'abilities': ['test_ability']
    }
    
    # Test 2: Save through PydanticBridge
    print("2. Testing PydanticBridge Save...")
    
    # Create temporary file for testing
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    try:
        # Save using CompatibilityLayer (simulating editor save)
        from schemas.migration import CompatibilityLayer
        success, error = CompatibilityLayer.save_piece_from_dict(mock_piece_data,
                                                                os.path.basename(temp_filename))
        
        if success:
            print("✓ PydanticBridge save successful")
        else:
            print(f"❌ PydanticBridge save failed: {error}")
            return False
        
        # Test 3: Load back through PydanticBridge
        print("3. Testing PydanticBridge Load...")
        
        loaded_data, load_error = pydantic_bridge.load_piece_for_ui(
            os.path.basename(temp_filename).replace('.json', '')
        )
        
        if loaded_data and not load_error:
            print("✓ PydanticBridge load successful")
            print(f"  Loaded piece: {loaded_data.get('name', 'Unknown')}")
        else:
            print(f"❌ PydanticBridge load failed: {load_error}")
            return False
        
        # Test 4: Verify data integrity
        print("4. Testing Data Integrity...")
        
        key_fields = ['name', 'role', 'movement', 'maxPoints']
        integrity_passed = True
        
        for field in key_fields:
            if field in mock_piece_data and field in loaded_data:
                if mock_piece_data[field] == loaded_data[field]:
                    print(f"  ✓ {field}: {loaded_data[field]}")
                else:
                    print(f"  ❌ {field}: Expected {mock_piece_data[field]}, got {loaded_data[field]}")
                    integrity_passed = False
            else:
                print(f"  ⚠️ {field}: Missing in one of the datasets")
        
        if not integrity_passed:
            return False
        
        # Test 5: Simulate piece tester consumption
        print("5. Testing Piece Tester Consumption...")
        
        # Simulate how piece tester loads pieces
        try:
            piece_model, error = pydantic_data_manager.load_piece_by_name(
                os.path.basename(temp_filename).replace('.json', '')
            )
            
            if piece_model and not error:
                tester_data = piece_model.to_legacy_dict()
                print("✓ Piece tester can consume the data")
                print(f"  Piece name: {tester_data.get('name', 'Unknown')}")
                print(f"  Movement type: {tester_data.get('movement', {}).get('type', 'Unknown')}")
                print(f"  Max points: {tester_data.get('maxPoints', 'Unknown')}")
            else:
                print(f"❌ Piece tester consumption failed: {error}")
                return False
                
        except Exception as e:
            print(f"❌ Piece tester consumption error: {e}")
            return False
        
        print("\n✅ COMPLETE DATA FLOW TEST PASSED!")
        print("Data successfully flows: UI → EditorDataInterface → PydanticBridge → JSON → Piece Tester")
        return True
        
    finally:
        # Clean up temporary file
        try:
            os.unlink(temp_filename)
        except:
            pass

def test_ability_data_flow():
    """Test ability data flow"""
    print("\n=== ABILITY DATA FLOW TEST ===")
    
    # Create mock ability data
    mock_ability_data = {
        'version': '1.0.0',
        'name': 'Test Ability',
        'description': 'A test ability for data flow validation',
        'cost': 3,
        'activationMode': 'manual',
        'tags': ['move', 'capture'],
        'summonMax': 2,
        'captureTarget': 'Enemy'
    }
    
    # Test save and load cycle
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as temp_file:
        temp_filename = temp_file.name
    
    try:
        # Save through CompatibilityLayer
        from schemas.migration import CompatibilityLayer
        success, error = CompatibilityLayer.save_ability_from_dict(mock_ability_data,
                                                                  os.path.basename(temp_filename))
        
        if success:
            print("✓ Ability save successful")
        else:
            print(f"❌ Ability save failed: {error}")
            return False
        
        # Load back
        loaded_data, load_error = pydantic_bridge.load_ability_for_ui(
            os.path.basename(temp_filename).replace('.json', '')
        )
        
        if loaded_data and not load_error:
            print("✓ Ability load successful")
            print(f"  Loaded ability: {loaded_data.get('name', 'Unknown')}")
            print(f"  Tags: {loaded_data.get('tags', [])}")
            return True
        else:
            print(f"❌ Ability load failed: {load_error}")
            return False
            
    finally:
        try:
            os.unlink(temp_filename)
        except:
            pass

if __name__ == "__main__":
    piece_test_passed = test_complete_data_flow()
    ability_test_passed = test_ability_data_flow()
    
    if piece_test_passed and ability_test_passed:
        print("\n🎉 ALL DATA FLOW TESTS PASSED!")
        print("The Adventure Chess data pipeline is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ SOME DATA FLOW TESTS FAILED!")
        sys.exit(1)
