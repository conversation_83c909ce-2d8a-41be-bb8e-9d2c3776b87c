#!/usr/bin/env python3
"""
Adjacency Editor Dialog for Adventure Chess
Used for configuring adjacency requirements and adjacency recharge settings
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout, QFormLayout,
    QPushButton, QLabel, QSpinBox, QListWidget, QListWidgetItem,
    QGroupBox, QCheckBox, QMessageBox
)
from PyQt6.QtCore import Qt

# Import shared UI utilities
from ui.ui_shared_components import create_legend_item, create_dialog_buttons


class AdjacencyEditorDialog(QDialog):
    """
    Dialog for editing adjacency patterns and piece requirements
    Used for both adjacency recharge and adjacency required ability tags
    """
    
    def __init__(self, parent=None, initial_config=None, dialog_type="adjacency_required"):
        super().__init__(parent)
        self.dialog_type = dialog_type  # "adjacency_required" or "adjacency_recharge"
        
        if dialog_type == "adjacency_recharge":
            self.setWindowTitle("Adjacency Recharge Configuration")
        else:
            self.setWindowTitle("Adjacency Required Configuration")
            
        self.setMinimumSize(500, 600)
        
        # Initialize configuration
        self.config = initial_config if initial_config else {
            'pieces': [],
            'distance': 1,  # Default to 1 for 3x3 square pattern
            'pattern': [[False for _ in range(11)] for _ in range(11)]  # 11x11 grid for max distance
        }
        
        self.init_ui()
        self.load_config()
        self.update_pattern_visibility()  # Initialize visibility based on distance
    
    def init_ui(self):
        """Initialize the user interface"""
        layout = QVBoxLayout()
        
        # Instructions
        if self.dialog_type == "adjacency_recharge":
            instructions = QLabel("Configure which pieces must be adjacent for recharge to work.")
        else:
            instructions = QLabel("Configure which pieces must be adjacent for this ability to activate.")
        instructions.setWordWrap(True)
        instructions.setStyleSheet("color: #666; font-style: italic; padding: 10px;")
        layout.addWidget(instructions)
        
        # Distance configuration
        distance_group = QGroupBox("Distance Settings")
        distance_layout = QFormLayout()
        
        self.distance_spin = QSpinBox()
        self.distance_spin.setRange(0, 5)  # Updated range per glossary V1.0.1
        self.distance_spin.setValue(1)  # Default to 1 for 3x3 square
        self.distance_spin.setToolTip("Maximum distance for adjacency (0 = self only, 1 = 3x3 square, 2 = 5x5 square, etc.)")
        self.distance_spin.valueChanged.connect(self.on_distance_changed)
        distance_layout.addRow("Max Distance:", self.distance_spin)
        
        distance_group.setLayout(distance_layout)
        layout.addWidget(distance_group)
        
        # Adjacency pattern (dynamic chess board style)
        pattern_group = QGroupBox("Adjacency Pattern")
        pattern_layout = QVBoxLayout()

        pattern_info = QLabel("Select which positions are valid (square pattern: 0=self, 1=3x3, 2=5x5, etc.):")
        pattern_info.setStyleSheet("color: #666; font-size: 10px;")
        pattern_layout.addWidget(pattern_info)

        # Create dynamic grid for adjacency pattern (starts as 3x3, expands with distance)
        self.pattern_widget = QGroupBox()
        self.pattern_widget.setMinimumSize(200, 200)
        self.grid_layout = QGridLayout(self.pattern_widget)
        self.grid_layout.setSpacing(1)

        # Initialize with maximum possible size (11x11 for distance 5)
        self.max_grid_size = 11
        self.center_pos = self.max_grid_size // 2  # Center position (5, 5)
        self.pattern_buttons = []

        for r in range(self.max_grid_size):
            row = []
            for c in range(self.max_grid_size):
                btn = QPushButton()
                btn.setFixedSize(25, 25)
                btn.setCheckable(True)
                btn.clicked.connect(self.on_pattern_changed)

                # Style like a chess board
                is_light = (r + c) % 2 == 0
                if r == self.center_pos and c == self.center_pos:
                    # Center piece - always visible and disabled
                    btn.setText("♔")
                    btn.setEnabled(False)
                    btn.setStyleSheet("""
                        QPushButton {
                            background-color: #3498db;
                            color: white;
                            font-weight: bold;
                            border: 1px solid #2980b9;
                        }
                    """)
                else:
                    # Chess board pattern
                    light_color = "#f0d9b5" if is_light else "#b58863"
                    btn.setStyleSheet(f"""
                        QPushButton {{
                            background-color: {light_color};
                            border: 1px solid #8b7355;
                        }}
                        QPushButton:checked {{
                            background-color: #4CAF50;
                            color: white;
                            border: 2px solid #45a049;
                        }}
                        QPushButton:hover {{
                            border: 2px solid #3498db;
                        }}
                    """)

                self.grid_layout.addWidget(btn, r, c)
                row.append(btn)
            self.pattern_buttons.append(row)

        pattern_layout.addWidget(self.pattern_widget)
        pattern_group.setLayout(pattern_layout)
        layout.addWidget(pattern_group)
        
        # Piece selection
        pieces_group = QGroupBox("Required Pieces")
        pieces_layout = QVBoxLayout()
        
        # Piece list
        self.pieces_list = QListWidget()
        self.pieces_list.setMaximumHeight(120)
        pieces_layout.addWidget(self.pieces_list)
        
        # Piece management buttons
        piece_buttons_layout = QHBoxLayout()
        
        self.add_piece_btn = QPushButton("Add Piece")
        self.add_piece_btn.clicked.connect(self.add_piece)
        piece_buttons_layout.addWidget(self.add_piece_btn)
        
        self.remove_piece_btn = QPushButton("Remove Selected")
        self.remove_piece_btn.clicked.connect(self.remove_piece)
        piece_buttons_layout.addWidget(self.remove_piece_btn)
        
        piece_buttons_layout.addStretch()
        pieces_layout.addLayout(piece_buttons_layout)
        
        pieces_group.setLayout(pieces_layout)
        layout.addWidget(pieces_group)
        
        # Dialog buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.ok_btn = QPushButton("OK")
        self.ok_btn.clicked.connect(self.accept)
        button_layout.addWidget(self.ok_btn)
        
        self.cancel_btn = QPushButton("Cancel")
        self.cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def load_config(self):
        """Load configuration into UI"""
        # Load distance
        self.distance_spin.setValue(self.config.get('distance', 1))

        # Load pattern (handle both old 3x3 and new 11x11 formats)
        pattern = self.config.get('pattern', [[False for _ in range(11)] for _ in range(11)])

        # If pattern is smaller than 11x11, expand it
        if len(pattern) < 11:
            new_pattern = [[False for _ in range(11)] for _ in range(11)]
            offset = (11 - len(pattern)) // 2
            for r in range(len(pattern)):
                for c in range(len(pattern[0])):
                    new_pattern[r + offset][c + offset] = pattern[r][c]
            pattern = new_pattern

        # Load pattern into buttons
        for r in range(11):
            for c in range(11):
                if r != self.center_pos or c != self.center_pos:  # Skip center button
                    self.pattern_buttons[r][c].setChecked(pattern[r][c])

        # Load pieces
        pieces = self.config.get('pieces', [])
        for piece in pieces:
            item = QListWidgetItem(piece)
            self.pieces_list.addItem(item)
    
    def on_distance_changed(self):
        """Handle distance change - update pattern visibility"""
        self.update_pattern_visibility()

    def update_pattern_visibility(self):
        """Update which pattern buttons are visible based on distance (square pattern)"""
        distance = self.distance_spin.value()

        for r in range(11):
            for c in range(11):
                # Calculate Chebyshev distance (square pattern) from center
                chebyshev_dist = max(abs(r - self.center_pos), abs(c - self.center_pos))

                # Show button if within distance range
                visible = chebyshev_dist <= distance
                self.pattern_buttons[r][c].setVisible(visible)

                # If hiding a checked button, uncheck it
                if not visible and self.pattern_buttons[r][c].isChecked():
                    self.pattern_buttons[r][c].setChecked(False)

        # Resize the pattern widget based on visible area (square pattern)
        visible_size = (distance * 2 + 1) * 27  # 25px button + 2px spacing
        self.pattern_widget.setFixedSize(max(visible_size, 100), max(visible_size, 100))

    def on_pattern_changed(self):
        """Handle pattern button changes"""
        # Update internal pattern state for all buttons
        for r in range(11):
            for c in range(11):
                if r != self.center_pos or c != self.center_pos:  # Skip center
                    if len(self.config['pattern']) <= r:
                        # Expand pattern if needed
                        while len(self.config['pattern']) <= r:
                            self.config['pattern'].append([False] * 11)
                    if len(self.config['pattern'][r]) <= c:
                        # Expand row if needed
                        while len(self.config['pattern'][r]) <= c:
                            self.config['pattern'][r].append(False)
                    self.config['pattern'][r][c] = self.pattern_buttons[r][c].isChecked()
    
    def add_piece(self):
        """Add a piece to the requirements list"""
        # Use simple input dialog for piece selection
        from PyQt6.QtWidgets import QInputDialog
        piece_name, ok = QInputDialog.getText(
            self,
            "Add Piece",
            "Enter piece name:",
            text="Pawn"
        )
        if ok and piece_name.strip():
            # Check if piece is already in the list
            existing_items = [self.pieces_list.item(i).text()
                            for i in range(self.pieces_list.count())]
            if piece_name.strip() not in existing_items:
                item = QListWidgetItem(piece_name.strip())
                self.pieces_list.addItem(item)
    
    def remove_piece(self):
        """Remove selected piece from the list"""
        current_item = self.pieces_list.currentItem()
        if current_item:
            row = self.pieces_list.row(current_item)
            self.pieces_list.takeItem(row)
    
    def get_config(self):
        """Get the current configuration"""
        # Update pieces list
        pieces = []
        for i in range(self.pieces_list.count()):
            pieces.append(self.pieces_list.item(i).text())
        
        return {
            'distance': self.distance_spin.value(),
            'pattern': self.config['pattern'],
            'pieces': pieces
        }


def edit_adjacency_config(parent=None, initial_config=None, dialog_type="adjacency_required"):
    """
    Convenience function to edit adjacency configuration
    
    Args:
        parent: Parent widget
        initial_config: Initial configuration dict
        dialog_type: "adjacency_required" or "adjacency_recharge"
    
    Returns:
        Configuration dict if accepted, None if cancelled
    """
    dialog = AdjacencyEditorDialog(parent, initial_config, dialog_type)
    if dialog.exec() == QDialog.DialogCode.Accepted:
        return dialog.get_config()
    return None
