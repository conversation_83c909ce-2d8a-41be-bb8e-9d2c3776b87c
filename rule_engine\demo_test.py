#!/usr/bin/env python3
"""
Demo test for Adventure Chess Movement System
Demonstrates key features and functionality
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def demo_movement_patterns():
    """Demonstrate different movement patterns"""
    print("🎮 Demo: Movement Patterns")
    print("-" * 40)
    
    from schemas.base import Coordinate
    from core.tester_engine import PieceColor
    from rule_engine.movement.patterns import MovementPatternProcessor, PatternType
    
    processor = MovementPatternProcessor()
    center = Coordinate(row=3, col=3)
    
    class MockGameState:
        def get_piece_at(self, pos):
            return []
    
    game_state = MockGameState()
    
    # Test different movement types
    movement_types = [
        ('orthogonal', 'Rook-like movement'),
        ('diagonal', 'Bishop-like movement'), 
        ('any', 'Queen-like movement'),
        ('lShape', 'Knight-like movement')
    ]
    
    for movement_type, description in movement_types:
        piece_data = {
            'movement': {'type': movement_type, 'distance': 2},
            'can_capture': True,
            'owner': PieceColor.WHITE
        }
        
        result = processor.calculate_valid_moves(piece_data, center, game_state)
        print(f"  📍 {description}: {len(result.valid_moves)} valid positions")
    
    # Demo custom pattern
    print(f"\n  🎯 Custom Pattern Demo:")
    custom_pattern = [[0 for _ in range(8)] for _ in range(8)]
    
    # Create a cross pattern
    for i in range(8):
        if i != 3:
            custom_pattern[3][i] = PatternType.BOTH.value  # Horizontal line
            custom_pattern[i][3] = PatternType.BOTH.value  # Vertical line
    
    piece_data = {
        'movement': {
            'type': 'custom',
            'pattern': custom_pattern,
            'piece_position': [3, 3]
        },
        'can_capture': True,
        'owner': PieceColor.WHITE
    }
    
    result = processor.calculate_valid_moves(piece_data, center, game_state)
    print(f"     Cross pattern: {len(result.valid_moves)} valid positions")
    print(f"     Sample positions: {[(p.row, p.col) for p in result.valid_moves[:5]]}")


def demo_movement_validation():
    """Demonstrate movement validation"""
    print(f"\n🔍 Demo: Movement Validation")
    print("-" * 40)
    
    from schemas.base import Coordinate
    from core.tester_engine import PieceColor
    from rule_engine.movement.validator import MovementValidator, ValidationLevel
    
    validator = MovementValidator(ValidationLevel.STANDARD)
    
    piece_data = {
        'id': 'demo_piece',
        'movement': {'type': 'orthogonal', 'distance': 3},
        'can_capture': True,
        'owner': PieceColor.WHITE,
        'abilities': []
    }
    
    class MockGameState:
        def get_piece_at(self, pos):
            return []
        def get_cell(self, pos):
            class MockCell:
                obstacles = []
            return MockCell()
    
    game_state = MockGameState()
    current_pos = Coordinate(row=2, col=2)
    
    # Test valid movements
    test_moves = [
        (Coordinate(row=2, col=4), "Valid orthogonal move"),
        (Coordinate(row=2, col=6), "Invalid - too far"),
        (Coordinate(row=4, col=4), "Invalid - diagonal not allowed")
    ]
    
    for target_pos, description in test_moves:
        try:
            result = validator.validate_movement(piece_data, current_pos, target_pos, game_state)
            status = "✅ VALID" if result.is_valid else "❌ INVALID"
            print(f"  {status} {description}")
            if not result.is_valid and result.error_message:
                print(f"         Reason: {result.error_message}")
        except Exception as e:
            print(f"  ❌ INVALID {description} (Coordinate validation failed)")


def demo_movement_modifiers():
    """Demonstrate movement modifiers"""
    print(f"\n⚡ Demo: Movement Modifiers")
    print("-" * 40)
    
    from schemas.base import Coordinate
    from rule_engine.movement.modifiers import MovementModifierEngine, MovementModifier, ModifierType
    
    modifier_engine = MovementModifierEngine()
    
    # Base movement data
    base_movement = {
        'movement': {'type': 'orthogonal', 'distance': 2}
    }
    
    print(f"  📊 Base movement distance: {base_movement['movement']['distance']}")
    
    # Add range bonus
    range_bonus = MovementModifier(
        modifier_id="speed_boost",
        modifier_type=ModifierType.RANGE_BONUS,
        source_ability="haste",
        source_piece_id="caster",
        target_piece_id="demo_piece",
        value=2
    )
    
    modifier_engine.add_modifier(range_bonus)
    
    current_pos = Coordinate(row=3, col=3)
    modified_movement = modifier_engine.apply_modifiers_to_movement(
        "demo_piece", base_movement, current_pos, None
    )
    
    print(f"  ⚡ With speed boost: {modified_movement['distance']}")
    
    # Test movement restrictions
    can_move, reason = modifier_engine.check_movement_allowed("demo_piece")
    print(f"  🚶 Can move: {can_move}")
    
    # Add immobilize effect
    immobilize = MovementModifier(
        modifier_id="paralysis",
        modifier_type=ModifierType.IMMOBILIZE,
        source_ability="hold_person",
        source_piece_id="enemy_caster",
        target_piece_id="demo_piece"
    )
    
    modifier_engine.add_modifier(immobilize)
    can_move, reason = modifier_engine.check_movement_allowed("demo_piece")
    print(f"  🚫 With paralysis: {can_move} ({reason})")


def demo_pathfinding():
    """Demonstrate pathfinding capabilities"""
    print(f"\n🗺️  Demo: Pathfinding & Line of Sight")
    print("-" * 40)
    
    from schemas.base import Coordinate
    from rule_engine.movement.pathfinding import PathfindingEngine, LineOfSightCalculator
    
    pathfinder = PathfindingEngine()
    los_calc = LineOfSightCalculator()
    
    start = Coordinate(row=1, col=1)
    end = Coordinate(row=4, col=4)
    
    class MockGameState:
        def get_piece_at(self, pos):
            # Block some positions
            blocked_positions = [(2, 2), (3, 3)]
            if (pos.row, pos.col) in blocked_positions:
                return [{'id': 'obstacle'}]
            return []
        
        def get_cell(self, pos):
            class MockCell:
                obstacles = []
            return MockCell()
    
    game_state = MockGameState()
    
    # Test pathfinding
    path = pathfinder.find_path(start, end, game_state)
    print(f"  🎯 Path from ({start.row},{start.col}) to ({end.row},{end.col}): {'Found' if path else 'Blocked'}")
    
    # Test line of sight
    los_result = los_calc.has_line_of_sight(start, end, game_state)
    print(f"  👁️  Line of sight: {'Clear' if los_result.has_line_of_sight else 'Blocked'}")
    if los_result.blocked_by:
        blocking_pos = los_result.blocked_by[0]
        print(f"      Blocked by obstacle at ({blocking_pos.row},{blocking_pos.col})")
    
    # Test range calculation
    range_val = los_calc.calculate_range(start, end)
    print(f"  📏 Range: {range_val} squares")
    
    # Test positions in range
    positions_in_range = los_calc.get_positions_in_range(start, 2)
    print(f"  🎯 Positions within range 2: {len(positions_in_range)}")


def demo_rule_engine():
    """Demonstrate rule engine integration"""
    print(f"\n🎲 Demo: Rule Engine Integration")
    print("-" * 40)
    
    from schemas.base import Coordinate
    from core.tester_engine import PieceColor
    from rule_engine.engine import RuleEngine
    from rule_engine.game_state import EnhancedGameState, EnhancedPieceState
    
    # Create rule engine
    engine = RuleEngine()
    print(f"  ✅ Rule engine created successfully")
    
    # Create game state
    game_state = EnhancedGameState()
    print(f"  ✅ Enhanced game state created")
    
    # Create test piece
    test_piece = EnhancedPieceState(
        id="demo_piece",
        piece_type="test_piece",
        owner=PieceColor.WHITE,
        position=Coordinate(row=2, col=2),
        current_points=5,
        max_points=10
    )
    
    game_state.pieces["demo_piece"] = test_piece
    print(f"  ✅ Test piece added at ({test_piece.position.row},{test_piece.position.col})")
    
    # Test movement simulation
    target_pos = Coordinate(row=2, col=4)
    simulated_state = engine.simulate_ability(
        game_state, "demo_piece", "move", [target_pos]
    )
    
    print(f"  ✅ Movement simulation completed")
    print(f"  📍 Simulated move to ({target_pos.row},{target_pos.col})")


def main():
    """Run all demos"""
    print("🚀 Adventure Chess Movement System - Feature Demo")
    print("=" * 60)
    
    try:
        demo_movement_patterns()
        demo_movement_validation()
        demo_movement_modifiers()
        demo_pathfinding()
        demo_rule_engine()
        
        print("\n" + "=" * 60)
        print("🎉 All demos completed successfully!")
        print("\n📋 **Movement System Features Demonstrated:**")
        print("   ✅ Multiple movement patterns (orthogonal, diagonal, L-shape, custom)")
        print("   ✅ Comprehensive movement validation")
        print("   ✅ Movement modifiers and restrictions")
        print("   ✅ Pathfinding and line-of-sight calculations")
        print("   ✅ Full rule engine integration")
        print("\n🎯 **Ready for Phase 3: Ability Tag Processing Engine**")
        
    except Exception as e:
        print(f"\n❌ Demo failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
