#!/usr/bin/env python3
"""Remove Obstacle Tag Resolver"""
from typing import List, Dict, Any
from schemas import Ability
from ..base import BaseTagResolver, TagCategory, TagEffect

class RemoveObstacleTagResolver(BaseTagResolver):
    def _get_tag_name(self) -> str: return "removeObstacle"
    def _get_category(self) -> TagCategory: return TagCategory.ACTION
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        return [TagEffect(effect_type="remove_obstacle", priority=35, immediate=True, parameters={"action": "remove_obstacle"})]
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        return {"requires_target": True, "target_type": "position", "min_targets": 1, "max_targets": 3}
