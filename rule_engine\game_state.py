#!/usr/bin/env python3
"""
Enhanced Game State Management for Adventure Chess Rule Engine
Extends the existing logic engine with validation and advanced features
"""

from typing import Dict, List, Optional, Tuple, Any, Set
from dataclasses import dataclass, field
import copy
import uuid
from datetime import datetime

# Import from schemas for data structures
from schemas.base import Coordinate
from schemas import Piece, Ability
from core.tester_engine import PieceColor

# For now, create placeholder base classes since logic_engine is empty
class BaseGameState:
    def __init__(self):
        self.pieces = {}
        self.current_player = PieceColor.WHITE
        self.turn_counter = 1

    def get_piece(self, piece_id):
        return self.pieces.get(piece_id)

    def get_piece_at(self, position):
        return []

    def move_piece(self, piece_id, target_position):
        piece = self.get_piece(piece_id)
        if piece:
            piece.position = target_position

    def validate_state(self):
        return True, []

    def next_turn(self):
        self.turn_counter += 1
        self.current_player = PieceColor.BLACK if self.current_player == PieceColor.WHITE else PieceColor.WHITE

class BasePieceState:
    def __init__(self, id, piece_type, owner, position, current_points=0, max_points=0,
                 status_effects=None, abilities=None, movement_modifiers=None,
                 is_committed=False, turns_immobilized=0, carried_pieces=None,
                 invisibility_state=None, has_moved_this_turn=False,
                 abilities_used_this_turn=None, cooldowns=None):
        self.id = id
        self.piece_type = piece_type
        self.owner = owner
        self.position = position
        self.current_points = current_points
        self.max_points = max_points
        self.status_effects = status_effects or []
        self.abilities = abilities or []
        self.movement_modifiers = movement_modifiers or {}
        self.is_committed = is_committed
        self.turns_immobilized = turns_immobilized
        self.carried_pieces = carried_pieces or []
        self.invisibility_state = invisibility_state or {}
        self.has_moved_this_turn = has_moved_this_turn
        self.abilities_used_this_turn = abilities_used_this_turn or []
        self.cooldowns = cooldowns or {}

    def can_use_ability(self, ability_name):
        return True, "Ability can be used"

    def get_ability(self, ability_name):
        for ability in self.abilities:
            if isinstance(ability, str) and ability == ability_name:
                return {'name': ability_name, 'tags': []}
            elif isinstance(ability, dict) and ability.get('name') == ability_name:
                return ability
        return None

    def apply_status_effect(self, effect):
        self.status_effects.append(effect)

    def tick_effects(self):
        self.status_effects = [e for e in self.status_effects if getattr(e, 'duration', 1) > 0]

class StatusEffect:
    def __init__(self, name, duration=-1, parameters=None):
        self.name = name
        self.duration = duration
        self.parameters = parameters or {}

class BoardCell:
    def __init__(self):
        self.obstacles = []


@dataclass
class ActionContext:
    """Context information for processing an action"""
    game_state: 'EnhancedGameState'
    action_request: Any  # Will be ActionRequest from engine.py
    current_player: PieceColor
    turn_counter: int
    is_simulation: bool = False
    timestamp: datetime = field(default_factory=datetime.now)
    context_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    
    # Additional context for tag processing
    source_piece: Optional['EnhancedPieceState'] = None
    target_pieces: List['EnhancedPieceState'] = field(default_factory=list)
    affected_positions: List[Coordinate] = field(default_factory=list)
    cost_modifiers: Dict[str, float] = field(default_factory=dict)
    range_modifiers: Dict[str, int] = field(default_factory=dict)


@dataclass
class EffectInstance:
    """Represents an active effect in the game"""
    effect_id: str
    effect_type: str
    source_ability: str
    source_piece_id: str
    target_piece_id: Optional[str] = None
    target_position: Optional[Coordinate] = None
    parameters: Dict[str, Any] = field(default_factory=dict)
    duration: int = -1  # -1 for permanent effects
    created_turn: int = 0
    priority: int = 0  # Higher priority effects resolve first
    
    def is_expired(self, current_turn: int) -> bool:
        """Check if this effect has expired"""
        if self.duration == -1:
            return False
        return (current_turn - self.created_turn) >= self.duration


class EnhancedPieceState(BasePieceState):
    """Enhanced piece state with additional rule engine features"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Enhanced tracking
        self.ability_cooldowns: Dict[str, int] = {}
        self.temporary_abilities: List[str] = []
        self.movement_modifiers: Dict[str, Any] = {}
        self.targeting_modifiers: Dict[str, Any] = {}
        self.cost_modifiers: Dict[str, float] = {}
        
        # State tracking
        self.actions_this_turn: List[str] = []
        self.effects_applied: List[str] = []
        self.last_position: Optional[Coordinate] = None
        self.starting_position: Optional[Coordinate] = None
        
        # Advanced properties
        self.is_invisible: bool = False
        self.visibility_conditions: Dict[str, Any] = {}
        self.carried_by: Optional[str] = None  # piece ID carrying this piece
        self.sharing_space_with: List[str] = []  # piece IDs sharing space
        
        # Recharge system enhancements
        self.recharge_modifiers: Dict[str, Any] = {}
        self.commitment_state: Dict[str, Any] = {}
    
    def can_use_ability_enhanced(self, ability_name: str, context: ActionContext) -> Tuple[bool, str, Dict[str, Any]]:
        """
        Enhanced ability usage check with detailed information
        Returns: (can_use, reason, additional_info)
        """
        # Basic checks from parent class
        can_use, reason = self.can_use_ability(ability_name)
        if not can_use:
            return False, reason, {}
        
        additional_info = {}
        
        # Check enhanced conditions
        if ability_name in self.ability_cooldowns:
            cooldown = self.ability_cooldowns[ability_name]
            if cooldown > 0:
                return False, f"Ability on cooldown ({cooldown} turns)", {"cooldown": cooldown}
        
        # Check cost modifiers
        ability = self.get_ability(ability_name)
        if ability:
            base_cost = ability.get('cost', 0)
            modified_cost = self._calculate_modified_cost(base_cost, ability_name, context)
            additional_info['base_cost'] = base_cost
            additional_info['modified_cost'] = modified_cost
            
            if modified_cost > self.current_points:
                return False, f"Insufficient points ({self.current_points}/{modified_cost})", additional_info
        
        # Check position requirements
        if self._requires_starting_position(ability_name) and not self._is_at_starting_position():
            return False, "Ability requires piece to be at starting position", {}
        
        return True, "Ability can be used", additional_info
    
    def _calculate_modified_cost(self, base_cost: int, ability_name: str, context: ActionContext) -> int:
        """Calculate cost with all modifiers applied"""
        modified_cost = base_cost
        
        # Apply cost modifiers
        for modifier_name, modifier_value in self.cost_modifiers.items():
            if isinstance(modifier_value, float):
                modified_cost = int(modified_cost * modifier_value)
            elif isinstance(modifier_value, int):
                modified_cost += modifier_value
        
        # Apply context-specific modifiers
        for modifier_name, modifier_value in context.cost_modifiers.items():
            if isinstance(modifier_value, float):
                modified_cost = int(modified_cost * modifier_value)
            elif isinstance(modifier_value, int):
                modified_cost += modifier_value
        
        return max(0, modified_cost)  # Cost cannot be negative
    
    def _requires_starting_position(self, ability_name: str) -> bool:
        """Check if ability requires starting position"""
        ability = self.get_ability(ability_name)
        if ability and isinstance(ability, dict):
            return 'requiresStartingPosition' in ability.get('tags', [])
        return False
    
    def _is_at_starting_position(self) -> bool:
        """Check if piece is at its starting position"""
        return (self.starting_position is not None and 
                self.position is not None and 
                self.position.row == self.starting_position.row and 
                self.position.col == self.starting_position.col)
    
    def add_temporary_ability(self, ability_name: str, duration: int = -1):
        """Add a temporary ability to this piece"""
        if ability_name not in self.temporary_abilities:
            self.temporary_abilities.append(ability_name)
            if duration > 0:
                # Add to status effects for duration tracking
                effect = StatusEffect(
                    name=f"temp_ability_{ability_name}",
                    duration=duration,
                    parameters={"ability": ability_name}
                )
                self.apply_status_effect(effect)
    
    def remove_temporary_ability(self, ability_name: str):
        """Remove a temporary ability from this piece"""
        if ability_name in self.temporary_abilities:
            self.temporary_abilities.remove(ability_name)
    
    def get_all_abilities(self) -> List[str]:
        """Get all abilities including temporary ones"""
        all_abilities = []
        
        # Add permanent abilities
        for ability in self.abilities:
            if isinstance(ability, str):
                all_abilities.append(ability)
            elif isinstance(ability, dict):
                all_abilities.append(ability.get('name', ''))
        
        # Add temporary abilities
        all_abilities.extend(self.temporary_abilities)
        
        return all_abilities
    
    def tick_effects_enhanced(self):
        """Enhanced effect ticking with additional cleanup"""
        # Call parent tick_effects
        self.tick_effects()
        
        # Clean up temporary abilities
        expired_abilities = []
        for effect in self.status_effects:
            if effect.name.startswith("temp_ability_") and effect.duration <= 0:
                ability_name = effect.parameters.get("ability")
                if ability_name:
                    expired_abilities.append(ability_name)
        
        for ability_name in expired_abilities:
            self.remove_temporary_ability(ability_name)
        
        # Tick ability cooldowns
        for ability_name in list(self.ability_cooldowns.keys()):
            self.ability_cooldowns[ability_name] -= 1
            if self.ability_cooldowns[ability_name] <= 0:
                del self.ability_cooldowns[ability_name]
        
        # Clear per-turn tracking
        self.actions_this_turn.clear()
        self.effects_applied.clear()


class EnhancedGameState(BaseGameState):
    """Enhanced game state with rule engine features"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Enhanced tracking
        self.active_effects: Dict[str, EffectInstance] = {}
        self.effect_queue: List[EffectInstance] = []
        self.reaction_queue: List[Dict[str, Any]] = []
        
        # Turn management
        self.phase: str = "main"  # main, recharge, cleanup, etc.
        self.actions_this_turn: List[Dict[str, Any]] = []
        self.turn_start_time: Optional[datetime] = None
        
        # Validation and state tracking
        self.state_version: int = 1
        self.last_validation: Optional[datetime] = None
        self.validation_errors: List[str] = []
        
        # Game configuration
        self.rule_variants: Dict[str, bool] = {}
        self.time_limits: Dict[str, int] = {}
        
        # Convert existing pieces to enhanced pieces
        self._upgrade_pieces_to_enhanced()
    
    def _upgrade_pieces_to_enhanced(self):
        """Convert existing pieces to enhanced piece states"""
        enhanced_pieces = {}
        for piece_id, piece in self.pieces.items():
            if not isinstance(piece, EnhancedPieceState):
                # Create enhanced piece from existing piece
                enhanced_piece = EnhancedPieceState(
                    id=piece.id,
                    piece_type=piece.piece_type,
                    owner=piece.owner,
                    position=piece.position,
                    current_points=piece.current_points,
                    max_points=piece.max_points,
                    status_effects=piece.status_effects.copy(),
                    abilities=piece.abilities.copy(),
                    movement_modifiers=piece.movement_modifiers.copy(),
                    is_committed=piece.is_committed,
                    turns_immobilized=piece.turns_immobilized,
                    carried_pieces=piece.carried_pieces.copy(),
                    invisibility_state=piece.invisibility_state.copy(),
                    has_moved_this_turn=piece.has_moved_this_turn,
                    abilities_used_this_turn=piece.abilities_used_this_turn.copy(),
                    cooldowns=piece.cooldowns.copy()
                )
                enhanced_pieces[piece_id] = enhanced_piece
            else:
                enhanced_pieces[piece_id] = piece
        
        self.pieces = enhanced_pieces
    
    def add_effect(self, effect: EffectInstance):
        """Add an active effect to the game state"""
        self.active_effects[effect.effect_id] = effect
        
        # Add to queue if it has a delay
        if effect.parameters.get('delay', 0) > 0:
            self.effect_queue.append(effect)
    
    def remove_effect(self, effect_id: str):
        """Remove an active effect"""
        if effect_id in self.active_effects:
            del self.active_effects[effect_id]
        
        # Remove from queue if present
        self.effect_queue = [e for e in self.effect_queue if e.effect_id != effect_id]
    
    def get_effects_by_type(self, effect_type: str) -> List[EffectInstance]:
        """Get all active effects of a specific type"""
        return [effect for effect in self.active_effects.values() if effect.effect_type == effect_type]
    
    def get_effects_on_piece(self, piece_id: str) -> List[EffectInstance]:
        """Get all effects targeting a specific piece"""
        return [effect for effect in self.active_effects.values() if effect.target_piece_id == piece_id]
    
    def get_effects_on_position(self, position: Coordinate) -> List[EffectInstance]:
        """Get all effects targeting a specific position"""
        return [effect for effect in self.active_effects.values() 
                if effect.target_position and 
                effect.target_position.row == position.row and 
                effect.target_position.col == position.col]
    
    def validate_state_enhanced(self) -> Tuple[bool, List[str]]:
        """Enhanced state validation with detailed error reporting"""
        is_valid, errors = self.validate_state()
        
        # Additional enhanced validations
        enhanced_errors = []
        
        # Validate effects
        for effect_id, effect in self.active_effects.items():
            if effect.target_piece_id and effect.target_piece_id not in self.pieces:
                enhanced_errors.append(f"Effect {effect_id} targets non-existent piece {effect.target_piece_id}")
        
        # Validate piece states
        for piece_id, piece in self.pieces.items():
            if isinstance(piece, EnhancedPieceState):
                # Validate carried pieces
                for carried_id in piece.carried_pieces:
                    if carried_id not in self.pieces:
                        enhanced_errors.append(f"Piece {piece_id} carries non-existent piece {carried_id}")
                
                # Validate space sharing
                for shared_id in piece.sharing_space_with:
                    if shared_id not in self.pieces:
                        enhanced_errors.append(f"Piece {piece_id} shares space with non-existent piece {shared_id}")
        
        all_errors = errors + enhanced_errors
        self.validation_errors = all_errors
        self.last_validation = datetime.now()
        
        return len(all_errors) == 0, all_errors
    
    def create_snapshot_enhanced(self) -> 'EnhancedGameState':
        """Create a deep copy with enhanced state preservation"""
        snapshot = copy.deepcopy(self)
        snapshot.state_version = self.state_version + 1
        return snapshot

    def create_snapshot(self) -> 'EnhancedGameState':
        """Alias for create_snapshot_enhanced for compatibility"""
        return self.create_snapshot_enhanced()
    
    def next_turn_enhanced(self):
        """Enhanced turn advancement with phase management"""
        # Process cleanup phase
        self.phase = "cleanup"
        self._process_cleanup_phase()
        
        # Process recharge phase
        self.phase = "recharge"
        self._process_recharge_phase()
        
        # Advance turn
        self.next_turn()
        
        # Reset to main phase
        self.phase = "main"
        self.turn_start_time = datetime.now()
        self.actions_this_turn.clear()
        
        # Increment state version
        self.state_version += 1
    
    def _process_cleanup_phase(self):
        """Process cleanup at end of turn"""
        # Remove expired effects
        expired_effects = []
        for effect_id, effect in self.active_effects.items():
            if effect.is_expired(self.turn_counter):
                expired_effects.append(effect_id)
        
        for effect_id in expired_effects:
            self.remove_effect(effect_id)
        
        # Tick all piece effects
        for piece in self.pieces.values():
            if isinstance(piece, EnhancedPieceState):
                piece.tick_effects_enhanced()
    
    def _process_recharge_phase(self):
        """Process recharge systems"""
        # This will be implemented in the recharge system phase
        pass

    def is_valid_position(self, position: Coordinate) -> bool:
        """Check if position is within board bounds"""
        return 0 <= position.row < 8 and 0 <= position.col < 8

    def get_pieces_by_owner(self, owner: PieceColor) -> List[EnhancedPieceState]:
        """Get all pieces owned by a specific player"""
        return [piece for piece in self.pieces.values() if piece.owner == owner]
