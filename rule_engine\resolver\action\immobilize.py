#!/usr/bin/env python3
"""
Immobilize Tag Resolver for Adventure Chess Rule Engine
Handles piece immobilization with duration and condition support
"""

from typing import List, Dict, Any
import logging

from schemas import Ability
from schemas.base import Coordinate
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class ImmobilizeTagResolver(BaseTagResolver):
    """
    Resolver for the 'immobilize' tag
    Handles piece immobilization with various durations and conditions
    """
    
    def _get_tag_name(self) -> str:
        return "immobilize"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.ACTION
    
    def _get_conflicts(self) -> List[str]:
        return ["move", "teleport", "carryPiece", "swapPlaces"]  # Immobilize conflicts with movement
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        """
        Process immobilize tag and return immobilization effects
        
        Args:
            ability: The ability containing the immobilize tag
            context: ActionContext with game state and action details
            
        Returns:
            List of TagEffect objects for immobilization
        """
        effects = []
        
        # Extract immobilize-specific data
        immobilize_data = extract_tag_data(ability, "immobilize")
        
        # Get immobilize parameters
        duration = immobilize_data.get("duration", 1)
        target_list = immobilize_data.get("target_list", [])
        condition = immobilize_data.get("condition")
        stackable = immobilize_data.get("stackable", False)
        
        # Find valid immobilize targets
        immobilize_targets = self._find_immobilize_targets(context, target_list)
        
        if not immobilize_targets:
            return effects
        
        # Create immobilize effects for each target
        for target_piece_id in immobilize_targets:
            immobilize_effect = TagEffect(
                effect_type="immobilize",
                priority=95,  # Very high priority to prevent movement
                immediate=True,
                parameters={
                    "action": "immobilize",
                    "target_piece_id": target_piece_id,
                    "source_piece_id": context.source_piece_id,
                    "duration": duration,
                    "condition": condition,
                    "stackable": stackable,
                    "immobilize_type": immobilize_data.get("type", "standard")
                }
            )
            effects.append(immobilize_effect)
        
        # Add conditional release effects
        if condition:
            for target_piece_id in immobilize_targets:
                condition_effect = TagEffect(
                    effect_type="condition_tracker",
                    priority=20,
                    immediate=False,
                    parameters={
                        "target_piece_id": target_piece_id,
                        "condition": condition,
                        "on_condition_met": "remove_immobilize",
                        "check_frequency": "per_turn"
                    }
                )
                effects.append(condition_effect)
        
        # Add duration tracking for temporary immobilization
        if duration > 0:
            for target_piece_id in immobilize_targets:
                duration_effect = TagEffect(
                    effect_type="duration_tracker",
                    priority=10,
                    immediate=False,
                    parameters={
                        "target_piece_id": target_piece_id,
                        "duration": duration,
                        "effect_type": "immobilize",
                        "on_expire": "remove_immobilize"
                    }
                )
                effects.append(duration_effect)
        
        return effects
    
    def _find_immobilize_targets(self, context: Any, target_list: List[str]) -> List[str]:
        """
        Find valid pieces to immobilize
        
        Args:
            context: ActionContext
            target_list: List of specific piece types/IDs to target
            
        Returns:
            List of piece IDs that can be immobilized
        """
        targets = []
        
        # Get all target positions
        target_positions = []
        if context.targeting.primary_target:
            target_positions.append(context.targeting.primary_target)
        target_positions.extend(context.targeting.secondary_targets)
        
        # Find pieces at target positions
        for position in target_positions:
            pieces_at_position = context.game_state.get_piece_at(position)
            
            for piece in pieces_at_position:
                if self._can_immobilize_piece(context, piece, target_list):
                    targets.append(piece.id)
        
        return targets
    
    def _can_immobilize_piece(self, context: Any, target_piece: Any, target_list: List[str]) -> bool:
        """
        Check if a piece can be immobilized
        
        Args:
            context: ActionContext
            target_piece: The piece to potentially immobilize
            target_list: List of valid target types
            
        Returns:
            True if piece can be immobilized
        """
        # Check if piece type is in target list (if specified)
        if target_list and target_piece.piece_type not in target_list:
            return False
        
        # Check if piece has immobilize immunity
        if hasattr(target_piece, 'abilities'):
            for ability in target_piece.abilities:
                if isinstance(ability, dict) and "immobilizeImmune" in ability.get('tags', []):
                    return False
        
        # Check if piece is already immobilized (and not stackable)
        if hasattr(target_piece, 'turns_immobilized') and target_piece.turns_immobilized > 0:
            # Check if this immobilize effect is stackable
            immobilize_data = extract_tag_data(context.action_request, "immobilize")
            if not immobilize_data.get("stackable", False):
                return False
        
        # Check if piece has freedom of movement
        if hasattr(target_piece, 'status_effects'):
            for effect in target_piece.status_effects:
                if effect.name in ["freedom", "unstoppable"]:
                    return False
        
        return True
    
    def _check_immobilize_condition(self, condition: str, target_piece: Any, context: Any) -> bool:
        """
        Check if immobilize condition is met
        
        Args:
            condition: The condition to check
            target_piece: The immobilized piece
            context: ActionContext
            
        Returns:
            True if condition is met (immobilize should be removed)
        """
        if condition == "until_damaged":
            # Check if piece has taken damage since immobilization
            return hasattr(target_piece, 'damage_taken_this_turn') and target_piece.damage_taken_this_turn > 0
        
        elif condition == "until_adjacent_ally":
            # Check if an allied piece is adjacent
            adjacent_positions = self._get_adjacent_positions(target_piece.position)
            for pos in adjacent_positions:
                pieces_at_pos = context.game_state.get_piece_at(pos)
                for piece in pieces_at_pos:
                    if piece.owner == target_piece.owner and piece.id != target_piece.id:
                        return True
            return False
        
        elif condition == "until_caster_dies":
            # Check if the caster is still alive
            caster = context.game_state.get_piece(context.source_piece_id)
            return caster is None
        
        elif condition == "until_turn_end":
            # Immobilize lasts until end of current turn
            return True  # This would be handled by turn management
        
        return False
    
    def _get_adjacent_positions(self, position: Coordinate) -> List[Coordinate]:
        """
        Get all adjacent positions to a coordinate
        
        Args:
            position: Center position
            
        Returns:
            List of adjacent coordinates
        """
        adjacent = []
        directions = [(-1, -1), (-1, 0), (-1, 1), (0, -1), (0, 1), (1, -1), (1, 0), (1, 1)]
        
        for dr, dc in directions:
            new_row = position.row + dr
            new_col = position.col + dc
            
            if 0 <= new_row < 8 and 0 <= new_col < 8:
                adjacent.append(Coordinate(row=new_row, col=new_col))
        
        return adjacent
    
    def calculate_cost_modifier(self, base_cost: int, ability: Ability, context: Any) -> int:
        """
        Calculate cost modifications for immobilization
        
        Args:
            base_cost: Base ability cost
            ability: The ability being used
            context: ActionContext
            
        Returns:
            Modified cost
        """
        immobilize_data = extract_tag_data(ability, "immobilize")
        
        # Cost based on duration
        duration = immobilize_data.get("duration", 1)
        cost_per_turn = immobilize_data.get("cost_per_turn", 0)
        
        # Cost per target
        target_count = len(context.targeting.all_targets)
        cost_per_target = immobilize_data.get("cost_per_target", 0)
        
        total_cost = base_cost + (duration * cost_per_turn) + (target_count * cost_per_target)
        
        return total_cost
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        """
        Get targeting requirements for immobilize abilities
        
        Args:
            ability: The ability being analyzed
            
        Returns:
            Dictionary with targeting requirements
        """
        immobilize_data = extract_tag_data(ability, "immobilize")
        
        return {
            "requires_target": True,
            "target_type": "piece",
            "min_targets": 1,
            "max_targets": immobilize_data.get("max_targets", 1),
            "target_must_be_occupied": True,
            "range_required": True,
            "line_of_sight": immobilize_data.get("requires_los", True),
            "target_filter": "any"  # Can target any piece unless restricted
        }
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> tuple[bool, str]:
        """
        Validate prerequisites for immobilize tag
        
        Args:
            ability: The ability being validated
            context: ActionContext
            
        Returns:
            (is_valid, error_message)
        """
        # Call parent validation first
        is_valid, message = super().validate_prerequisites(ability, context)
        if not is_valid:
            return is_valid, message
        
        # Check immobilize-specific prerequisites
        immobilize_data = extract_tag_data(ability, "immobilize")
        
        # Check if there are valid targets
        immobilize_targets = self._find_immobilize_targets(
            context, 
            immobilize_data.get("target_list", [])
        )
        
        if not immobilize_targets:
            return False, "No valid immobilization targets found"
        
        # Check if caster can immobilize (some pieces might be immune to casting immobilize)
        source_piece = context.game_state.get_piece(context.source_piece_id)
        if source_piece and hasattr(source_piece, 'status_effects'):
            for effect in source_piece.status_effects:
                if effect.name in ["silenced", "disabled"]:
                    return False, "Caster is silenced and cannot immobilize"
        
        return True, "Prerequisites satisfied"
