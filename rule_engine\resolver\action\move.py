#!/usr/bin/env python3
"""
Move Tag Resolver for Adventure Chess Rule Engine
Handles movement actions with advanced validation and modifiers
"""

from typing import List, Dict, Any
import logging

from schemas import Ability
from schemas.base import Coordinate
from ..base import BaseTagResolver, TagCategory, TagEffect, extract_tag_data


class MoveTagResolver(BaseTagResolver):
    """
    Resolver for the 'move' tag
    Handles piece movement with distance, direction, and modifier support
    """
    
    def _get_tag_name(self) -> str:
        return "move"
    
    def _get_category(self) -> TagCategory:
        return TagCategory.ACTION
    
    def _get_conflicts(self) -> List[str]:
        return ["immobilize", "teleport"]  # Movement conflicts with immobilization
    
    def process(self, ability: Ability, context: Any) -> List[TagEffect]:
        """
        Process move tag and return movement effects
        
        Args:
            ability: The ability containing the move tag
            context: ActionContext with game state and action details
            
        Returns:
            List of TagEffect objects for movement
        """
        effects = []
        
        # Extract move-specific data
        move_data = extract_tag_data(ability, "move")
        
        # Get movement parameters
        move_distance = move_data.get("distance", 1)
        move_direction = move_data.get("direction")
        force_movement = move_data.get("force", False)
        
        # Validate movement is possible
        if not self._can_move(context, force_movement):
            return effects
        
        # Create movement effect
        movement_effect = TagEffect(
            effect_type="movement",
            priority=100,  # High priority for movement
            immediate=True,
            parameters={
                "action": "move",
                "distance": move_distance,
                "direction": move_direction,
                "force": force_movement,
                "source_position": context.source_position,
                "target_position": context.targeting.primary_target,
                "piece_id": context.source_piece_id
            }
        )
        
        effects.append(movement_effect)
        
        # Add secondary effects if any
        if move_data.get("trigger_on_move"):
            trigger_effect = TagEffect(
                effect_type="trigger",
                priority=50,
                immediate=False,
                parameters={
                    "trigger_type": "on_move",
                    "piece_id": context.source_piece_id,
                    "abilities": move_data.get("trigger_abilities", [])
                }
            )
            effects.append(trigger_effect)
        
        return effects
    
    def _can_move(self, context: Any, force_movement: bool = False) -> bool:
        """
        Check if piece can move
        
        Args:
            context: ActionContext
            force_movement: Whether this is forced movement (ignores some restrictions)
            
        Returns:
            True if movement is allowed
        """
        # Get source piece
        source_piece = context.game_state.get_piece(context.source_piece_id)
        if not source_piece:
            return False
        
        # Check for immobilization (unless forced)
        if not force_movement and hasattr(source_piece, 'turns_immobilized'):
            if source_piece.turns_immobilized > 0:
                return False
        
        # Check for commitment (unless forced)
        if not force_movement and hasattr(source_piece, 'is_committed'):
            if source_piece.is_committed:
                return False
        
        # Check if target position is valid
        if not context.targeting.primary_target:
            return False
        
        # Validate target position is within movement range
        return self._validate_movement_range(context, source_piece)
    
    def _validate_movement_range(self, context: Any, source_piece: Any) -> bool:
        """
        Validate that target is within movement range
        
        Args:
            context: ActionContext
            source_piece: The piece attempting to move
            
        Returns:
            True if target is within range
        """
        if not context.targeting.primary_target or not source_piece.position:
            return False
        
        # Calculate distance
        target = context.targeting.primary_target
        source = source_piece.position
        
        distance = max(abs(target.row - source.row), abs(target.col - source.col))
        
        # Get movement range (this would integrate with movement system)
        # For now, use a default range
        max_range = context.get_modifier("movement_range", 1)
        
        return distance <= max_range
    
    def calculate_cost_modifier(self, base_cost: int, ability: Ability, context: Any) -> int:
        """
        Calculate cost modifications for movement
        
        Args:
            base_cost: Base ability cost
            ability: The ability being used
            context: ActionContext
            
        Returns:
            Modified cost
        """
        move_data = extract_tag_data(ability, "move")
        
        # Check for no turn cost
        if "noTurnCost" in ability.tags:
            return 0
        
        # Distance-based cost
        if move_data.get("cost_per_distance"):
            if context.targeting.primary_target and context.source_position:
                target = context.targeting.primary_target
                source = context.source_position
                distance = max(abs(target.row - source.row), abs(target.col - source.col))
                return base_cost + (distance * move_data.get("cost_per_distance", 0))
        
        return base_cost
    
    def get_targeting_requirements(self, ability: Ability) -> Dict[str, Any]:
        """
        Get targeting requirements for move abilities
        
        Args:
            ability: The ability being analyzed
            
        Returns:
            Dictionary with targeting requirements
        """
        move_data = extract_tag_data(ability, "move")
        
        return {
            "requires_target": True,
            "target_type": "position",
            "min_targets": 1,
            "max_targets": 1,
            "target_must_be_empty": not move_data.get("allow_occupied", False),
            "range_required": True,
            "line_of_sight": move_data.get("requires_los", False)
        }
    
    def get_range_modifier(self, base_range: int, ability: Ability, context: Any) -> int:
        """
        Get range modifications for movement
        
        Args:
            base_range: Base movement range
            ability: The ability being used
            context: ActionContext
            
        Returns:
            Modified range
        """
        move_data = extract_tag_data(ability, "move")
        
        # Apply range modifiers
        range_bonus = move_data.get("range_bonus", 0)
        range_multiplier = move_data.get("range_multiplier", 1.0)
        
        modified_range = int((base_range + range_bonus) * range_multiplier)
        
        # Apply context modifiers
        if context and hasattr(context, 'range_modifiers'):
            for modifier_name, modifier_value in context.range_modifiers.items():
                if isinstance(modifier_value, int):
                    modified_range += modifier_value
                elif isinstance(modifier_value, float):
                    modified_range = int(modified_range * modifier_value)
        
        return max(0, modified_range)
    
    def validate_prerequisites(self, ability: Ability, context: Any) -> tuple[bool, str]:
        """
        Validate prerequisites for move tag
        
        Args:
            ability: The ability being validated
            context: ActionContext
            
        Returns:
            (is_valid, error_message)
        """
        # Call parent validation first
        is_valid, message = super().validate_prerequisites(ability, context)
        if not is_valid:
            return is_valid, message
        
        # Check move-specific prerequisites
        move_data = extract_tag_data(ability, "move")
        
        # Check if piece has moved this turn (if restricted)
        if move_data.get("once_per_turn", False):
            source_piece = context.game_state.get_piece(context.source_piece_id)
            if source_piece and hasattr(source_piece, 'has_moved_this_turn'):
                if source_piece.has_moved_this_turn:
                    return False, "Piece has already moved this turn"
        
        # Check starting position requirement
        if "requiresStartingPosition" in ability.tags:
            source_piece = context.game_state.get_piece(context.source_piece_id)
            if source_piece and hasattr(source_piece, 'starting_position'):
                if (source_piece.position.row != source_piece.starting_position.row or 
                    source_piece.position.col != source_piece.starting_position.col):
                    return False, "Ability requires piece to be at starting position"
        
        return True, "Prerequisites satisfied"
