#!/usr/bin/env python3
"""
Context Management for Adventure Chess Rule Engine
Provides contextual information for action processing and tag resolution
"""

from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass, field
import uuid
from datetime import datetime

# Import from schemas for data structures
from schemas.base import Coordinate
from core.tester_engine import PieceColor


@dataclass
class TargetingContext:
    """Context for targeting validation and processing"""
    primary_target: Optional[Coordinate] = None
    secondary_targets: List[Coordinate] = field(default_factory=list)
    all_targets: List[Coordinate] = field(default_factory=list)
    
    # Targeting constraints
    max_targets: int = 1
    min_targets: int = 0
    target_types: Set[str] = field(default_factory=set)  # "piece", "empty", "obstacle"
    
    # Range and area information
    effective_range: int = 1
    area_of_effect: List[Coordinate] = field(default_factory=list)
    line_of_sight_required: bool = False
    
    # Filtering
    friendly_only: bool = False
    enemy_only: bool = False
    exclude_self: bool = True
    
    def add_target(self, target: Coordinate):
        """Add a target to the context"""
        if target not in self.all_targets:
            self.all_targets.append(target)
            if not self.primary_target:
                self.primary_target = target
            else:
                self.secondary_targets.append(target)
    
    def is_valid_target_count(self) -> bool:
        """Check if current target count is valid"""
        count = len(self.all_targets)
        return self.min_targets <= count <= self.max_targets


@dataclass
class CostContext:
    """Context for cost calculation and validation"""
    base_cost: int = 0
    modified_cost: int = 0
    cost_modifiers: Dict[str, float] = field(default_factory=dict)
    
    # Resource tracking
    available_points: int = 0
    required_points: int = 0
    
    # Cost breakdown
    ability_cost: int = 0
    target_cost: int = 0  # Cost per target
    range_cost: int = 0   # Cost for extended range
    modifier_cost: int = 0 # Cost from modifiers
    
    def calculate_total_cost(self) -> int:
        """Calculate total cost with all modifiers"""
        total = self.base_cost
        
        # Apply percentage modifiers
        for modifier_name, modifier_value in self.cost_modifiers.items():
            if isinstance(modifier_value, float) and 0 < modifier_value <= 2.0:
                total = int(total * modifier_value)
        
        # Add flat modifiers
        total += self.target_cost + self.range_cost + self.modifier_cost
        
        self.modified_cost = max(0, total)  # Cost cannot be negative
        return self.modified_cost
    
    def can_afford(self) -> bool:
        """Check if the cost can be afforded"""
        return self.available_points >= self.calculate_total_cost()


@dataclass
class EffectContext:
    """Context for effect processing and application"""
    effect_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    effect_type: str = ""
    priority: int = 0
    
    # Timing
    immediate: bool = True
    delay_turns: int = 0
    duration: int = -1  # -1 for permanent
    
    # Scope
    affects_caster: bool = False
    affects_targets: bool = True
    affects_area: bool = False
    
    # State tracking
    applied: bool = False
    can_stack: bool = False
    replaces_similar: bool = True
    
    # Effect parameters
    parameters: Dict[str, Any] = field(default_factory=dict)
    
    def is_ready_to_apply(self, current_turn: int, created_turn: int) -> bool:
        """Check if effect is ready to be applied"""
        if not self.immediate:
            return (current_turn - created_turn) >= self.delay_turns
        return True


@dataclass
class ValidationContext:
    """Context for validation checks"""
    validation_level: str = "standard"  # "permissive", "standard", "strict"
    
    # Validation results
    is_valid: bool = True
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # Validation flags
    skip_cost_validation: bool = False
    skip_range_validation: bool = False
    skip_target_validation: bool = False
    allow_invalid_targets: bool = False
    
    # Context-specific validations
    require_line_of_sight: bool = True
    require_adjacency: bool = False
    require_starting_position: bool = False
    
    def add_error(self, message: str):
        """Add validation error"""
        self.errors.append(message)
        self.is_valid = False
    
    def add_warning(self, message: str):
        """Add validation warning"""
        self.warnings.append(message)


@dataclass
class ActionContext:
    """
    Complete context for action processing
    Combines all context types for comprehensive action handling
    """
    # Core context
    context_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Game state references
    game_state: Any = None  # EnhancedGameState
    action_request: Any = None  # ActionRequest
    
    # Player and turn information
    current_player: PieceColor = PieceColor.WHITE
    turn_counter: int = 1
    phase: str = "main"
    
    # Processing flags
    is_simulation: bool = False
    is_preview: bool = False
    is_reaction: bool = False
    
    # Source information
    source_piece_id: str = ""
    source_position: Optional[Coordinate] = None
    ability_name: str = ""
    
    # Context components
    targeting: TargetingContext = field(default_factory=TargetingContext)
    cost: CostContext = field(default_factory=CostContext)
    effects: List[EffectContext] = field(default_factory=list)
    validation: ValidationContext = field(default_factory=ValidationContext)
    
    # Additional context data
    modifiers: Dict[str, Any] = field(default_factory=dict)
    temporary_data: Dict[str, Any] = field(default_factory=dict)
    
    # Chain tracking (for reactions and complex interactions)
    parent_context_id: Optional[str] = None
    chain_depth: int = 0
    max_chain_depth: int = 10
    
    def create_child_context(self, action_type: str = "reaction") -> 'ActionContext':
        """Create a child context for chained actions"""
        child = ActionContext(
            game_state=self.game_state,
            current_player=self.current_player,
            turn_counter=self.turn_counter,
            phase=self.phase,
            is_simulation=self.is_simulation,
            is_reaction=(action_type == "reaction"),
            parent_context_id=self.context_id,
            chain_depth=self.chain_depth + 1
        )
        
        # Copy relevant modifiers
        child.modifiers = self.modifiers.copy()
        
        return child
    
    def can_chain(self) -> bool:
        """Check if further chaining is allowed"""
        return self.chain_depth < self.max_chain_depth
    
    def add_effect(self, effect: EffectContext):
        """Add an effect to this context"""
        self.effects.append(effect)
    
    def get_effects_by_type(self, effect_type: str) -> List[EffectContext]:
        """Get all effects of a specific type"""
        return [effect for effect in self.effects if effect.effect_type == effect_type]
    
    def has_modifier(self, modifier_name: str) -> bool:
        """Check if a specific modifier is present"""
        return modifier_name in self.modifiers
    
    def get_modifier(self, modifier_name: str, default: Any = None) -> Any:
        """Get a modifier value"""
        return self.modifiers.get(modifier_name, default)
    
    def set_modifier(self, modifier_name: str, value: Any):
        """Set a modifier value"""
        self.modifiers[modifier_name] = value
    
    def apply_cost_modifier(self, modifier_name: str, modifier_value: float):
        """Apply a cost modifier"""
        self.cost.cost_modifiers[modifier_name] = modifier_value
    
    def apply_range_modifier(self, range_bonus: int):
        """Apply a range modifier"""
        self.targeting.effective_range += range_bonus
    
    def set_targeting_constraint(self, constraint_name: str, value: Any):
        """Set a targeting constraint"""
        if constraint_name == "friendly_only":
            self.targeting.friendly_only = bool(value)
        elif constraint_name == "enemy_only":
            self.targeting.enemy_only = bool(value)
        elif constraint_name == "max_targets":
            self.targeting.max_targets = int(value)
        elif constraint_name == "min_targets":
            self.targeting.min_targets = int(value)
        elif constraint_name == "line_of_sight":
            self.targeting.line_of_sight_required = bool(value)
    
    def validate_context(self) -> bool:
        """Validate the entire context"""
        # Validate targeting
        if not self.targeting.is_valid_target_count():
            self.validation.add_error(f"Invalid target count: {len(self.targeting.all_targets)}")
        
        # Validate cost
        if not self.cost.can_afford():
            self.validation.add_error(f"Insufficient points: {self.cost.available_points}/{self.cost.calculate_total_cost()}")
        
        # Validate chain depth
        if not self.can_chain():
            self.validation.add_error(f"Maximum chain depth exceeded: {self.chain_depth}")
        
        return self.validation.is_valid
    
    def to_summary(self) -> Dict[str, Any]:
        """Create a summary of this context for logging"""
        return {
            "context_id": self.context_id,
            "source_piece": self.source_piece_id,
            "ability": self.ability_name,
            "targets": len(self.targeting.all_targets),
            "cost": self.cost.calculate_total_cost(),
            "effects": len(self.effects),
            "is_valid": self.validation.is_valid,
            "chain_depth": self.chain_depth
        }


def create_action_context(game_state: Any, action_request: Any) -> ActionContext:
    """
    Factory function to create a properly initialized ActionContext
    """
    context = ActionContext(
        game_state=game_state,
        action_request=action_request,
        current_player=game_state.current_player,
        turn_counter=game_state.turn_counter,
        phase=getattr(game_state, 'phase', 'main')
    )
    
    # Initialize from action request
    if hasattr(action_request, 'piece_id'):
        context.source_piece_id = action_request.piece_id
    
    if hasattr(action_request, 'ability_name'):
        context.ability_name = action_request.ability_name or ""
    
    if hasattr(action_request, 'target_position') and action_request.target_position:
        context.targeting.add_target(action_request.target_position)
    
    # Get source piece information
    if context.source_piece_id and game_state:
        source_piece = game_state.get_piece(context.source_piece_id)
        if source_piece:
            context.source_position = source_piece.position
            context.cost.available_points = source_piece.current_points
    
    return context
